import React, { useState, useRef, useEffect } from 'react'
import { ChevronDown, X, Search, Check } from 'lucide-react'

interface Option {
  value: string
  label: string
}

interface MultiSelectDropdownProps {
  options: Option[]
  value: string[]
  onChange: (values: string[]) => void
  placeholder?: string
  searchPlaceholder?: string
  className?: string
  disabled?: boolean
  maxDisplayItems?: number
}

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  options,
  value,
  onChange,
  placeholder = "Select options...",
  searchPlaceholder = "Search options...",
  className = "",
  disabled = false,
  maxDisplayItems = 2
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isOpen])

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Get selected option labels
  const selectedOptions = options.filter(option => value.includes(option.value))

  // Handle option selection
  const handleOptionToggle = (optionValue: string) => {
    if (value.includes(optionValue)) {
      onChange(value.filter(v => v !== optionValue))
    } else {
      onChange([...value, optionValue])
    }
  }

  // Remove selected option
  const handleRemoveOption = (optionValue: string, event: React.MouseEvent) => {
    event.stopPropagation()
    onChange(value.filter(v => v !== optionValue))
  }

  // Clear all selections
  const handleClearAll = (event: React.MouseEvent) => {
    event.stopPropagation()
    onChange([])
  }

  // Get display text for trigger
  const getDisplayText = () => {
    if (selectedOptions.length === 0) {
      return placeholder
    }
    
    if (selectedOptions.length <= maxDisplayItems) {
      return selectedOptions.map(option => option.label).join(', ')
    }
    
    return `${selectedOptions.slice(0, maxDisplayItems).map(option => option.label).join(', ')} +${selectedOptions.length - maxDisplayItems} more`
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm cursor-pointer focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400'}`}
        aria-expanded={isOpen}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center flex-1 min-w-0">
            {selectedOptions.length > 0 && selectedOptions.length <= maxDisplayItems ? (
              <div className="flex items-center gap-1 flex-wrap">
                {selectedOptions.map(option => (
                  <span
                    key={option.value}
                    className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {option.label}
                    <button
                      onClick={(e) => handleRemoveOption(option.value, e)}
                      className="ml-1 hover:text-blue-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            ) : (
              <span className={`truncate ${selectedOptions.length === 0 ? 'text-gray-500' : 'text-gray-900'}`}>
                {getDisplayText()}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {selectedOptions.length > 0 && (
              <button
                onClick={handleClearAll}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
            <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
          {/* Search Input */}
          <div className="p-2 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Options List */}
          <div className="max-h-60 overflow-y-auto">
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500">
                No options found
              </div>
            ) : (
              filteredOptions.map(option => {
                const isSelected = value.includes(option.value)
                return (
                  <button
                    key={option.value}
                    onClick={() => handleOptionToggle(option.value)}
                    className={`px-3 py-2 text-sm cursor-pointer hover:bg-gray-50 transition-colors duration-150 w-full text-left flex items-center justify-between ${
                      isSelected ? 'bg-blue-50 text-blue-900' : 'text-gray-900'
                    }`}
                  >
                    <span>{option.label}</span>
                    {isSelected && <Check className="h-4 w-4 text-blue-600" />}
                  </button>
                )
              })
            )}
          </div>

          {/* Footer with selection count */}
          {selectedOptions.length > 0 && (
            <div className="p-2 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span>{selectedOptions.length} selected</span>
                <button
                  onClick={handleClearAll}
                  className="text-blue-600 hover:text-blue-800"
                >
                  Clear all
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default MultiSelectDropdown

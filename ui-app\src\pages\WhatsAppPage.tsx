import React, { useState, useEffect, useRef } from 'react'
import { whatsappApi } from '../services/whatsappApi'
import {
  Search,
  MoreVertical,
  Phone,
  Video,
  Paperclip,
  Smile,
  Send,
  Settings,
  Plus,
  MessageCircle,
  Users,
  Archive,
  Star,
  Trash2,
  Volume2,
  VolumeX,
  Check,
  CheckCheck,
  Clock,
  Image,
  FileText,
  Mic,
  Calendar,
  UserPlus,
  X,
  ArrowLeft,
  Info,
  CheckSquare,
  BellOff,
  Timer,
  Heart,
  XCircle,
  Minus,
  LogOut,
  Filter,
  Camera,
  Play
} from 'lucide-react'
import SectionHeader from '../components/SectionHeader'

interface WhatsAppAccount {
  id: string
  name: string
  phone: string
  profilePicture?: string
  isActive: boolean
  status: 'connected' | 'disconnected' | 'connecting'
  lastSeen?: string
  userId: string
}

interface Contact {
  id: string
  name: string
  phone: string
  profilePicture?: string
  lastSeen?: string
  isOnline: boolean
  status?: string
}

interface Message {
  id: string
  contactId: string
  content: string
  timestamp: Date
  type: 'text' | 'image' | 'document' | 'audio' | 'video' | 'contact' | 'event'
  isFromMe: boolean
  status: 'sent' | 'delivered' | 'read'
  attachmentUrl?: string
  attachmentName?: string
}

interface Conversation {
  id: string
  contact: Contact
  lastMessage: Message
  unreadCount: number
  isPinned: boolean
  isArchived: boolean
  isMuted: boolean
  isStarred: boolean
}

interface BroadcastList {
  id: string
  name: string
  contacts: Contact[]
  createdAt: Date
}

const WhatsAppPage = () => {
  const [accounts, setAccounts] = useState<WhatsAppAccount[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const [selectedAccount, setSelectedAccount] = useState<WhatsAppAccount | null>(null)
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [messageSearchQuery, setMessageSearchQuery] = useState('')
  const [showAccountSettings, setShowAccountSettings] = useState(false)
  const [showNewChat, setShowNewChat] = useState(false)
  const [showAddAccount, setShowAddAccount] = useState(false)
  const [showAttachments, setShowAttachments] = useState(false)
  const [showChatMenu, setShowChatMenu] = useState(false)
  const [showMainMenu, setShowMainMenu] = useState(false)
  const [showBroadcast, setShowBroadcast] = useState(false)
  const [activeFilter, setActiveFilter] = useState<'all' | 'unread' | 'favorites' | 'groups' | 'archived'>('all')
  const [contacts, setContacts] = useState<Contact[]>([])
  const [broadcastLists, setBroadcastLists] = useState<BroadcastList[]>([])
  const [newAccountData, setNewAccountData] = useState({ name: '', phone: '' })

  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Load accounts and conversations
  useEffect(() => {
    loadAccounts()
    loadContacts()
  }, [])

  useEffect(() => {
    if (selectedAccount) {
      loadConversations()
      loadContacts()
    }
  }, [selectedAccount, searchQuery])

  const loadAccounts = async () => {
    try {
      setIsLoading(true)
      const response = await whatsappApi.getAccounts()

      if (response.success && response.data) {
        const transformedAccounts: WhatsAppAccount[] = response.data.map(acc => ({
          id: acc.id,
          name: acc.name,
          phone: acc.phone,
          isActive: acc.is_active,
          status: acc.status,
          lastSeen: acc.last_seen,
          userId: acc.created_by
        }))

        setAccounts(transformedAccounts)
        if (transformedAccounts.length > 0) {
          setSelectedAccount(transformedAccounts[0])
        }
      } else {
        // Fallback to mock data if API fails
        const mockAccounts: WhatsAppAccount[] = [
          {
            id: '1',
            name: 'Business Account',
            phone: '+**********',
            isActive: true,
            status: 'connected',
            lastSeen: '2 minutes ago',
            userId: 'user1'
          },
          {
            id: '2',
            name: 'Personal Account',
            phone: '+**********',
            isActive: false,
            status: 'disconnected',
            lastSeen: '1 hour ago',
            userId: 'user1'
          }
        ]
        setAccounts(mockAccounts)
        if (mockAccounts.length > 0) {
          setSelectedAccount(mockAccounts[0])
        }
      }
    } catch (error) {
      console.error('Error loading accounts:', error)
      // Fallback to mock data on error
      const mockAccounts: WhatsAppAccount[] = [
        {
          id: '1',
          name: 'Business Account',
          phone: '+**********',
          isActive: true,
          status: 'connected',
          lastSeen: '2 minutes ago',
          userId: 'user1'
        }
      ]
      setAccounts(mockAccounts)
      if (mockAccounts.length > 0) {
        setSelectedAccount(mockAccounts[0])
      }
    } finally {
      setIsLoading(false)
    }
  }

  const loadContacts = async () => {
    try {
      if (!selectedAccount) return

      const response = await whatsappApi.getContacts(selectedAccount.id)

      if (response.success && response.data) {
        const transformedContacts: Contact[] = response.data.map(contact => ({
          id: contact.id,
          name: contact.name || contact.phone,
          phone: contact.phone,
          isOnline: contact.is_online || false,
          lastSeen: contact.last_seen,
          status: contact.custom_fields?.status || 'Hey there! I am using WhatsApp.'
        }))

        setContacts(transformedContacts)
      } else {
        // Fallback to mock data
        const mockContacts: Contact[] = [
          {
            id: '1',
            name: 'John Doe',
            phone: '+**********',
            isOnline: true,
            status: 'Available'
          },
          {
            id: '2',
            name: 'Jane Smith',
            phone: '+**********',
            isOnline: false,
            lastSeen: '1 hour ago',
            status: 'Hey there! I am using WhatsApp.'
          },
          {
            id: '3',
            name: 'Mike Johnson',
            phone: '+1234567893',
            isOnline: true,
            status: 'Busy'
          }
        ]
        setContacts(mockContacts)
      }
    } catch (error) {
      console.error('Error loading contacts:', error)
      // Fallback to mock data on error
      const mockContacts: Contact[] = [
        {
          id: '1',
          name: 'John Doe',
          phone: '+**********',
          isOnline: true,
          status: 'Available'
        }
      ]
      setContacts(mockContacts)
    }
  }

  const loadConversations = async () => {
    if (!selectedAccount) return

    try {
      const response = await whatsappApi.getConversations(selectedAccount.id, { search: searchQuery })

      if (response.success && response.data) {
        const transformedConversations: Conversation[] = response.data.map(conv => ({
          id: conv.id,
          contact: {
            id: conv.contact_id,
            name: conv.contact_name || conv.contact_phone,
            phone: conv.contact_phone,
            isOnline: conv.is_online || false,
            lastSeen: conv.last_seen
          },
          lastMessage: {
            id: conv.last_message_id || '1',
            contactId: conv.contact_id,
            content: conv.last_message_content || 'No messages yet',
            timestamp: new Date(conv.last_message_time || Date.now()),
            type: 'text',
            isFromMe: conv.last_message_from_me || false,
            status: 'delivered'
          },
          unreadCount: conv.unread_count || 0,
          isPinned: conv.is_pinned || false,
          isArchived: conv.is_archived || false,
          isMuted: conv.is_muted || false,
          isStarred: conv.is_starred || false
        }))

        setConversations(transformedConversations)
      } else {
        // Fallback to mock data
        const mockConversations: Conversation[] = [
          {
            id: '1',
            contact: {
              id: '1',
              name: 'John Doe',
              phone: '+**********',
              isOnline: true
            },
            lastMessage: {
              id: '1',
              contactId: '1',
              content: 'Hey, how are you?',
              timestamp: new Date(Date.now() - 5 * 60 * 1000),
              type: 'text',
              isFromMe: false,
              status: 'delivered'
            },
            unreadCount: 2,
            isPinned: false,
            isArchived: false,
            isMuted: false,
            isStarred: false
          },
          {
            id: '2',
            contact: {
              id: '2',
              name: 'Jane Smith',
              phone: '+**********',
              isOnline: false,
              lastSeen: '1 hour ago'
            },
            lastMessage: {
              id: '2',
              contactId: '2',
              content: 'Thanks for the information!',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
              type: 'text',
              isFromMe: true,
              status: 'read'
            },
            unreadCount: 0,
            isPinned: true,
            isArchived: false,
            isMuted: false,
            isStarred: true
          }
        ]
        setConversations(mockConversations)
      }
    } catch (error) {
      console.error('Error loading conversations:', error)
      // Fallback to mock data on error
      const mockConversations: Conversation[] = [
        {
          id: '1',
          contact: {
            id: '1',
            name: 'John Doe',
            phone: '+**********',
            isOnline: true
          },
          lastMessage: {
            id: '1',
            contactId: '1',
            content: 'Hey, how are you?',
            timestamp: new Date(Date.now() - 5 * 60 * 1000),
            type: 'text',
            isFromMe: false,
            status: 'delivered'
          },
          unreadCount: 2,
          isPinned: false,
          isArchived: false,
          isMuted: false,
          isStarred: false
        }
      ]
      setConversations(mockConversations)
    }
  }

  // Load messages for selected conversation
  useEffect(() => {
    if (selectedConversation) {
      loadMessages(selectedConversation.id)
    }
  }, [selectedConversation, messageSearchQuery])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || !selectedAccount) return

    try {
      const response = await whatsappApi.sendMessage(selectedAccount.id, {
        contactPhone: selectedConversation.contact.phone,
        message: newMessage,
        messageType: 'text'
      })

      if (response.success) {
        const message: Message = {
          id: response.data?.messageId || Date.now().toString(),
          contactId: selectedConversation.contact.id,
          content: newMessage,
          timestamp: new Date(),
          type: 'text',
          isFromMe: true,
          status: 'sent'
        }

        setMessages(prev => [...prev, message])
        setNewMessage('')
        setShowAttachments(false)
      } else {
        console.error('Failed to send message:', response.error)
        // Still add to local state for demo purposes
        const message: Message = {
          id: Date.now().toString(),
          contactId: selectedConversation.contact.id,
          content: newMessage,
          timestamp: new Date(),
          type: 'text',
          isFromMe: true,
          status: 'sent'
        }

        setMessages(prev => [...prev, message])
        setNewMessage('')
        setShowAttachments(false)
      }
    } catch (error) {
      console.error('Error sending message:', error)
      // Fallback to local state update
      const message: Message = {
        id: Date.now().toString(),
        contactId: selectedConversation.contact.id,
        content: newMessage,
        timestamp: new Date(),
        type: 'text',
        isFromMe: true,
        status: 'sent'
      }

      setMessages(prev => [...prev, message])
      setNewMessage('')
      setShowAttachments(false)
    }
  }

  // CRUD Operations
  const handleAddAccount = async () => {
    if (!newAccountData.name.trim() || !newAccountData.phone.trim()) return

    try {
      const response = await whatsappApi.createAccount({
        name: newAccountData.name,
        phone: newAccountData.phone
      })

      if (response.success && response.data) {
        const newAccount: WhatsAppAccount = {
          id: response.data.id,
          name: response.data.name,
          phone: response.data.phone,
          isActive: response.data.is_active,
          status: response.data.status,
          userId: response.data.created_by
        }

        setAccounts(prev => [...prev, newAccount])
        setNewAccountData({ name: '', phone: '' })
        setShowAddAccount(false)
      } else {
        console.error('Failed to create account:', response.error)
        // Still add to local state for demo purposes
        const newAccount: WhatsAppAccount = {
          id: Date.now().toString(),
          name: newAccountData.name,
          phone: newAccountData.phone,
          isActive: false,
          status: 'disconnected',
          userId: 'user1'
        }

        setAccounts(prev => [...prev, newAccount])
        setNewAccountData({ name: '', phone: '' })
        setShowAddAccount(false)
      }
    } catch (error) {
      console.error('Error creating account:', error)
    }
  }

  const handleDeleteAccount = async (accountId: string) => {
    try {
      const response = await whatsappApi.deleteAccount(accountId)

      if (response.success) {
        setAccounts(prev => prev.filter(acc => acc.id !== accountId))
        if (selectedAccount?.id === accountId) {
          const remainingAccounts = accounts.filter(acc => acc.id !== accountId)
          setSelectedAccount(remainingAccounts[0] || null)
        }
      } else {
        console.error('Failed to delete account:', response.error)
        // Still remove from local state for demo purposes
        setAccounts(prev => prev.filter(acc => acc.id !== accountId))
        if (selectedAccount?.id === accountId) {
          const remainingAccounts = accounts.filter(acc => acc.id !== accountId)
          setSelectedAccount(remainingAccounts[0] || null)
        }
      }
    } catch (error) {
      console.error('Error deleting account:', error)
    }
  }

  const handleToggleArchive = async (conversationId: string) => {
    try {
      const conversation = conversations.find(conv => conv.id === conversationId)
      if (!conversation) return

      const response = await whatsappApi.updateConversation(conversationId, {
        isArchived: !conversation.isArchived
      })

      if (response.success) {
        setConversations(prev => prev.map(conv =>
          conv.id === conversationId
            ? { ...conv, isArchived: !conv.isArchived }
            : conv
        ))
      } else {
        console.error('Failed to update conversation:', response.error)
        // Still update local state for demo purposes
        setConversations(prev => prev.map(conv =>
          conv.id === conversationId
            ? { ...conv, isArchived: !conv.isArchived }
            : conv
        ))
      }
    } catch (error) {
      console.error('Error updating conversation:', error)
    }
  }

  const handleToggleMute = async (conversationId: string) => {
    try {
      const conversation = conversations.find(conv => conv.id === conversationId)
      if (!conversation) return

      const response = await whatsappApi.updateConversation(conversationId, {
        isMuted: !conversation.isMuted
      })

      if (response.success) {
        setConversations(prev => prev.map(conv =>
          conv.id === conversationId
            ? { ...conv, isMuted: !conv.isMuted }
            : conv
        ))
      } else {
        console.error('Failed to update conversation:', response.error)
        // Still update local state for demo purposes
        setConversations(prev => prev.map(conv =>
          conv.id === conversationId
            ? { ...conv, isMuted: !conv.isMuted }
            : conv
        ))
      }
    } catch (error) {
      console.error('Error updating conversation:', error)
    }
  }

  const handleToggleStar = async (conversationId: string) => {
    try {
      const conversation = conversations.find(conv => conv.id === conversationId)
      if (!conversation) return

      const response = await whatsappApi.updateConversation(conversationId, {
        isPinned: !conversation.isStarred
      })

      if (response.success) {
        setConversations(prev => prev.map(conv =>
          conv.id === conversationId
            ? { ...conv, isStarred: !conv.isStarred }
            : conv
        ))
      } else {
        console.error('Failed to update conversation:', response.error)
        // Still update local state for demo purposes
        setConversations(prev => prev.map(conv =>
          conv.id === conversationId
            ? { ...conv, isStarred: !conv.isStarred }
            : conv
        ))
      }
    } catch (error) {
      console.error('Error updating conversation:', error)
    }
  }

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      const response = await whatsappApi.deleteConversation(conversationId)

      if (response.success) {
        setConversations(prev => prev.filter(conv => conv.id !== conversationId))
        if (selectedConversation?.id === conversationId) {
          setSelectedConversation(null)
        }
      } else {
        console.error('Failed to delete conversation:', response.error)
        // Still remove from local state for demo purposes
        setConversations(prev => prev.filter(conv => conv.id !== conversationId))
        if (selectedConversation?.id === conversationId) {
          setSelectedConversation(null)
        }
      }
    } catch (error) {
      console.error('Error deleting conversation:', error)
    }
  }

  const handleSendAttachment = async (type: string) => {
    if (!selectedConversation || !selectedAccount) return

    try {
      const response = await whatsappApi.sendMessage(selectedAccount.id, {
        contactPhone: selectedConversation.contact.phone,
        message: `${type} attachment`,
        messageType: type
      })

      if (response.success) {
        const message: Message = {
          id: response.data?.messageId || Date.now().toString(),
          contactId: selectedConversation.contact.id,
          content: `${type} attachment`,
          timestamp: new Date(),
          type: type as any,
          isFromMe: true,
          status: 'sent',
          attachmentUrl: '#'
        }

        setMessages(prev => [...prev, message])
        setShowAttachments(false)
      } else {
        console.error('Failed to send attachment:', response.error)
        // Still add to local state for demo purposes
        const message: Message = {
          id: Date.now().toString(),
          contactId: selectedConversation.contact.id,
          content: `${type} attachment`,
          timestamp: new Date(),
          type: type as any,
          isFromMe: true,
          status: 'sent',
          attachmentUrl: '#'
        }

        setMessages(prev => [...prev, message])
        setShowAttachments(false)
      }
    } catch (error) {
      console.error('Error sending attachment:', error)
    }
  }

  // Load messages for selected conversation
  const loadMessages = async (conversationId: string) => {
    try {
      const response = await whatsappApi.getMessages(conversationId, { search: messageSearchQuery })

      if (response.success && response.data) {
        const transformedMessages: Message[] = response.data.map(msg => ({
          id: msg.id,
          contactId: msg.contact_id,
          content: msg.content,
          timestamp: new Date(msg.timestamp),
          type: msg.message_type,
          isFromMe: msg.is_from_me,
          status: msg.status,
          attachmentUrl: msg.media_url
        }))

        setMessages(transformedMessages)
      } else {
        // Fallback to mock messages
        const mockMessages: Message[] = [
          {
            id: '1',
            contactId: selectedConversation?.contact.id || '1',
            content: 'Hey, how are you?',
            timestamp: new Date(Date.now() - 10 * 60 * 1000),
            type: 'text',
            isFromMe: false,
            status: 'delivered'
          },
          {
            id: '2',
            contactId: selectedConversation?.contact.id || '1',
            content: 'I\'m doing great! How about you?',
            timestamp: new Date(Date.now() - 8 * 60 * 1000),
            type: 'text',
            isFromMe: true,
            status: 'read'
          }
        ]
        setMessages(mockMessages)
      }
    } catch (error) {
      console.error('Error loading messages:', error)
      // Fallback to mock messages on error
      const mockMessages: Message[] = [
        {
          id: '1',
          contactId: selectedConversation?.contact.id || '1',
          content: 'Hey, how are you?',
          timestamp: new Date(Date.now() - 10 * 60 * 1000),
          type: 'text',
          isFromMe: false,
          status: 'delivered'
        }
      ]
      setMessages(mockMessages)
    }
  }

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="h-4 w-4 text-gray-400" />
      case 'delivered':
        return <CheckCheck className="h-4 w-4 text-gray-400" />
      case 'read':
        return <CheckCheck className="h-4 w-4 text-blue-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  const filteredConversations = conversations.filter(conv => {
    const matchesSearch = conv.contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conv.contact.phone.includes(searchQuery)

    switch (activeFilter) {
      case 'unread':
        return matchesSearch && conv.unreadCount > 0
      case 'favorites':
        return matchesSearch && conv.isStarred
      case 'groups':
        return matchesSearch && conv.contact.name.includes('Group')
      case 'archived':
        return matchesSearch && conv.isArchived
      default:
        return matchesSearch && !conv.isArchived
    }
  })

  const filteredMessages = messages.filter(msg =>
    messageSearchQuery === '' ||
    msg.content.toLowerCase().includes(messageSearchQuery.toLowerCase())
  )

  return (
    <div className="h-screen bg-gray-100 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <SectionHeader
            icon={<MessageCircle className="h-7 w-7" />}
            title="WhatsApp Business"
            subtitle="Manage your WhatsApp Business accounts and conversations"
            actions={
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowBroadcast(true)}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Broadcast
                </button>
                <button
                  onClick={() => setShowAccountSettings(!showAccountSettings)}
                  className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Accounts ({accounts.length})
                </button>
                <div className="relative">
                  <button
                    onClick={() => setShowMainMenu(!showMainMenu)}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                  >
                    <MoreVertical className="h-5 w-5" />
                  </button>
                  {showMainMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                      <button
                        onClick={() => {
                          setShowNewChat(true)
                          setShowMainMenu(false)
                        }}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <Users className="h-4 w-4 mr-3" />
                        New group
                      </button>
                      <button
                        onClick={() => setShowMainMenu(false)}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <Star className="h-4 w-4 mr-3" />
                        Starred messages
                      </button>
                      <button
                        onClick={() => setShowMainMenu(false)}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <CheckSquare className="h-4 w-4 mr-3" />
                        Select chats
                      </button>
                      <hr className="my-2" />
                      <button
                        onClick={() => setShowMainMenu(false)}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <LogOut className="h-4 w-4 mr-3" />
                        Log out
                      </button>
                    </div>
                  )}
                </div>
              </div>
            }
          />
        </div>
      </div>

      {/* Account Settings Panel */}
      {showAccountSettings && (
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">WhatsApp Accounts</h3>
            <button
              onClick={() => setShowAddAccount(true)}
              className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Account
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {accounts.map(account => (
              <div key={account.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div
                    className="flex items-center cursor-pointer flex-1"
                    onClick={() => setSelectedAccount(account)}
                  >
                    <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                      <MessageCircle className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{account.name}</p>
                      <p className="text-xs text-gray-500">{account.phone}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      account.status === 'connected'
                        ? 'bg-green-100 text-green-800'
                        : account.status === 'connecting'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {account.status}
                    </div>
                    <button
                      onClick={() => handleDeleteAccount(account.id)}
                      className="p-1 text-red-400 hover:text-red-600 rounded"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Main Chat Interface */}
      <div className="flex-1 flex overflow-hidden">
        {/* Conversations Sidebar */}
        <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
          {/* Header with New Chat */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-lg font-semibold text-gray-900">WhatsApp</h2>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowNewChat(true)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                >
                  <Plus className="h-5 w-5" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                  <MoreVertical className="h-5 w-5" />
                </button>
              </div>
            </div>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search or start a new chat"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            </div>
          </div>

          {/* Filter Tabs */}
          <div className="px-4 py-2 border-b border-gray-200">
            <div className="flex space-x-1">
              {[
                { key: 'all', label: 'All' },
                { key: 'unread', label: 'Unread' },
                { key: 'favorites', label: 'Favorites' },
                { key: 'groups', label: 'Groups' }
              ].map(filter => (
                <button
                  key={filter.key}
                  onClick={() => setActiveFilter(filter.key as any)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    activeFilter === filter.key
                      ? 'bg-green-100 text-green-800'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>

            <button
              onClick={() => setActiveFilter('archived')}
              className="flex items-center mt-2 text-sm text-gray-600 hover:text-gray-800"
            >
              <Archive className="h-4 w-4 mr-2" />
              Archived
            </button>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto">
            {filteredConversations.map(conversation => (
              <div
                key={conversation.id}
                onClick={() => setSelectedConversation(conversation)}
                className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                  selectedConversation?.id === conversation.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1 min-w-0">
                    <div className="relative">
                      <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="h-6 w-6 text-gray-500" />
                      </div>
                      {conversation.contact.isOnline && (
                        <div className="absolute bottom-0 right-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></div>
                      )}
                    </div>
                    <div className="ml-3 flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {conversation.contact.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatTime(conversation.lastMessage.timestamp)}
                        </p>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <div className="flex items-center flex-1 min-w-0">
                          {conversation.isMuted && (
                            <VolumeX className="h-3 w-3 text-gray-400 mr-1 flex-shrink-0" />
                          )}
                          <p className="text-sm text-gray-600 truncate">
                            {conversation.lastMessage.isFromMe && 'You: '}
                            {conversation.lastMessage.content}
                          </p>
                        </div>
                        <div className="flex items-center space-x-1">
                          {conversation.isPinned && (
                            <Star className="h-3 w-3 text-yellow-500" />
                          )}
                          {conversation.unreadCount > 0 && (
                            <span className="bg-green-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                              {conversation.unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedConversation ? (
            <>
              {/* Chat Header */}
              <div className="bg-white border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-gray-500" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">
                        {selectedConversation.contact.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {selectedConversation.contact.isOnline 
                          ? 'Online' 
                          : `Last seen ${selectedConversation.contact.lastSeen || 'recently'}`
                        }
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search messages..."
                        value={messageSearchQuery}
                        onChange={(e) => setMessageSearchQuery(e.target.value)}
                        className="w-48 pl-10 pr-4 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                      />
                    </div>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                      <Phone className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                      <Video className="h-5 w-5" />
                    </button>
                    <div className="relative">
                      <button
                        onClick={() => setShowChatMenu(!showChatMenu)}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                      >
                        <MoreVertical className="h-5 w-5" />
                      </button>

                      {showChatMenu && (
                        <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                          <button
                            onClick={() => {
                              setShowChatMenu(false)
                            }}
                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <Info className="h-4 w-4 mr-3" />
                            Group info
                          </button>
                          <button
                            onClick={() => setShowChatMenu(false)}
                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <CheckSquare className="h-4 w-4 mr-3" />
                            Select messages
                          </button>
                          <button
                            onClick={() => {
                              handleToggleMute(selectedConversation.id)
                              setShowChatMenu(false)
                            }}
                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <BellOff className="h-4 w-4 mr-3" />
                            {selectedConversation.isMuted ? 'Unmute notifications' : 'Mute notifications'}
                          </button>
                          <button
                            onClick={() => setShowChatMenu(false)}
                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <Timer className="h-4 w-4 mr-3" />
                            Disappearing messages
                          </button>
                          <button
                            onClick={() => {
                              handleToggleStar(selectedConversation.id)
                              setShowChatMenu(false)
                            }}
                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <Heart className="h-4 w-4 mr-3" />
                            Add to favorites
                          </button>
                          <hr className="my-2" />
                          <button
                            onClick={() => {
                              handleToggleArchive(selectedConversation.id)
                              setShowChatMenu(false)
                            }}
                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <Archive className="h-4 w-4 mr-3" />
                            {selectedConversation.isArchived ? 'Unarchive chat' : 'Archive chat'}
                          </button>
                          <button
                            onClick={() => setShowChatMenu(false)}
                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <XCircle className="h-4 w-4 mr-3" />
                            Close chat
                          </button>
                          <button
                            onClick={() => setShowChatMenu(false)}
                            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          >
                            <Minus className="h-4 w-4 mr-3" />
                            Clear chat
                          </button>
                          <button
                            onClick={() => {
                              handleDeleteConversation(selectedConversation.id)
                              setShowChatMenu(false)
                            }}
                            className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center"
                          >
                            <Trash2 className="h-4 w-4 mr-3" />
                            Delete chat
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
                {filteredMessages.map(message => (
                  <div
                    key={message.id}
                    className={`flex ${message.isFromMe ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.isFromMe
                          ? 'bg-green-500 text-white'
                          : 'bg-white text-gray-900 border border-gray-200'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <div className={`flex items-center justify-end mt-1 space-x-1 ${
                        message.isFromMe ? 'text-green-100' : 'text-gray-500'
                      }`}>
                        <span className="text-xs">{formatTime(message.timestamp)}</span>
                        {message.isFromMe && getMessageStatusIcon(message.status)}
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="bg-white border-t border-gray-200 px-4 py-4">
                {showAttachments && (
                  <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-3 gap-3">
                      <button
                        onClick={() => handleSendAttachment('image')}
                        className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <Image className="h-6 w-6 text-blue-500 mb-1" />
                        <span className="text-xs text-gray-600">Photo</span>
                      </button>
                      <button
                        onClick={() => handleSendAttachment('video')}
                        className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <Play className="h-6 w-6 text-red-500 mb-1" />
                        <span className="text-xs text-gray-600">Video</span>
                      </button>
                      <button
                        onClick={() => handleSendAttachment('document')}
                        className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <FileText className="h-6 w-6 text-green-500 mb-1" />
                        <span className="text-xs text-gray-600">Document</span>
                      </button>
                      <button
                        onClick={() => handleSendAttachment('contact')}
                        className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <UserPlus className="h-6 w-6 text-purple-500 mb-1" />
                        <span className="text-xs text-gray-600">Contact</span>
                      </button>
                      <button
                        onClick={() => handleSendAttachment('audio')}
                        className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <Mic className="h-6 w-6 text-orange-500 mb-1" />
                        <span className="text-xs text-gray-600">Audio</span>
                      </button>
                      <button
                        onClick={() => handleSendAttachment('event')}
                        className="flex flex-col items-center p-3 bg-white rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <Calendar className="h-6 w-6 text-indigo-500 mb-1" />
                        <span className="text-xs text-gray-600">Event</span>
                      </button>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <button
                      onClick={() => setShowAttachments(!showAttachments)}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    >
                      <Paperclip className="h-5 w-5" />
                    </button>
                  </div>
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      placeholder="Type a message..."
                      className="w-full px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    />
                    <button className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600">
                      <Smile className="h-5 w-5" />
                    </button>
                  </div>
                  <button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    className="p-2 bg-green-500 text-white rounded-full hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    <Send className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Select a conversation
                </h3>
                <p className="text-gray-600">
                  Choose a conversation from the sidebar to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Account Modal */}
      {showAddAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Add WhatsApp Account</h3>
              <button
                onClick={() => setShowAddAccount(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Account Name
                </label>
                <input
                  type="text"
                  value={newAccountData.name}
                  onChange={(e) => setNewAccountData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g., Business Account"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="text"
                  value={newAccountData.phone}
                  onChange={(e) => setNewAccountData(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="+**********"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddAccount(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleAddAccount}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Add Account
              </button>
            </div>
          </div>
        </div>
      )}

      {/* New Chat Modal */}
      {showNewChat && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-96 max-h-96 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-gray-900">New chat</h3>
                <button
                  onClick={() => setShowNewChat(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <ArrowLeft className="h-5 w-5" />
                </button>
              </div>

              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search name or number"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>

            <div className="p-4 space-y-3">
              <button className="w-full flex items-center p-3 hover:bg-gray-50 rounded-lg">
                <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <span className="ml-3 text-sm font-medium text-gray-900">New group</span>
              </button>

              <button className="w-full flex items-center p-3 hover:bg-gray-50 rounded-lg">
                <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                  <UserPlus className="h-5 w-5 text-green-600" />
                </div>
                <span className="ml-3 text-sm font-medium text-gray-900">New contact</span>
              </button>

              <button className="w-full flex items-center p-3 hover:bg-gray-50 rounded-lg">
                <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-green-600" />
                </div>
                <span className="ml-3 text-sm font-medium text-gray-900">New community</span>
              </button>
            </div>

            <div className="p-4 border-t border-gray-200">
              <h4 className="text-sm font-medium text-gray-500 mb-3">Contacts on WhatsApp</h4>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {contacts.map(contact => (
                  <button
                    key={contact.id}
                    onClick={() => {
                      // Create new conversation with this contact
                      setShowNewChat(false)
                    }}
                    className="w-full flex items-center p-2 hover:bg-gray-50 rounded-lg"
                  >
                    <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <Users className="h-4 w-4 text-gray-500" />
                    </div>
                    <div className="ml-3 text-left">
                      <p className="text-sm font-medium text-gray-900">{contact.name}</p>
                      <p className="text-xs text-gray-500">{contact.status}</p>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Broadcast Modal */}
      {showBroadcast && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-[600px] max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">Broadcast Message</h3>
                <button
                  onClick={() => setShowBroadcast(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              <p className="text-sm text-gray-600">
                Send a message to multiple WhatsApp accounts at once
              </p>
            </div>

            <div className="p-6 space-y-6">
              {/* Account Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Select WhatsApp Accounts
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {accounts.map(account => (
                    <label key={account.id} className="flex items-center p-2 hover:bg-gray-50 rounded-lg">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <div className="ml-3 flex items-center">
                        <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                          <MessageCircle className="h-4 w-4 text-green-600" />
                        </div>
                        <div className="ml-2">
                          <p className="text-sm font-medium text-gray-900">{account.name}</p>
                          <p className="text-xs text-gray-500">{account.phone}</p>
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Contact Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Select Recipients
                </label>
                <div className="relative mb-3">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search contacts..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {contacts.map(contact => (
                    <label key={contact.id} className="flex items-center p-2 hover:bg-gray-50 rounded-lg">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <div className="ml-3 flex items-center">
                        <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <Users className="h-4 w-4 text-gray-500" />
                        </div>
                        <div className="ml-2">
                          <p className="text-sm font-medium text-gray-900">{contact.name}</p>
                          <p className="text-xs text-gray-500">{contact.phone}</p>
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Message */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message
                </label>
                <textarea
                  rows={4}
                  placeholder="Type your broadcast message..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowBroadcast(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Handle broadcast send
                  setShowBroadcast(false)
                }}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Send Broadcast
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default WhatsAppPage

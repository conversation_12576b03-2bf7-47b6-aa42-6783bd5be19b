import React, { useState } from 'react';
import { 
  Building, 
  ExternalLink, 
  Copy, 
  CheckCircle, 
  AlertCircle, 
  Info,
  Eye,
  EyeOff
} from 'lucide-react';

interface SalesforceSetupProps {
  onComplete: (config: any) => void;
  onBack: () => void;
}

const SalesforceSetup: React.FC<SalesforceSetupProps> = ({ onComplete, onBack }) => {
  const [step, setStep] = useState(1);
  const [config, setConfig] = useState({
    clientId: '',
    clientSecret: '',
    instanceUrl: 'https://na1.salesforce.com',
    apiVersion: '58.0',
    redirectUri: 'http://localhost:3000/oauth/salesforce/callback'
  });
  const [showSecret, setShowSecret] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleTestConnection = async () => {
    setLoading(true);
    try {
      // Mock API call to test connection
      await new Promise(resolve => setTimeout(resolve, 2000));
      setStep(3);
    } catch (error) {
      console.error('Connection test failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleComplete = () => {
    onComplete(config);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
          <Building className="w-8 h-8 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Salesforce Setup</h2>
        <p className="text-gray-600 mt-2">Configure your Salesforce Connected App</p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start">
          <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-3" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-2">Before you begin, you need to create a Connected App in Salesforce:</p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Log in to your Salesforce org</li>
              <li>Go to Setup → App Manager → New Connected App</li>
              <li>Fill in the basic information</li>
              <li>Enable OAuth Settings</li>
              <li>Add the callback URL: <code className="bg-blue-100 px-1 rounded">{config.redirectUri}</code></li>
              <li>Select the required OAuth scopes</li>
              <li>Save and note the Consumer Key and Consumer Secret</li>
            </ol>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <button
          onClick={() => setStep(2)}
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          I've Created the Connected App
          <ExternalLink className="w-4 h-4 ml-2" />
        </button>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Enter Connected App Details</h2>
        <p className="text-gray-600 mt-2">Add the credentials from your Salesforce Connected App</p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Consumer Key (Client ID) *
          </label>
          <input
            type="text"
            value={config.clientId}
            onChange={(e) => handleInputChange('clientId', e.target.value)}
            placeholder="3MVG9..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Consumer Secret (Client Secret) *
          </label>
          <div className="relative">
            <input
              type={showSecret ? 'text' : 'password'}
              value={config.clientSecret}
              onChange={(e) => handleInputChange('clientSecret', e.target.value)}
              placeholder="••••••••••••••••••••••••••••••••"
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
            <button
              type="button"
              onClick={() => setShowSecret(!showSecret)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showSecret ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Instance URL
          </label>
          <input
            type="text"
            value={config.instanceUrl}
            onChange={(e) => handleInputChange('instanceUrl', e.target.value)}
            placeholder="https://na1.salesforce.com"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <p className="text-xs text-gray-500 mt-1">
            Common instances: na1 (North America), eu1 (Europe), ap1 (Asia Pacific)
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            API Version
          </label>
          <select
            value={config.apiVersion}
            onChange={(e) => handleInputChange('apiVersion', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="58.0">58.0 (Latest)</option>
            <option value="57.0">57.0</option>
            <option value="56.0">56.0</option>
            <option value="55.0">55.0</option>
          </select>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 mr-2" />
          <div className="text-sm text-yellow-800">
            <p className="font-medium">Required OAuth Scopes:</p>
            <ul className="mt-1 list-disc list-inside space-y-1">
              <li>Access and manage your data (api)</li>
              <li>Perform requests at any time (refresh_token, offline_access)</li>
              <li>Access custom permissions (custom_permissions)</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setStep(1)}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Back
        </button>
        <button
          onClick={handleTestConnection}
          disabled={!config.clientId || !config.clientSecret || loading}
          className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Testing...
            </>
          ) : (
            'Test Connection'
          )}
        </button>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Connection Successful!</h2>
        <p className="text-gray-600 mt-2">Your Salesforce connection has been configured successfully</p>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="font-medium text-green-800 mb-3">Connection Details:</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-green-700">Instance URL:</span>
            <span className="text-green-900">{config.instanceUrl}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-green-700">API Version:</span>
            <span className="text-green-900">{config.apiVersion}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-green-700">Status:</span>
            <span className="text-green-900">Connected</span>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-2" />
          <div className="text-sm text-blue-800">
            <p className="font-medium">Next Steps:</p>
            <ul className="mt-1 list-disc list-inside space-y-1">
              <li>Create data mappings to sync Salesforce objects</li>
              <li>Set up webhooks for real-time data updates</li>
              <li>Configure sync schedules</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <button
          onClick={handleComplete}
          className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          Complete Setup
          <CheckCircle className="w-4 h-4 ml-2" />
        </button>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto p-6">
      {step === 1 && renderStep1()}
      {step === 2 && renderStep2()}
      {step === 3 && renderStep3()}
    </div>
  );
};

export default SalesforceSetup; 
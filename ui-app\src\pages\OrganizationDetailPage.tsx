import { useState } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import {
  ArrowLeft,
  Mail,
  Edit,
  ExternalLink,
  Building,
  User,
  Shield,
  Settings,
  Activity,
  Phone,
  Calendar,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  Tag,
  Key,
  LogOut,
  Trash2,
  Save,
  X,
  Box,
  Package,
  Plug
} from 'lucide-react'

const OrganizationDetailPage = () => {
  const { userId } = useParams()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')

  console.log('OrganizationDetailPage rendered, userId:', userId)

  // Mock user data - in real app this would come from API based on userId
  const getUserData = () => {
    if (userId === 'guest-1') {
      return {
        id: 'guest-1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        organization: 'TechStart Inc',
        organizationType: 'Supplier',
        accessLevel: 'Read Only',
        status: 'Active',
        dateGranted: 'May 15, 2025',
        expirationDate: 'Jun 15, 2025',
        lastLogin: 'May 20, 2025, 03:15 PM',
        lastIpAddress: '************',
        avatar: 'MW',
        description: 'Guest User from TechStart Inc • Granted: May 15, 2025',
        permissions: {
          dashboard: 'Read Only',
          orders: 'No Access',
          products: 'Read Only'
        }
      }
    } else if (userId === 'guest-2') {
      return {
        id: 'guest-2',
        name: 'Anna Lee',
        email: '<EMAIL>',
        phone: '+****************',
        organization: 'Global Corp',
        organizationType: 'Vendor',
        accessLevel: 'Limited Access',
        status: 'Pending',
        dateGranted: 'May 20, 2025',
        expirationDate: 'Jul 01, 2025',
        lastLogin: 'Never',
        lastIpAddress: 'N/A',
        avatar: 'AL',
        description: 'Guest User from Global Corp • Granted: May 20, 2025',
        permissions: {
          dashboard: 'Limited',
          orders: 'Limited',
          products: 'No Access'
        }
      }
    } else {
      return {
        id: 'guest-3',
        name: 'Robert Kim',
        email: '<EMAIL>',
        phone: '+****************',
        organization: 'Innovation Labs',
        organizationType: 'B2B',
        accessLevel: 'Full Access',
        status: 'Expired',
        dateGranted: 'Mar 01, 2025',
        expirationDate: 'May 01, 2025',
        lastLogin: 'Apr 30, 2025, 11:45 AM',
        lastIpAddress: '*************',
        avatar: 'RK',
        description: 'Guest User from Innovation Labs • Granted: Mar 01, 2025',
        permissions: {
          dashboard: 'Full Access',
          orders: 'Full Access',
          products: 'Full Access'
        }
      }
    }
  }

  const user = getUserData()

  const loginHistory = [
    {
      date: 'May 21, 2025, 02:30 PM',
      ipAddress: '***********',
      device: 'Chrome / Windows 10',
      location: 'San Francisco, CA, USA',
      status: 'Success'
    },
    {
      date: 'May 18, 2025, 09:45 AM',
      ipAddress: '***********',
      device: 'Chrome / Windows 10',
      location: 'San Francisco, CA, USA',
      status: 'Success'
    },
    {
      date: 'May 16, 2025, 04:20 PM',
      ipAddress: '***********',
      device: 'Mobile / iOS 17',
      location: 'San Francisco, CA, USA',
      status: 'Success'
    },
    {
      date: 'May 10, 2025, 11:15 AM',
      ipAddress: '***********',
      device: 'Chrome / Windows 10',
      location: 'San Francisco, CA, USA',
      status: 'Success'
    },
    {
      date: 'May 5, 2025, 08:30 AM',
      ipAddress: '***********',
      device: 'Firefox / macOS',
      location: 'San Francisco, CA, USA',
      status: 'Failed'
    }
  ]

  const activityLog = [
    {
      id: 1,
      action: 'Updated profile information',
      timestamp: 'May 15, 2025, 10:30 AM',
      details: 'By: Sarah Johnson',
      type: 'profile'
    },
    {
      id: 2,
      action: 'Permissions updated by Administrator',
      timestamp: 'May 10, 2025, 02:20 PM',
      details: 'By: Sarah Johnson',
      type: 'permissions'
    },
    {
      id: 3,
      action: 'Role changed from Manager to Admin',
      timestamp: 'May 1, 2025, 09:15 AM',
      details: 'By: Sarah Johnson',
      type: 'role'
    },
    {
      id: 4,
      action: 'Account created',
      timestamp: 'Oct 15, 2024, 08:50 AM',
      details: '',
      type: 'account'
    }
  ]

  const tabs = [
    { id: 'overview', name: 'Overview', icon: User },
    { id: 'activity', name: 'Activity', icon: Activity }
  ]

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
    switch (status.toLowerCase()) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'expired':
      case 'inactive':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  const getPermissionBadge = (permission: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
    switch (permission.toLowerCase()) {
      case 'full access':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'limited':
      case 'read only':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'no access':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'profile':
        return <User className="h-4 w-4 text-blue-500" />
      case 'permissions':
        return <Shield className="h-4 w-4 text-purple-500" />
      case 'role':
        return <Key className="h-4 w-4 text-orange-500" />
      case 'account':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate(-1)}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Organization
          </button>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="h-12 w-12 rounded-full flex items-center justify-center text-white font-medium text-lg bg-indigo-500">
                {user.avatar}
              </div>
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-gray-900">{userId === 'guest-1' ? 'TechStart Inc.' : user.name}</h1>
                {/* Subtext removed as requested */}
                <div className="flex items-center mt-1">
                  <span className={getStatusBadge(user.status)}>{user.status}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {/* Organization name with external link removed as requested */}
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <Mail className="h-4 w-4 mr-2" />
                Email User
              </button>
              <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800">
                <Edit className="h-4 w-4 mr-2" />
                Edit User
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Contact Information (renamed) */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200 flex items-center">
                    <User className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center mb-6">
                      <div className="h-16 w-16 rounded-full flex items-center justify-center text-white font-medium text-xl bg-indigo-500">
                        {user.avatar}
                      </div>
                      <div className="ml-4">
                        <h4 className="text-lg font-medium text-gray-900">{user.name}</h4>
                        <p className="text-sm text-gray-500">Guest User</p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{user.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{user.phone}</span>
                      </div>
                      <div className="flex items-center">
                        <Building className="h-4 w-4 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{user.organization}</span>
                      </div>
                      <div className="flex items-center">
                        <Tag className="h-4 w-4 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{user.organizationType}</span>
                      </div>
                    </div>

                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-500">Status</span>
                        <span className={getStatusBadge(user.status)}>{user.status}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-500">Date Granted</span>
                        <span className="text-sm text-gray-900">{user.dateGranted}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Access Information */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200 flex items-center">
                    <Shield className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Access Information</h3>
                  </div>
                  <div className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-500">Last Login</span>
                        <span className="text-sm text-gray-900">{user.lastLogin}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-500">Last IP Address</span>
                        <span className="text-sm text-gray-900">{user.lastIpAddress}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-500">Expiration Date</span>
                        <span className="text-sm text-gray-900">{user.expirationDate}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="px-6 py-4 border-b border-gray-200 flex items-center">
                    <Activity className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
                  </div>
                  <div className="p-6">
                    <div className="space-y-3">
                      {activityLog.slice(0, 3).map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-3">
                          {getActivityIcon(activity.type)}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                            <p className="text-xs text-gray-500">{activity.timestamp}</p>
                            {activity.details && (
                              <p className="text-xs text-gray-400">{activity.details}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'activity' && (
              <div className="space-y-6">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Activity Log</h3>
                  <div className="space-y-3">
                    {activityLog.map((activity) => (
                      <div key={activity.id} className="flex items-start space-x-3 p-3 bg-white rounded-lg border">
                        {getActivityIcon(activity.type)}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                          <p className="text-xs text-gray-500">{activity.timestamp}</p>
                          {activity.details && (
                            <p className="text-xs text-gray-400">{activity.details}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default OrganizationDetailPage 
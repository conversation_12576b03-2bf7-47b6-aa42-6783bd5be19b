import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export type FontSize = 'small' | 'medium' | 'large'
export type Theme = 'light' | 'dark'

interface SettingsContextType {
  fontSize: FontSize
  theme: Theme
  setFontSize: (size: FontSize) => void
  setTheme: (theme: Theme) => void
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

interface SettingsProviderProps {
  children: ReactNode
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const [fontSize, setFontSizeState] = useState<FontSize>('small')
  const [theme, setThemeState] = useState<Theme>('light')

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedFontSize = localStorage.getItem('fontSize') as FontSize
    const savedTheme = localStorage.getItem('theme') as Theme
    
    if (savedFontSize && ['small', 'medium', 'large'].includes(savedFontSize)) {
      setFontSizeState(savedFontSize)
    }
    
    if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
      setThemeState(savedTheme)
    }
  }, [])

  // Apply theme to document
  useEffect(() => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [theme])

  // Apply font size to document
  useEffect(() => {
    const root = document.documentElement
    root.classList.remove('text-small', 'text-medium', 'text-large')
    root.classList.add(`text-${fontSize}`)
  }, [fontSize])

  const setFontSize = (size: FontSize) => {
    setFontSizeState(size)
    localStorage.setItem('fontSize', size)
  }

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
    localStorage.setItem('theme', newTheme)
  }

  return (
    <SettingsContext.Provider value={{ fontSize, theme, setFontSize, setTheme }}>
      {children}
    </SettingsContext.Provider>
  )
}

export const useSettings = () => {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}

import { Customer, CustomerFilters, CustomerListResponse, CustomerActivity, CustomerNote, CustomerDocument, Order, OrderFilters, OrderListResponse } from '../types/customer'

// Mock data for customers
const mockCustomers: Customer[] = [
  {
    id: 'CUST-001',
    name: 'Acme Corporation',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Acme Corporation',
    type: 'Business',
    status: 'Active',
    tier: 'Gold',
    credit_limit: 50000,
    credit_used: 15000,
    payment_terms: 'Net 30',
    tax_exempt: false,
    date_created: '2023-01-15',
    last_order_date: '2024-01-10',
    total_orders: 45,
    total_spent: 125000,
    lifetime_value: 150000,
    average_order_value: 2777,
    tags: ['VIP', 'Enterprise', 'Technology'],
    notes: 'Key enterprise customer with high growth potential',
    assigned_rep: '<PERSON>',
    territory: 'West Coast',
    source: 'Website',
    website: 'https://acme.com',
    industry: 'Technology',
    employee_count: 500,
    annual_revenue: 10000000,
    primary_contact: {
      id: 'CONT-001',
      name: '<PERSON>',
      title: 'Procurement Manager',
      email: '<EMAIL>',
      phone: '+****************',
      mobile: '+****************',
      department: 'Procurement',
      is_primary: true,
      is_decision_maker: true,
      communication_preferences: ['email', 'phone']
    },
    billing_address: {
      id: 'ADDR-001',
      type: 'billing',
      name: 'Acme Corporation',
      company: 'Acme Corporation',
      address_line_1: '123 Business Ave',
      address_line_2: 'Suite 100',
      city: 'San Francisco',
      state: 'CA',
      postal_code: '94105',
      country: 'USA',
      is_default: true
    },
    shipping_addresses: [
      {
        id: 'ADDR-002',
        type: 'shipping',
        name: 'Acme Warehouse',
        company: 'Acme Corporation',
        address_line_1: '456 Warehouse Blvd',
        city: 'Oakland',
        state: 'CA',
        postal_code: '94607',
        country: 'USA',
        is_default: true,
        delivery_instructions: 'Loading dock B'
      }
    ],
    payment_methods: [
      {
        id: 'PAY-001',
        type: 'net_terms',
        name: 'Net 30 Terms',
        details: 'Net 30 payment terms',
        is_default: true,
        is_active: true
      }
    ],
    credit_history: [
      {
        id: 'CREDIT-001',
        date: '2023-06-15',
        type: 'limit_increase',
        amount: 50000,
        reason: 'Increased order volume',
        approved_by: 'Credit Manager'
      }
    ],
    communication_preferences: {
      email_notifications: true,
      sms_notifications: false,
      phone_calls: true,
      postal_mail: false,
      preferred_contact_time: '9 AM - 5 PM PST',
      language: 'English',
      frequency: 'weekly'
    },
    order_preferences: {
      preferred_shipping_method: 'Standard',
      preferred_payment_method: 'Net Terms',
      auto_reorder: false,
      special_instructions: 'Call before delivery',
      requires_po_number: true,
      approval_required: false
    },
    child_customers: [],
    metrics: {
      orders_last_30_days: 3,
      orders_last_90_days: 8,
      orders_last_year: 45,
      revenue_last_30_days: 8500,
      revenue_last_90_days: 22000,
      revenue_last_year: 125000,
      average_days_between_orders: 8,
      return_rate: 2.1,
      satisfaction_score: 4.8,
      nps_score: 9,
      churn_risk: 'low',
      engagement_score: 85
    }
  },
  {
    id: 'CUST-002',
    name: 'TechStart Inc',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'TechStart Inc',
    type: 'Business',
    status: 'Active',
    tier: 'Silver',
    credit_limit: 25000,
    credit_used: 8500,
    payment_terms: 'Net 15',
    tax_exempt: false,
    date_created: '2023-03-20',
    last_order_date: '2024-01-08',
    total_orders: 28,
    total_spent: 67500,
    lifetime_value: 85000,
    average_order_value: 2410,
    tags: ['Startup', 'Technology', 'Growth'],
    notes: 'Fast-growing startup with increasing order frequency',
    assigned_rep: 'Mike Chen',
    territory: 'East Coast',
    source: 'Referral',
    website: 'https://techstart.com',
    industry: 'Software',
    employee_count: 150,
    annual_revenue: 5000000,
    primary_contact: {
      id: 'CONT-002',
      name: 'Emily Davis',
      title: 'Operations Director',
      email: '<EMAIL>',
      phone: '+****************',
      department: 'Operations',
      is_primary: true,
      is_decision_maker: true,
      communication_preferences: ['email']
    },
    billing_address: {
      id: 'ADDR-003',
      type: 'billing',
      name: 'TechStart Inc',
      company: 'TechStart Inc',
      address_line_1: '789 Innovation Dr',
      city: 'Boston',
      state: 'MA',
      postal_code: '02101',
      country: 'USA',
      is_default: true
    },
    shipping_addresses: [
      {
        id: 'ADDR-004',
        type: 'shipping',
        name: 'TechStart Office',
        company: 'TechStart Inc',
        address_line_1: '789 Innovation Dr',
        city: 'Boston',
        state: 'MA',
        postal_code: '02101',
        country: 'USA',
        is_default: true
      }
    ],
    payment_methods: [
      {
        id: 'PAY-002',
        type: 'credit_card',
        name: 'Corporate Card',
        details: '**** **** **** 1234',
        is_default: true,
        is_active: true,
        expiry_date: '12/25'
      }
    ],
    credit_history: [],
    communication_preferences: {
      email_notifications: true,
      sms_notifications: true,
      phone_calls: false,
      postal_mail: false,
      preferred_contact_time: '10 AM - 6 PM EST',
      language: 'English',
      frequency: 'immediate'
    },
    order_preferences: {
      preferred_shipping_method: 'Express',
      preferred_payment_method: 'Credit Card',
      auto_reorder: true,
      special_instructions: '',
      requires_po_number: false,
      approval_required: false
    },
    child_customers: [],
    metrics: {
      orders_last_30_days: 2,
      orders_last_90_days: 6,
      orders_last_year: 28,
      revenue_last_30_days: 4800,
      revenue_last_90_days: 14500,
      revenue_last_year: 67500,
      average_days_between_orders: 13,
      return_rate: 1.5,
      satisfaction_score: 4.6,
      nps_score: 8,
      churn_risk: 'low',
      engagement_score: 78
    }
  },
  {
    id: 'CUST-002',
    name: 'Luket Lailor',
    email: '<EMAIL>',
    phone: '(*************',
    company: 'Atlantix',
    type: 'Business',
    status: 'Active',
    tier: 'Gold',
    credit_limit: 0,
    credit_used: 0,
    payment_terms: 'Not Set',
    tax_exempt: false,
    date_created: '2024-08-09T08:53:00Z',
    last_order_date: '2025-06-26T03:53:00Z',
    total_orders: 37,
    total_spent: 0,
    lifetime_value: 0,
    average_order_value: 0,
    tags: [],
    notes: 'Araceli 12:27 PM 8/9/2024 - Per Avani -Hello Araceli ...',
    assigned_rep: 'Admin User',
    territory: '',
    source: '',
    website: 'hello.com',
    industry: '',
    employee_count: 0,
    annual_revenue: 0,
    primary_contact: {
      id: 'CONT-003',
      name: '',
      title: '',
      email: '<EMAIL>',
      phone: '**********',
      is_primary: true,
      is_decision_maker: false,
      communication_preferences: []
    },
    billing_address: {
      id: 'ADDR-005',
      type: 'billing',
      name: 'Luket Lailor',
      address_line_1: '123 Main St',
      city: 'City',
      state: 'State',
      postal_code: 'ZIP',
      country: 'USA',
      is_default: true
    },
    shipping_addresses: [
      {
        id: 'ADDR-006',
        type: 'shipping',
        name: 'Luket Lailor',
        address_line_1: '456 Other St',
        city: 'City',
        state: 'State',
        postal_code: 'ZIP',
        country: 'USA',
        is_default: true
      }
    ],
    payment_methods: [],
    credit_history: [],
    communication_preferences: {
      email_notifications: false,
      sms_notifications: false,
      phone_calls: false,
      postal_mail: false,
      preferred_contact_time: '',
      language: '',
      frequency: 'monthly'
    },
    order_preferences: {
      preferred_shipping_method: '',
      preferred_payment_method: '',
      auto_reorder: false,
      special_instructions: '',
      requires_po_number: false,
      approval_required: false
    },
    child_customers: [],
    metrics: {
      orders_last_30_days: 0,
      orders_last_90_days: 0,
      orders_last_year: 0,
      revenue_last_30_days: 0,
      revenue_last_90_days: 0,
      revenue_last_year: 0,
      average_days_between_orders: 0,
      return_rate: 0,
      satisfaction_score: 0,
      nps_score: 0,
      churn_risk: 'low',
      engagement_score: 0
    },
    payment_term: 'Not Set',
    payment_term_approved_by: '',
    payment_term_approved_date_time: '',
    payment_term_notes: '',
    payment_term_issues: '',
    payment_term_risk: '',
    unpaid_balance: 0,
    ach_echeck_approved: false,
    ach_echeck_approved_by: '',
    ach_echecked_approved_date_time: '',
    ach_form_received_uploaded: false,
    ach_form_uploaded_by: '',
    ach_form_received_uploaded_date: '',
    net_terms_received_and_uploaded: false,
    net_term_agreement_uploaded_by: '',
    net_terms_uploaded_date_time: '',
    customer_credit_card_fees_exempt: false,
    credit_card_fees_exempt_approved_by: '',
    credit_card_fees_exempt_approved_date: '',
    federal_tax_identification: '*********',
    read_agree_midwest_terms: false,
    are_you_a_business: false,
    how_did_you_hear_about_us: 'Sales Team',
    last_order_number: '1172647',
    first_order_placed_date: '2024-10-01T07:30:00Z',
    total_spent_last_30_days: 0,
    last_order_amount: 720,
    order_payment_method: 'Pay at Bensenville Warehouse, Cash/CC',
    order_shipping_method: 'Liquidation Shipments (MW)',
    days_since_last_order: 0,
    attempt1_date_capture: '2024-08-09T08:53:00Z',
    user_who_last_modified_notes: 'Araceli',
    notes_last_modified_date_time: '2025-06-26T06:30:00Z',
    cash_and_carry_check_approved: false,
    check_approved_by: '',
    check_approved_date_time: '',
    check_approved_amount_limit: 0,
    noted_for_check_cnc: '',
    does_customer_have_account_with_ctos: false,
    bc_customer_group: 'Tiered 1 Prices Group (Make Sure Customer Has No Coupon CODE)',
    bc_group_name: 'Tiered 1 Prices Group (Make Sure Customer Has No Coupon CODE)',
    bc_email: '<EMAIL>',
    bc_store_credit: 0,
    bc_customerid: '33984',
    bc_modified_time: '2025-06-26T06:30:00Z',
    cell_mobile_number: '**********',
    sms_consent: false,
    pricelist: 'Tier Pro',
    main_contact_name: '',
    main_contact_phone: '',
    days_since_account_created: 321,
    months_and_days: '10 months 17 days',
    approved_date: '2025-06-26T06:28:00Z',
    approved_user: 'Araceli Diaz',
    account_approved_date_time: '2025-06-26T06:28:00Z',
    account_rejected_date_time: '',
    account_closed_date_time: '2025-01-02T09:57:00Z',
    account_on_hold_date_time: '',
    account_created_manually: 'No',
    usps_exempt_reminder_email: false,
    market_influencer: '',
    old_record: false,
    pre_rejected_customer: false,
    re_approved_date_time: '',
    coupon_code: '',
    customer_group: 'Tiered 1 Prices Group (Make Sure Customer Has No Coupon CODE)',
    related_account: '1',
    related_account_manager: 'Admin User',
    company_google_search: 'Google Search',
    first_call_date: '',
    last_call_date: '',
    joined: '2024-08-09T08:53:00Z',
    approval_path: 'Approved',
    state_licensed: 'TEST',
    authorized_state_license: true,
    rejected_closed_hold_reason: '',
    retailer_distributor: '',
    type_of_business: 'Employee',
    no_of_locations: 0,
    how_many_customers_you_sell_to_monthly: 0,
    estimated_monthly_purchasing: 0,
    auto_create_account_at_cbdstore: 'Not Interested',
    do_you_want_to_be_tax_exempt: false,
    do_not_notify_the_customer: false,
    tax_exempt_state_list: '',
    price_leak_incident: '',
    customer_price_risk: '',
    no_activity_in_days: 0,
    user_who_reject_closed_hold_this_account: '',
    customer_badge: 'Not Set',
    state_website_verification_link: '',
    block_credit_card_payment: false,
    block_cash_and_carry_payment: false,
    ach_auth_temp: false,
    net_terms_auth_temp: false,
    morning_media_screen: false,
    date_installed: ''
  }
]

// Mock activities
const mockActivities: CustomerActivity[] = [
  {
    id: 'ACT-001',
    customer_id: 'CUST-001',
    type: 'order_placed',
    description: 'Order #ORD-003 placed for $2,850',
    date: '2024-01-10T10:30:00Z',
    user: 'System'
  },
  {
    id: 'ACT-002',
    customer_id: 'CUST-001',
    type: 'contact_made',
    description: 'Phone call regarding upcoming order',
    date: '2024-01-08T14:15:00Z',
    user: 'Sarah Johnson'
  }
]

// Mock notes
const mockNotes: CustomerNote[] = [
  {
    id: 'NOTE-001',
    customer_id: 'CUST-001',
    content: 'Customer interested in bulk pricing for Q2 orders',
    type: 'sales',
    visibility: 'internal',
    created_by: 'Sarah Johnson',
    created_at: '2024-01-05T09:00:00Z',
    tags: ['pricing', 'bulk-order']
  }
]

// Mock order data
const mockOrders: Order[] = [
  {
    id: 'ORD-001',
    order_number: 'ORD-2024-001',
    customer_id: 'CUST-002',
    customer_name: 'Luket Lailor',
    status: 'Delivered',
    order_date: '2024-01-15',
    required_date: '2024-01-20',
    shipped_date: '2024-01-18',
    delivered_date: '2024-01-19',
    total_amount: 2450.00,
    tax_amount: 196.00,
    shipping_amount: 45.00,
    discount_amount: 100.00,
    subtotal: 2309.00,
    currency: 'USD',
    payment_method: 'Credit Card',
    payment_status: 'Paid',
    shipping_method: 'Express',
    shipping_address: {
      id: 'SHIP-001',
      type: 'shipping',
      name: 'Luket Lailor',
      address_line_1: '123 Main Street',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA',
      is_default: true
    },
    billing_address: {
      id: 'BILL-001',
      type: 'billing',
      name: 'Luket Lailor',
      address_line_1: '123 Main Street',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA',
      is_default: true
    },
    items: [
      {
        id: 'ITEM-001',
        order_id: 'ORD-001',
        product_id: 'PROD-001',
        product_name: 'Premium Widget A',
        product_sku: 'WID-A-PREM',
        product_category: 'Widgets',
        quantity: 2,
        unit_price: 750.00,
        total_price: 1500.00,
        discount_amount: 50.00,
        tax_amount: 120.00,
        weight: 2.5,
        status: 'Delivered',
        backordered: false
      },
      {
        id: 'ITEM-002',
        order_id: 'ORD-001',
        product_id: 'PROD-002',
        product_name: 'Standard Widget B',
        product_sku: 'WID-B-STD',
        product_category: 'Widgets',
        quantity: 1,
        unit_price: 450.00,
        total_price: 450.00,
        discount_amount: 25.00,
        tax_amount: 34.00,
        weight: 1.8,
        status: 'Delivered',
        backordered: false
      }
    ],
    notes: 'Customer requested express shipping for urgent delivery',
    po_number: 'PO-2024-001',
    tracking_number: '1Z999AA1234567890',
    carrier: 'FedEx',
    estimated_delivery: '2024-01-19',
    actual_delivery: '2024-01-19',
    created_by: '<EMAIL>',
    created_at: '2024-01-15T10:30:00Z',
    tags: ['Express', 'VIP'],
    priority: 'High',
    source: 'Web',
    approval_required: false,
    risk_level: 'Low',
    customer_rating: 5,
    customer_feedback: 'Excellent service and fast delivery!'
  },
  {
    id: 'ORD-002',
    order_number: 'ORD-2024-002',
    customer_id: 'CUST-002',
    customer_name: 'Luket Lailor',
    status: 'Processing',
    order_date: '2024-01-20',
    required_date: '2024-01-25',
    total_amount: 1895.50,
    tax_amount: 151.64,
    shipping_amount: 35.00,
    discount_amount: 0,
    subtotal: 1708.86,
    currency: 'USD',
    payment_method: 'Net Terms',
    payment_status: 'Pending',
    shipping_method: 'Standard',
    shipping_address: {
      id: 'SHIP-002',
      type: 'shipping',
      name: 'Luket Lailor',
      address_line_1: '456 Business Ave',
      address_line_2: 'Suite 200',
      city: 'New York',
      state: 'NY',
      postal_code: '10002',
      country: 'USA',
      is_default: false
    },
    billing_address: {
      id: 'BILL-002',
      type: 'billing',
      name: 'Luket Lailor',
      address_line_1: '123 Main Street',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA',
      is_default: true
    },
    items: [
      {
        id: 'ITEM-003',
        order_id: 'ORD-002',
        product_id: 'PROD-003',
        product_name: 'Industrial Widget C',
        product_sku: 'WID-C-IND',
        product_category: 'Industrial',
        quantity: 3,
        unit_price: 425.00,
        total_price: 1275.00,
        discount_amount: 0,
        tax_amount: 102.00,
        weight: 5.2,
        status: 'Processing',
        backordered: false
      },
      {
        id: 'ITEM-004',
        order_id: 'ORD-002',
        product_id: 'PROD-004',
        product_name: 'Accessory Kit D',
        product_sku: 'ACC-D-KIT',
        product_category: 'Accessories',
        quantity: 1,
        unit_price: 620.50,
        total_price: 620.50,
        discount_amount: 0,
        tax_amount: 49.64,
        weight: 1.2,
        status: 'Processing',
        backordered: false
      }
    ],
    notes: 'Standard business order',
    po_number: 'PO-2024-002',
    created_by: '<EMAIL>',
    created_at: '2024-01-20T14:15:00Z',
    tags: ['Business', 'Standard'],
    priority: 'Normal',
    source: 'Phone',
    approval_required: false,
    risk_level: 'Low'
  },
  {
    id: 'ORD-003',
    order_number: 'ORD-2024-003',
    customer_id: 'CUST-002',
    customer_name: 'Luket Lailor',
    status: 'Shipped',
    order_date: '2024-01-25',
    required_date: '2024-01-30',
    shipped_date: '2024-01-26',
    total_amount: 3200.00,
    tax_amount: 256.00,
    shipping_amount: 0,
    discount_amount: 200.00,
    subtotal: 3144.00,
    currency: 'USD',
    payment_method: 'Credit Card',
    payment_status: 'Paid',
    shipping_method: 'Free Shipping',
    shipping_address: {
      id: 'SHIP-003',
      type: 'shipping',
      name: 'Luket Lailor',
      address_line_1: '789 Warehouse Blvd',
      city: 'New York',
      state: 'NY',
      postal_code: '10003',
      country: 'USA',
      is_default: false,
      delivery_instructions: 'Loading dock A, call 30 minutes before arrival'
    },
    billing_address: {
      id: 'BILL-003',
      type: 'billing',
      name: 'Luket Lailor',
      address_line_1: '123 Main Street',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA',
      is_default: true
    },
    items: [
      {
        id: 'ITEM-005',
        order_id: 'ORD-003',
        product_id: 'PROD-005',
        product_name: 'Bulk Widget E',
        product_sku: 'WID-E-BULK',
        product_category: 'Bulk Items',
        quantity: 10,
        unit_price: 320.00,
        total_price: 3200.00,
        discount_amount: 200.00,
        tax_amount: 256.00,
        weight: 25.0,
        status: 'Shipped',
        backordered: false
      }
    ],
    notes: 'Bulk order with free shipping promotion',
    po_number: 'PO-2024-003',
    tracking_number: '1Z999AA1234567891',
    carrier: 'UPS',
    estimated_delivery: '2024-01-28',
    created_by: '<EMAIL>',
    created_at: '2024-01-25T09:45:00Z',
    tags: ['Bulk', 'Free Shipping'],
    priority: 'Normal',
    source: 'Web',
    approval_required: false,
    risk_level: 'Low'
  },
  {
    id: 'ORD-004',
    order_number: 'ORD-2024-004',
    customer_id: 'CUST-002',
    customer_name: 'Luket Lailor',
    status: 'Pending',
    order_date: '2024-01-28',
    required_date: '2024-02-05',
    total_amount: 875.25,
    tax_amount: 70.02,
    shipping_amount: 25.00,
    discount_amount: 0,
    subtotal: 780.23,
    currency: 'USD',
    payment_method: 'Check',
    payment_status: 'Pending',
    shipping_method: 'Standard',
    shipping_address: {
      id: 'SHIP-004',
      type: 'shipping',
      name: 'Luket Lailor',
      address_line_1: '123 Main Street',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA',
      is_default: true
    },
    billing_address: {
      id: 'BILL-004',
      type: 'billing',
      name: 'Luket Lailor',
      address_line_1: '123 Main Street',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA',
      is_default: true
    },
    items: [
      {
        id: 'ITEM-006',
        order_id: 'ORD-004',
        product_id: 'PROD-006',
        product_name: 'Specialty Widget F',
        product_sku: 'WID-F-SPEC',
        product_category: 'Specialty',
        quantity: 1,
        unit_price: 875.25,
        total_price: 875.25,
        discount_amount: 0,
        tax_amount: 70.02,
        weight: 3.5,
        status: 'Pending',
        backordered: false
      }
    ],
    notes: 'Check payment pending',
    created_by: '<EMAIL>',
    created_at: '2024-01-28T16:20:00Z',
    tags: ['Check Payment'],
    priority: 'Low',
    source: 'Email',
    approval_required: true,
    risk_level: 'Medium'
  }
]

export const mockCustomerApi = {
  getCustomers: async (filters?: CustomerFilters, page = 1, perPage = 20): Promise<CustomerListResponse> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredCustomers = [...mockCustomers]
    
    // Apply filters
    if (filters?.search) {
      const search = filters.search.toLowerCase()
      filteredCustomers = filteredCustomers.filter(customer =>
        customer.name.toLowerCase().includes(search) ||
        customer.email.toLowerCase().includes(search) ||
        customer.company.toLowerCase().includes(search)
      )
    }
    
    if (filters?.status?.length) {
      filteredCustomers = filteredCustomers.filter(customer =>
        filters.status!.includes(customer.status)
      )
    }
    
    if (filters?.type?.length) {
      filteredCustomers = filteredCustomers.filter(customer =>
        filters.type!.includes(customer.type)
      )
    }
    
    if (filters?.tier?.length) {
      filteredCustomers = filteredCustomers.filter(customer =>
        filters.tier!.includes(customer.tier)
      )
    }
    
    const total = filteredCustomers.length
    const startIndex = (page - 1) * perPage
    const endIndex = startIndex + perPage
    const customers = filteredCustomers.slice(startIndex, endIndex)
    
    return {
      customers,
      total,
      page,
      per_page: perPage,
      total_pages: Math.ceil(total / perPage)
    }
  },

  getCustomer: async (id: string): Promise<Customer | null> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return mockCustomers.find(customer => customer.id === id) || null
  },

  getCustomerActivities: async (customerId: string): Promise<CustomerActivity[]> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return mockActivities.filter(activity => activity.customer_id === customerId)
  },

  getCustomerNotes: async (customerId: string): Promise<CustomerNote[]> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return mockNotes.filter(note => note.customer_id === customerId)
  },

  createCustomer: async (customer: Partial<Customer>): Promise<Customer> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    const newCustomer = {
      ...customer,
      id: `CUST-${String(mockCustomers.length + 1).padStart(3, '0')}`,
      date_created: new Date().toISOString().split('T')[0]
    } as Customer
    mockCustomers.push(newCustomer)
    return newCustomer
  },

  updateCustomer: async (id: string, updates: Partial<Customer>): Promise<Customer> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockCustomers.findIndex(customer => customer.id === id)
    if (index === -1) throw new Error('Customer not found')
    
    mockCustomers[index] = { ...mockCustomers[index], ...updates }
    return mockCustomers[index]
  },

  deleteCustomer: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    const index = mockCustomers.findIndex(customer => customer.id === id)
    if (index === -1) throw new Error('Customer not found')
    mockCustomers.splice(index, 1)
  },

  getOrders: async (filters?: OrderFilters, page = 1, perPage = 20): Promise<OrderListResponse> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredOrders = [...mockOrders]
    
    // Apply filters
    if (filters?.search) {
      const search = filters.search.toLowerCase()
      filteredOrders = filteredOrders.filter(order =>
        order.order_number.toLowerCase().includes(search) ||
        order.customer_name.toLowerCase().includes(search) ||
        order.customer_id.toLowerCase().includes(search)
      )
    }
    
    if (filters?.status?.length) {
      filteredOrders = filteredOrders.filter(order =>
        filters.status!.includes(order.status)
      )
    }
    
    if (filters?.payment_status?.length) {
      filteredOrders = filteredOrders.filter(order =>
        filters.payment_status!.includes(order.payment_status)
      )
    }
    
    if (filters?.date_from) {
      filteredOrders = filteredOrders.filter(order =>
        new Date(order.order_date) >= new Date(filters.date_from!)
      )
    }
    
    if (filters?.date_to) {
      filteredOrders = filteredOrders.filter(order =>
        new Date(order.order_date) <= new Date(filters.date_to!)
      )
    }
    
    if (filters?.amount_min) {
      filteredOrders = filteredOrders.filter(order =>
        order.total_amount >= filters.amount_min!
      )
    }
    
    if (filters?.amount_max) {
      filteredOrders = filteredOrders.filter(order =>
        order.total_amount <= filters.amount_max!
      )
    }
    
    const total = filteredOrders.length
    const startIndex = (page - 1) * perPage
    const endIndex = startIndex + perPage
    const orders = filteredOrders.slice(startIndex, endIndex)
    
    return {
      orders,
      total,
      page,
      per_page: perPage,
      total_pages: Math.ceil(total / perPage)
    }
  },

  getOrder: async (id: string): Promise<Order | null> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return mockOrders.find(order => order.id === id) || null
  },

  createOrder: async (order: Partial<Order>): Promise<Order> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    const newOrder = {
      ...order,
      id: `ORD-${String(mockOrders.length + 1).padStart(4, '0')}`
    } as Order
    mockOrders.push(newOrder)
    return newOrder
  },

  updateOrder: async (id: string, updates: Partial<Order>): Promise<Order> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    const index = mockOrders.findIndex(order => order.id === id)
    if (index === -1) throw new Error('Order not found')
    
    mockOrders[index] = { ...mockOrders[index], ...updates }
    return mockOrders[index]
  },

  deleteOrder: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    const index = mockOrders.findIndex(order => order.id === id)
    if (index === -1) throw new Error('Order not found')
    mockOrders.splice(index, 1)
  }
}

{"version": 3, "sources": ["../../msw/src/core/utils/internal/checkGlobals.ts", "../../msw/src/core/utils/internal/isStringEqual.ts", "../../msw/src/core/utils/logging/getStatusCodeColor.ts", "../../msw/src/core/utils/logging/serializeRequest.ts", "../../msw/src/core/utils/logging/serializeResponse.ts", "../../path-to-regexp/src/index.ts", "../../@mswjs/interceptors/src/utils/bufferUtils.ts", "../../@mswjs/interceptors/src/glossary.ts", "../../@mswjs/interceptors/src/utils/canParseUrl.ts", "../../@mswjs/interceptors/src/utils/getValueBySymbol.ts", "../../@mswjs/interceptors/src/utils/fetchUtils.ts", "../../@mswjs/interceptors/src/getRawRequest.ts", "../../@mswjs/interceptors/src/BatchInterceptor.ts", "../../@mswjs/interceptors/src/utils/getCleanUrl.ts", "../../msw/src/core/utils/url/cleanUrl.ts", "../../msw/src/core/utils/url/isAbsoluteUrl.ts", "../../msw/src/core/utils/url/getAbsoluteUrl.ts", "../../msw/src/core/utils/matching/normalizePath.ts", "../../msw/src/core/utils/matching/matchRequestUrl.ts", "../../@bundled-es-modules/cookie/index-esm.js", "../../msw/src/core/utils/request/getRequestCookies.ts", "../../msw/src/core/handlers/HttpHandler.ts", "../../msw/src/core/http.ts", "../../msw/src/core/utils/internal/jsonParse.ts", "../../msw/src/core/utils/internal/parseMultipartData.ts", "../../msw/src/core/utils/internal/parseGraphQLRequest.ts", "../../msw/src/core/handlers/GraphQLHandler.ts", "../../msw/src/core/graphql.ts", "../../msw/src/core/handlers/WebSocketHandler.ts", "../../msw/src/core/ws/WebSocketMemoryClientStore.ts", "../../msw/src/core/ws/WebSocketIndexedDBClientStore.ts", "../../msw/src/core/ws/WebSocketClientManager.ts", "../../msw/src/core/ws.ts", "../../msw/src/core/getResponse.ts", "../../msw/src/core/HttpResponse.ts", "../../msw/src/core/delay.ts", "../../msw/src/core/bypass.ts", "../../msw/src/core/passthrough.ts", "../../msw/src/core/index.ts"], "sourcesContent": ["import { invariant } from 'outvariant'\nimport { devUtils } from './devUtils'\n\nexport function checkGlobals() {\n  /**\n   * MSW expects the \"URL\" constructor to be defined.\n   * It's not present in React Native so suggest a polyfill\n   * instead of failing silently.\n   * @see https://github.com/mswjs/msw/issues/1408\n   */\n  invariant(\n    typeof URL !== 'undefined',\n    devUtils.formatMessage(\n      `Global \"URL\" class is not defined. This likely means that you're running MSW in an environment that doesn't support all Node.js standard API (e.g. React Native). If that's the case, please use an appropriate polyfill for the \"URL\" class, like \"react-native-url-polyfill\".`,\n    ),\n  )\n}\n", "/**\n * Performs a case-insensitive comparison of two given strings.\n */\nexport function isStringEqual(actual: string, expected: string): boolean {\n  return actual.toLowerCase() === expected.toLowerCase()\n}\n", "export enum StatusCodeColor {\n  Success = '#69AB32',\n  Warning = '#F0BB4B',\n  Danger = '#E95F5D',\n}\n\n/**\n * Returns a HEX color for a given response status code number.\n */\nexport function getStatusCodeColor(status: number): StatusCodeColor {\n  if (status < 300) {\n    return StatusCodeColor.Success\n  }\n\n  if (status < 400) {\n    return StatusCodeColor.Warning\n  }\n\n  return StatusCodeColor.Danger\n}\n", "export interface LoggedRequest {\n  url: URL\n  method: string\n  headers: Record<string, string>\n  body: string\n}\n\n/**\n * Formats a mocked request for introspection in browser's console.\n */\nexport async function serializeRequest(\n  request: Request,\n): Promise<LoggedRequest> {\n  const requestClone = request.clone()\n  const requestText = await requestClone.text()\n\n  return {\n    url: new URL(request.url),\n    method: request.method,\n    headers: Object.fromEntries(request.headers.entries()),\n    body: requestText,\n  }\n}\n", "import statuses from '@bundled-es-modules/statuses'\n\nconst { message } = statuses\n\nexport interface SerializedResponse {\n  status: number\n  statusText: string\n  headers: Record<string, any>\n  body: string\n}\n\nexport async function serializeResponse(\n  response: Response,\n): Promise<SerializedResponse> {\n  const responseClone = response.clone()\n  const responseText = await responseClone.text()\n\n  // Normalize the response status and status text when logging\n  // since the default Response instance doesn't infer status texts\n  // from status codes. This has no effect on the actual response instance.\n  const responseStatus = responseClone.status || 200\n  const responseStatusText =\n    responseClone.statusText || message[responseStatus] || 'OK'\n\n  return {\n    status: responseStatus,\n    statusText: responseStatusText,\n    headers: Object.fromEntries(responseClone.headers.entries()),\n    body: responseText,\n  }\n}\n", "/**\n * Tokenizer results.\n */\ninterface LexToken {\n  type:\n    | \"OPEN\"\n    | \"CLOSE\"\n    | \"PATTERN\"\n    | \"NAME\"\n    | \"CHAR\"\n    | \"ESCAPED_CHAR\"\n    | \"MODIFIER\"\n    | \"END\";\n  index: number;\n  value: string;\n}\n\n/**\n * Tokenize input string.\n */\nfunction lexer(str: string): LexToken[] {\n  const tokens: LexToken[] = [];\n  let i = 0;\n\n  while (i < str.length) {\n    const char = str[i];\n\n    if (char === \"*\" || char === \"+\" || char === \"?\") {\n      tokens.push({ type: \"MODIFIER\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"\\\\\") {\n      tokens.push({ type: \"ESCAPED_CHAR\", index: i++, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"{\") {\n      tokens.push({ type: \"OPEN\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \"}\") {\n      tokens.push({ type: \"CLOSE\", index: i, value: str[i++] });\n      continue;\n    }\n\n    if (char === \":\") {\n      let name = \"\";\n      let j = i + 1;\n\n      while (j < str.length) {\n        const code = str.charCodeAt(j);\n\n        if (\n          // `0-9`\n          (code >= 48 && code <= 57) ||\n          // `A-Z`\n          (code >= 65 && code <= 90) ||\n          // `a-z`\n          (code >= 97 && code <= 122) ||\n          // `_`\n          code === 95\n        ) {\n          name += str[j++];\n          continue;\n        }\n\n        break;\n      }\n\n      if (!name) throw new TypeError(`Missing parameter name at ${i}`);\n\n      tokens.push({ type: \"NAME\", index: i, value: name });\n      i = j;\n      continue;\n    }\n\n    if (char === \"(\") {\n      let count = 1;\n      let pattern = \"\";\n      let j = i + 1;\n\n      if (str[j] === \"?\") {\n        throw new TypeError(`Pattern cannot start with \"?\" at ${j}`);\n      }\n\n      while (j < str.length) {\n        if (str[j] === \"\\\\\") {\n          pattern += str[j++] + str[j++];\n          continue;\n        }\n\n        if (str[j] === \")\") {\n          count--;\n          if (count === 0) {\n            j++;\n            break;\n          }\n        } else if (str[j] === \"(\") {\n          count++;\n          if (str[j + 1] !== \"?\") {\n            throw new TypeError(`Capturing groups are not allowed at ${j}`);\n          }\n        }\n\n        pattern += str[j++];\n      }\n\n      if (count) throw new TypeError(`Unbalanced pattern at ${i}`);\n      if (!pattern) throw new TypeError(`Missing pattern at ${i}`);\n\n      tokens.push({ type: \"PATTERN\", index: i, value: pattern });\n      i = j;\n      continue;\n    }\n\n    tokens.push({ type: \"CHAR\", index: i, value: str[i++] });\n  }\n\n  tokens.push({ type: \"END\", index: i, value: \"\" });\n\n  return tokens;\n}\n\nexport interface ParseOptions {\n  /**\n   * Set the default delimiter for repeat parameters. (default: `'/'`)\n   */\n  delimiter?: string;\n  /**\n   * List of characters to automatically consider prefixes when parsing.\n   */\n  prefixes?: string;\n}\n\n/**\n * Parse a string for the raw tokens.\n */\nexport function parse(str: string, options: ParseOptions = {}): Token[] {\n  const tokens = lexer(str);\n  const { prefixes = \"./\", delimiter = \"/#?\" } = options;\n  const result: Token[] = [];\n  let key = 0;\n  let i = 0;\n  let path = \"\";\n\n  const tryConsume = (type: LexToken[\"type\"]): string | undefined => {\n    if (i < tokens.length && tokens[i].type === type) return tokens[i++].value;\n  };\n\n  const mustConsume = (type: LexToken[\"type\"]): string => {\n    const value = tryConsume(type);\n    if (value !== undefined) return value;\n    const { type: nextType, index } = tokens[i];\n    throw new TypeError(`Unexpected ${nextType} at ${index}, expected ${type}`);\n  };\n\n  const consumeText = (): string => {\n    let result = \"\";\n    let value: string | undefined;\n    while ((value = tryConsume(\"CHAR\") || tryConsume(\"ESCAPED_CHAR\"))) {\n      result += value;\n    }\n    return result;\n  };\n\n  const isSafe = (value: string): boolean => {\n    for (const char of delimiter) if (value.indexOf(char) > -1) return true;\n    return false;\n  };\n\n  const safePattern = (prefix: string) => {\n    const prev = result[result.length - 1];\n    const prevText = prefix || (prev && typeof prev === \"string\" ? prev : \"\");\n\n    if (prev && !prevText) {\n      throw new TypeError(\n        `Must have text between two parameters, missing text after \"${(prev as Key).name}\"`,\n      );\n    }\n\n    if (!prevText || isSafe(prevText)) return `[^${escapeString(delimiter)}]+?`;\n    return `(?:(?!${escapeString(prevText)})[^${escapeString(delimiter)}])+?`;\n  };\n\n  while (i < tokens.length) {\n    const char = tryConsume(\"CHAR\");\n    const name = tryConsume(\"NAME\");\n    const pattern = tryConsume(\"PATTERN\");\n\n    if (name || pattern) {\n      let prefix = char || \"\";\n\n      if (prefixes.indexOf(prefix) === -1) {\n        path += prefix;\n        prefix = \"\";\n      }\n\n      if (path) {\n        result.push(path);\n        path = \"\";\n      }\n\n      result.push({\n        name: name || key++,\n        prefix,\n        suffix: \"\",\n        pattern: pattern || safePattern(prefix),\n        modifier: tryConsume(\"MODIFIER\") || \"\",\n      });\n      continue;\n    }\n\n    const value = char || tryConsume(\"ESCAPED_CHAR\");\n    if (value) {\n      path += value;\n      continue;\n    }\n\n    if (path) {\n      result.push(path);\n      path = \"\";\n    }\n\n    const open = tryConsume(\"OPEN\");\n    if (open) {\n      const prefix = consumeText();\n      const name = tryConsume(\"NAME\") || \"\";\n      const pattern = tryConsume(\"PATTERN\") || \"\";\n      const suffix = consumeText();\n\n      mustConsume(\"CLOSE\");\n\n      result.push({\n        name: name || (pattern ? key++ : \"\"),\n        pattern: name && !pattern ? safePattern(prefix) : pattern,\n        prefix,\n        suffix,\n        modifier: tryConsume(\"MODIFIER\") || \"\",\n      });\n      continue;\n    }\n\n    mustConsume(\"END\");\n  }\n\n  return result;\n}\n\nexport interface TokensToFunctionOptions {\n  /**\n   * When `true` the regexp will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * Function for encoding input strings for output.\n   */\n  encode?: (value: string, token: Key) => string;\n  /**\n   * When `false` the function can produce an invalid (unmatched) path. (default: `true`)\n   */\n  validate?: boolean;\n}\n\n/**\n * Compile a string to a template function for the path.\n */\nexport function compile<P extends object = object>(\n  str: string,\n  options?: ParseOptions & TokensToFunctionOptions,\n) {\n  return tokensToFunction<P>(parse(str, options), options);\n}\n\nexport type PathFunction<P extends object = object> = (data?: P) => string;\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nexport function tokensToFunction<P extends object = object>(\n  tokens: Token[],\n  options: TokensToFunctionOptions = {},\n): PathFunction<P> {\n  const reFlags = flags(options);\n  const { encode = (x: string) => x, validate = true } = options;\n\n  // Compile all the tokens into regexps.\n  const matches = tokens.map((token) => {\n    if (typeof token === \"object\") {\n      return new RegExp(`^(?:${token.pattern})$`, reFlags);\n    }\n  });\n\n  return (data: Record<string, any> | null | undefined) => {\n    let path = \"\";\n\n    for (let i = 0; i < tokens.length; i++) {\n      const token = tokens[i];\n\n      if (typeof token === \"string\") {\n        path += token;\n        continue;\n      }\n\n      const value = data ? data[token.name] : undefined;\n      const optional = token.modifier === \"?\" || token.modifier === \"*\";\n      const repeat = token.modifier === \"*\" || token.modifier === \"+\";\n\n      if (Array.isArray(value)) {\n        if (!repeat) {\n          throw new TypeError(\n            `Expected \"${token.name}\" to not repeat, but got an array`,\n          );\n        }\n\n        if (value.length === 0) {\n          if (optional) continue;\n\n          throw new TypeError(`Expected \"${token.name}\" to not be empty`);\n        }\n\n        for (let j = 0; j < value.length; j++) {\n          const segment = encode(value[j], token);\n\n          if (validate && !(matches[i] as RegExp).test(segment)) {\n            throw new TypeError(\n              `Expected all \"${token.name}\" to match \"${token.pattern}\", but got \"${segment}\"`,\n            );\n          }\n\n          path += token.prefix + segment + token.suffix;\n        }\n\n        continue;\n      }\n\n      if (typeof value === \"string\" || typeof value === \"number\") {\n        const segment = encode(String(value), token);\n\n        if (validate && !(matches[i] as RegExp).test(segment)) {\n          throw new TypeError(\n            `Expected \"${token.name}\" to match \"${token.pattern}\", but got \"${segment}\"`,\n          );\n        }\n\n        path += token.prefix + segment + token.suffix;\n        continue;\n      }\n\n      if (optional) continue;\n\n      const typeOfMessage = repeat ? \"an array\" : \"a string\";\n      throw new TypeError(`Expected \"${token.name}\" to be ${typeOfMessage}`);\n    }\n\n    return path;\n  };\n}\n\nexport interface RegexpToFunctionOptions {\n  /**\n   * Function for decoding strings for params.\n   */\n  decode?: (value: string, token: Key) => string;\n}\n\n/**\n * A match result contains data about the path match.\n */\nexport interface MatchResult<P extends object = object> {\n  path: string;\n  index: number;\n  params: P;\n}\n\n/**\n * A match is either `false` (no match) or a match result.\n */\nexport type Match<P extends object = object> = false | MatchResult<P>;\n\n/**\n * The match function takes a string and returns whether it matched the path.\n */\nexport type MatchFunction<P extends object = object> = (\n  path: string,\n) => Match<P>;\n\n/**\n * Create path match function from `path-to-regexp` spec.\n */\nexport function match<P extends object = object>(\n  str: Path,\n  options?: ParseOptions & TokensToRegexpOptions & RegexpToFunctionOptions,\n) {\n  const keys: Key[] = [];\n  const re = pathToRegexp(str, keys, options);\n  return regexpToFunction<P>(re, keys, options);\n}\n\n/**\n * Create a path match function from `path-to-regexp` output.\n */\nexport function regexpToFunction<P extends object = object>(\n  re: RegExp,\n  keys: Key[],\n  options: RegexpToFunctionOptions = {},\n): MatchFunction<P> {\n  const { decode = (x: string) => x } = options;\n\n  return function (pathname: string) {\n    const m = re.exec(pathname);\n    if (!m) return false;\n\n    const { 0: path, index } = m;\n    const params = Object.create(null);\n\n    for (let i = 1; i < m.length; i++) {\n      if (m[i] === undefined) continue;\n\n      const key = keys[i - 1];\n\n      if (key.modifier === \"*\" || key.modifier === \"+\") {\n        params[key.name] = m[i].split(key.prefix + key.suffix).map((value) => {\n          return decode(value, key);\n        });\n      } else {\n        params[key.name] = decode(m[i], key);\n      }\n    }\n\n    return { path, index, params };\n  };\n}\n\n/**\n * Escape a regular expression string.\n */\nfunction escapeString(str: string) {\n  return str.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, \"\\\\$1\");\n}\n\n/**\n * Get the flags for a regexp from the options.\n */\nfunction flags(options?: { sensitive?: boolean }) {\n  return options && options.sensitive ? \"\" : \"i\";\n}\n\n/**\n * Metadata about a key.\n */\nexport interface Key {\n  name: string | number;\n  prefix: string;\n  suffix: string;\n  pattern: string;\n  modifier: string;\n}\n\n/**\n * A token is a string (nothing special) or key metadata (capture group).\n */\nexport type Token = string | Key;\n\n/**\n * Pull out keys from a regexp.\n */\nfunction regexpToRegexp(path: RegExp, keys?: Key[]): RegExp {\n  if (!keys) return path;\n\n  const groupsRegex = /\\((?:\\?<(.*?)>)?(?!\\?)/g;\n\n  let index = 0;\n  let execResult = groupsRegex.exec(path.source);\n  while (execResult) {\n    keys.push({\n      // Use parenthesized substring match if available, index otherwise\n      name: execResult[1] || index++,\n      prefix: \"\",\n      suffix: \"\",\n      modifier: \"\",\n      pattern: \"\",\n    });\n    execResult = groupsRegex.exec(path.source);\n  }\n\n  return path;\n}\n\n/**\n * Transform an array into a regexp.\n */\nfunction arrayToRegexp(\n  paths: Array<string | RegExp>,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions,\n): RegExp {\n  const parts = paths.map((path) => pathToRegexp(path, keys, options).source);\n  return new RegExp(`(?:${parts.join(\"|\")})`, flags(options));\n}\n\n/**\n * Create a path regexp from string input.\n */\nfunction stringToRegexp(\n  path: string,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions,\n) {\n  return tokensToRegexp(parse(path, options), keys, options);\n}\n\nexport interface TokensToRegexpOptions {\n  /**\n   * When `true` the regexp will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * When `true` the regexp won't allow an optional trailing delimiter to match. (default: `false`)\n   */\n  strict?: boolean;\n  /**\n   * When `true` the regexp will match to the end of the string. (default: `true`)\n   */\n  end?: boolean;\n  /**\n   * When `true` the regexp will match from the beginning of the string. (default: `true`)\n   */\n  start?: boolean;\n  /**\n   * Sets the final character for non-ending optimistic matches. (default: `/`)\n   */\n  delimiter?: string;\n  /**\n   * List of characters that can also be \"end\" characters.\n   */\n  endsWith?: string;\n  /**\n   * Encode path tokens for use in the `RegExp`.\n   */\n  encode?: (value: string) => string;\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n */\nexport function tokensToRegexp(\n  tokens: Token[],\n  keys?: Key[],\n  options: TokensToRegexpOptions = {},\n) {\n  const {\n    strict = false,\n    start = true,\n    end = true,\n    encode = (x: string) => x,\n    delimiter = \"/#?\",\n    endsWith = \"\",\n  } = options;\n  const endsWithRe = `[${escapeString(endsWith)}]|$`;\n  const delimiterRe = `[${escapeString(delimiter)}]`;\n  let route = start ? \"^\" : \"\";\n\n  // Iterate over the tokens and create our regexp string.\n  for (const token of tokens) {\n    if (typeof token === \"string\") {\n      route += escapeString(encode(token));\n    } else {\n      const prefix = escapeString(encode(token.prefix));\n      const suffix = escapeString(encode(token.suffix));\n\n      if (token.pattern) {\n        if (keys) keys.push(token);\n\n        if (prefix || suffix) {\n          if (token.modifier === \"+\" || token.modifier === \"*\") {\n            const mod = token.modifier === \"*\" ? \"?\" : \"\";\n            route += `(?:${prefix}((?:${token.pattern})(?:${suffix}${prefix}(?:${token.pattern}))*)${suffix})${mod}`;\n          } else {\n            route += `(?:${prefix}(${token.pattern})${suffix})${token.modifier}`;\n          }\n        } else {\n          if (token.modifier === \"+\" || token.modifier === \"*\") {\n            throw new TypeError(\n              `Can not repeat \"${token.name}\" without a prefix and suffix`,\n            );\n          }\n\n          route += `(${token.pattern})${token.modifier}`;\n        }\n      } else {\n        route += `(?:${prefix}${suffix})${token.modifier}`;\n      }\n    }\n  }\n\n  if (end) {\n    if (!strict) route += `${delimiterRe}?`;\n\n    route += !options.endsWith ? \"$\" : `(?=${endsWithRe})`;\n  } else {\n    const endToken = tokens[tokens.length - 1];\n    const isEndDelimited =\n      typeof endToken === \"string\"\n        ? delimiterRe.indexOf(endToken[endToken.length - 1]) > -1\n        : endToken === undefined;\n\n    if (!strict) {\n      route += `(?:${delimiterRe}(?=${endsWithRe}))?`;\n    }\n\n    if (!isEndDelimited) {\n      route += `(?=${delimiterRe}|${endsWithRe})`;\n    }\n  }\n\n  return new RegExp(route, flags(options));\n}\n\n/**\n * Supported `path-to-regexp` input types.\n */\nexport type Path = string | RegExp | Array<string | RegExp>;\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n */\nexport function pathToRegexp(\n  path: Path,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions,\n) {\n  if (path instanceof RegExp) return regexpToRegexp(path, keys);\n  if (Array.isArray(path)) return arrayToRegexp(path, keys, options);\n  return stringToRegexp(path, keys, options);\n}\n", "const encoder = new TextEncoder()\n\nexport function encodeBuffer(text: string): Uint8Array {\n  return encoder.encode(text)\n}\n\nexport function decodeBuffer(buffer: <PERSON>rrayBuffer, encoding?: string): string {\n  const decoder = new TextDecoder(encoding)\n  return decoder.decode(buffer)\n}\n\n/**\n * Create an `ArrayBuffer` from the given `Uint8Array`.\n * Takes the byte offset into account to produce the right buffer\n * in the case when the buffer is bigger than the data view.\n */\nexport function toArrayBuffer(array: Uint8Array): ArrayBuffer {\n  return array.buffer.slice(\n    array.byteOffset,\n    array.byteOffset + array.byteLength\n  )\n}\n", "import type { RequestController } from './RequestController'\n\nexport const IS_PATCHED_MODULE: unique symbol = Symbol('isPatchedModule')\n\n/**\n * @note Export `RequestController` as a type only.\n * It's never meant to be created in the userland.\n */\nexport type { RequestController }\n\nexport type RequestCredentials = 'omit' | 'include' | 'same-origin'\n\nexport type HttpRequestEventMap = {\n  request: [\n    args: {\n      request: Request\n      requestId: string\n      controller: RequestController\n    }\n  ]\n  response: [\n    args: {\n      response: Response\n      isMockedResponse: boolean\n      request: Request\n      requestId: string\n    }\n  ]\n  unhandledException: [\n    args: {\n      error: unknown\n      request: Request\n      requestId: string\n      controller: RequestController\n    }\n  ]\n}\n", "/**\n * Returns a boolean indicating whether the given URL string\n * can be parsed into a `URL` instance.\n * A substitute for `URL.canParse()` for Node.js 18.\n */\nexport function canParseUrl(url: string): boolean {\n  try {\n    new URL(url)\n    return true\n  } catch (_error) {\n    return false\n  }\n}\n", "/**\n * Returns the value behind the symbol with the given name.\n */\nexport function getValueBySymbol<T>(\n  symbolName: string,\n  source: object\n): T | undefined {\n  const ownSymbols = Object.getOwnPropertySymbols(source)\n\n  const symbol = ownSymbols.find((symbol) => {\n    return symbol.description === symbolName\n  })\n\n  if (symbol) {\n    return Reflect.get(source, symbol)\n  }\n\n  return\n}\n", "import { canParseUrl } from './canParseUrl'\nimport { getValueBySymbol } from './getValueBySymbol'\n\nexport interface FetchResponseInit extends ResponseInit {\n  url?: string\n}\n\ninterface UndiciFetchInternalState {\n  aborted: boolean\n  rangeRequested: boolean\n  timingAllowPassed: boolean\n  requestIncludesCredentials: boolean\n  type: ResponseType\n  status: number\n  statusText: string\n  timingInfo: unknown\n  cacheState: unknown\n  headersList: Record<symbol, Map<string, unknown>>\n  urlList: Array<URL>\n  body?: {\n    stream: ReadableStream\n    source: unknown\n    length: number\n  }\n}\n\nexport class FetchResponse extends Response {\n  /**\n   * Response status codes for responses that cannot have body.\n   * @see https://fetch.spec.whatwg.org/#statuses\n   */\n  static readonly STATUS_CODES_WITHOUT_BODY = [101, 103, 204, 205, 304]\n\n  static readonly STATUS_CODES_WITH_REDIRECT = [301, 302, 303, 307, 308]\n\n  static isConfigurableStatusCode(status: number): boolean {\n    return status >= 200 && status <= 599\n  }\n\n  static isRedirectResponse(status: number): boolean {\n    return FetchResponse.STATUS_CODES_WITH_REDIRECT.includes(status)\n  }\n\n  /**\n   * Returns a boolean indicating whether the given response status\n   * code represents a response that can have a body.\n   */\n  static isResponseWithBody(status: number): boolean {\n    return !FetchResponse.STATUS_CODES_WITHOUT_BODY.includes(status)\n  }\n\n  static setUrl(url: string | undefined, response: Response): void {\n    if (!url || url === 'about:' || !canParseUrl(url)) {\n      return\n    }\n\n    const state = getValueBySymbol<UndiciFetchInternalState>('state', response)\n\n    if (state) {\n      // In Undici, push the URL to the internal list of URLs.\n      // This will respect the `response.url` getter logic correctly.\n      state.urlList.push(new URL(url))\n    } else {\n      // In other libraries, redefine the `url` property directly.\n      Object.defineProperty(response, 'url', {\n        value: url,\n        enumerable: true,\n        configurable: true,\n        writable: false,\n      })\n    }\n  }\n\n  /**\n   * Parses the given raw HTTP headers into a Fetch API `Headers` instance.\n   */\n  static parseRawHeaders(rawHeaders: Array<string>): Headers {\n    const headers = new Headers()\n    for (let line = 0; line < rawHeaders.length; line += 2) {\n      headers.append(rawHeaders[line], rawHeaders[line + 1])\n    }\n    return headers\n  }\n\n  constructor(body?: BodyInit | null, init: FetchResponseInit = {}) {\n    const status = init.status ?? 200\n    const safeStatus = FetchResponse.isConfigurableStatusCode(status)\n      ? status\n      : 200\n    const finalBody = FetchResponse.isResponseWithBody(status) ? body : null\n\n    super(finalBody, {\n      status: safeStatus,\n      statusText: init.statusText,\n      headers: init.headers,\n    })\n\n    if (status !== safeStatus) {\n      /**\n       * @note Undici keeps an internal \"Symbol(state)\" that holds\n       * the actual value of response status. Update that in Node.js.\n       */\n      const state = getValueBySymbol<UndiciFetchInternalState>('state', this)\n\n      if (state) {\n        state.status = status\n      } else {\n        Object.defineProperty(this, 'status', {\n          value: status,\n          enumerable: true,\n          configurable: true,\n          writable: false,\n        })\n      }\n    }\n\n    FetchResponse.setUrl(init.url, this)\n  }\n}\n", "const kRawRequest = Symbol('kRawRequest')\n\n/**\n * Returns a raw request instance associated with this request.\n *\n * @example\n * interceptor.on('request', ({ request }) => {\n *   const rawRequest = getRawRequest(request)\n *\n *   if (rawRequest instanceof http.ClientRequest) {\n *     console.log(rawRequest.rawHeaders)\n *   }\n * })\n */\nexport function getRawRequest(request: Request): unknown | undefined {\n  return Reflect.get(request, kRawRequest)\n}\n\nexport function setRawRequest(request: Request, rawRequest: unknown): void {\n  Reflect.set(request, kRawRequest, rawRequest)\n}\n", "import { EventMap, Listener } from 'strict-event-emitter'\nimport { Interceptor, ExtractEventNames } from './Interceptor'\n\nexport interface BatchInterceptorOptions<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> {\n  name: string\n  interceptors: InterceptorList\n}\n\nexport type ExtractEventMapType<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>\n> = InterceptorList extends ReadonlyArray<infer InterceptorType>\n  ? InterceptorType extends Interceptor<infer EventMap>\n    ? EventMap\n    : never\n  : never\n\n/**\n * A batch interceptor that exposes a single interface\n * to apply and operate with multiple interceptors at once.\n */\nexport class BatchInterceptor<\n  InterceptorList extends ReadonlyArray<Interceptor<any>>,\n  Events extends EventMap = ExtractEventMapType<InterceptorList>\n> extends Interceptor<Events> {\n  static symbol: symbol\n\n  private interceptors: InterceptorList\n\n  constructor(options: BatchInterceptorOptions<InterceptorList>) {\n    BatchInterceptor.symbol = Symbol(options.name)\n    super(BatchInterceptor.symbol)\n    this.interceptors = options.interceptors\n  }\n\n  protected setup() {\n    const logger = this.logger.extend('setup')\n\n    logger.info('applying all %d interceptors...', this.interceptors.length)\n\n    for (const interceptor of this.interceptors) {\n      logger.info('applying \"%s\" interceptor...', interceptor.constructor.name)\n      interceptor.apply()\n\n      logger.info('adding interceptor dispose subscription')\n      this.subscriptions.push(() => interceptor.dispose())\n    }\n  }\n\n  public on<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    // Instead of adding a listener to the batch interceptor,\n    // propagate the listener to each of the individual interceptors.\n    for (const interceptor of this.interceptors) {\n      interceptor.on(event, listener)\n    }\n\n    return this\n  }\n\n  public once<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.once(event, listener)\n    }\n\n    return this\n  }\n\n  public off<EventName extends ExtractEventNames<Events>>(\n    event: EventName,\n    listener: Listener<Events[EventName]>\n  ): this {\n    for (const interceptor of this.interceptors) {\n      interceptor.off(event, listener)\n    }\n\n    return this\n  }\n\n  public removeAllListeners<EventName extends ExtractEventNames<Events>>(\n    event?: EventName | undefined\n  ): this {\n    for (const interceptors of this.interceptors) {\n      interceptors.removeAllListeners(event)\n    }\n\n    return this\n  }\n}\n", "/**\n * Removes query parameters and hashes from a given URL.\n */\nexport function getCleanUrl(url: URL, isAbsolute: boolean = true): string {\n  return [isAbsolute && url.origin, url.pathname].filter(Boolean).join('')\n}\n", "const REDUNDANT_CHARACTERS_EXP = /[\\?|#].*$/g\n\nexport function getSearchParams(path: string) {\n  return new URL(`/${path}`, 'http://localhost').searchParams\n}\n\n/**\n * Removes search parameters and the fragment\n * from a given URL string.\n */\nexport function cleanUrl(path: string): string {\n  // If the path ends with an optional path parameter,\n  // return it as-is.\n  if (path.endsWith('?')) {\n    return path\n  }\n\n  // Otherwise, remove the search and fragment from it.\n  return path.replace(REDUNDANT_CHARACTERS_EXP, '')\n}\n", "/**\n * Determines if the given URL string is an absolute URL.\n */\nexport function isAbsoluteUrl(url: string): boolean {\n  return /^([a-z][a-z\\d\\+\\-\\.]*:)?\\/\\//i.test(url)\n}\n", "import { isAbsoluteUrl } from './isAbsoluteUrl'\n\n/**\n * Returns an absolute URL based on the given path.\n */\nexport function getAbsoluteUrl(path: string, baseUrl?: string): string {\n  // already absolute URL\n  if (isAbsoluteUrl(path)) {\n    return path\n  }\n\n  // Ignore path with pattern start with *\n  if (path.startsWith('*')) {\n    return path\n  }\n\n  // Resolve a relative request URL against a given custom \"baseUrl\"\n  // or the document baseURI (in the case of browser/browser-like environments).\n  const origin = baseUrl || (typeof location !== 'undefined' && location.href)\n\n  return origin\n    ? // Encode and decode the path to preserve escaped characters.\n      decodeURI(new URL(encodeURI(path), origin).href)\n    : path\n}\n", "import type { Path } from './matchRequestUrl'\nimport { cleanUrl } from '../url/cleanUrl'\nimport { getAbsoluteUrl } from '../url/getAbsoluteUrl'\n\n/**\n * Normalizes a given request handler path:\n * - Preserves RegExp.\n * - Removes query parameters and hashes.\n * - Rebases relative URLs against the \"baseUrl\" or the current location.\n * - Preserves relative URLs in Node.js, unless specified otherwise.\n * - Preserves optional path parameters.\n */\nexport function normalizePath(path: Path, baseUrl?: string): Path {\n  // RegExp paths do not need normalization.\n  if (path instanceof RegExp) {\n    return path\n  }\n\n  const maybeAbsoluteUrl = getAbsoluteUrl(path, baseUrl)\n\n  return cleanUrl(maybeAbsoluteUrl)\n}\n", "import { match } from 'path-to-regexp'\nimport { getCleanUrl } from '@mswjs/interceptors'\nimport { normalizePath } from './normalizePath'\n\nexport type Path = string | RegExp\nexport type PathParams<KeyType extends keyof any = string> = {\n  [ParamName in KeyType]?: string | ReadonlyArray<string>\n}\n\nexport interface Match {\n  matches: boolean\n  params?: PathParams\n}\n\n/**\n * Coerce a path supported by MSW into a path\n * supported by \"path-to-regexp\".\n */\nexport function coercePath(path: string): string {\n  return (\n    path\n      /**\n       * Replace wildcards (\"*\") with unnamed capturing groups\n       * because \"path-to-regexp\" doesn't support wildcards.\n       * Ignore path parameter' modifiers (i.e. \":name*\").\n       */\n      .replace(\n        /([:a-zA-Z_-]*)(\\*{1,2})+/g,\n        (_, parameterName: string | undefined, wildcard: string) => {\n          const expression = '(.*)'\n\n          if (!parameterName) {\n            return expression\n          }\n\n          return parameterName.startsWith(':')\n            ? `${parameterName}${wildcard}`\n            : `${parameterName}${expression}`\n        },\n      )\n      /**\n       * Escape the port so that \"path-to-regexp\" can match\n       * absolute URLs including port numbers.\n       */\n      .replace(/([^\\/])(:)(?=\\d+)/, '$1\\\\$2')\n      /**\n       * Escape the protocol so that \"path-to-regexp\" could match\n       * absolute URL.\n       * @see https://github.com/pillarjs/path-to-regexp/issues/259\n       */\n      .replace(/^([^\\/]+)(:)(?=\\/\\/)/, '$1\\\\$2')\n  )\n}\n\n/**\n * Returns the result of matching given request URL against a mask.\n */\nexport function matchRequestUrl(url: URL, path: Path, baseUrl?: string): Match {\n  const normalizedPath = normalizePath(path, baseUrl)\n  const cleanPath =\n    typeof normalizedPath === 'string'\n      ? coercePath(normalizedPath)\n      : normalizedPath\n\n  const cleanUrl = getCleanUrl(url)\n  const result = match(cleanPath, { decode: decodeURIComponent })(cleanUrl)\n  const params = (result && (result.params as PathParams)) || {}\n\n  return {\n    matches: result !== false,\n    params,\n  }\n}\n\nexport function isPath(value: unknown): value is Path {\n  return typeof value === 'string' || value instanceof RegExp\n}\n", "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n// node_modules/cookie/index.js\nvar require_cookie = __commonJS({\n  \"node_modules/cookie/index.js\"(exports) {\n    \"use strict\";\n    exports.parse = parse;\n    exports.serialize = serialize;\n    var __toString = Object.prototype.toString;\n    var __hasOwnProperty = Object.prototype.hasOwnProperty;\n    var cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n    var cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n    var domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n    var pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n    function parse(str, opt) {\n      if (typeof str !== \"string\") {\n        throw new TypeError(\"argument str must be a string\");\n      }\n      var obj = {};\n      var len = str.length;\n      if (len < 2) return obj;\n      var dec = opt && opt.decode || decode;\n      var index = 0;\n      var eqIdx = 0;\n      var endIdx = 0;\n      do {\n        eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1) break;\n        endIdx = str.indexOf(\";\", index);\n        if (endIdx === -1) {\n          endIdx = len;\n        } else if (eqIdx > endIdx) {\n          index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n          continue;\n        }\n        var keyStartIdx = startIndex(str, index, eqIdx);\n        var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n        var key = str.slice(keyStartIdx, keyEndIdx);\n        if (!__hasOwnProperty.call(obj, key)) {\n          var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n          var valEndIdx = endIndex(str, endIdx, valStartIdx);\n          if (str.charCodeAt(valStartIdx) === 34 && str.charCodeAt(valEndIdx - 1) === 34) {\n            valStartIdx++;\n            valEndIdx--;\n          }\n          var val = str.slice(valStartIdx, valEndIdx);\n          obj[key] = tryDecode(val, dec);\n        }\n        index = endIdx + 1;\n      } while (index < len);\n      return obj;\n    }\n    function startIndex(str, index, max) {\n      do {\n        var code = str.charCodeAt(index);\n        if (code !== 32 && code !== 9) return index;\n      } while (++index < max);\n      return max;\n    }\n    function endIndex(str, index, min) {\n      while (index > min) {\n        var code = str.charCodeAt(--index);\n        if (code !== 32 && code !== 9) return index + 1;\n      }\n      return min;\n    }\n    function serialize(name, val, opt) {\n      var enc = opt && opt.encode || encodeURIComponent;\n      if (typeof enc !== \"function\") {\n        throw new TypeError(\"option encode is invalid\");\n      }\n      if (!cookieNameRegExp.test(name)) {\n        throw new TypeError(\"argument name is invalid\");\n      }\n      var value = enc(val);\n      if (!cookieValueRegExp.test(value)) {\n        throw new TypeError(\"argument val is invalid\");\n      }\n      var str = name + \"=\" + value;\n      if (!opt) return str;\n      if (null != opt.maxAge) {\n        var maxAge = Math.floor(opt.maxAge);\n        if (!isFinite(maxAge)) {\n          throw new TypeError(\"option maxAge is invalid\");\n        }\n        str += \"; Max-Age=\" + maxAge;\n      }\n      if (opt.domain) {\n        if (!domainValueRegExp.test(opt.domain)) {\n          throw new TypeError(\"option domain is invalid\");\n        }\n        str += \"; Domain=\" + opt.domain;\n      }\n      if (opt.path) {\n        if (!pathValueRegExp.test(opt.path)) {\n          throw new TypeError(\"option path is invalid\");\n        }\n        str += \"; Path=\" + opt.path;\n      }\n      if (opt.expires) {\n        var expires = opt.expires;\n        if (!isDate(expires) || isNaN(expires.valueOf())) {\n          throw new TypeError(\"option expires is invalid\");\n        }\n        str += \"; Expires=\" + expires.toUTCString();\n      }\n      if (opt.httpOnly) {\n        str += \"; HttpOnly\";\n      }\n      if (opt.secure) {\n        str += \"; Secure\";\n      }\n      if (opt.partitioned) {\n        str += \"; Partitioned\";\n      }\n      if (opt.priority) {\n        var priority = typeof opt.priority === \"string\" ? opt.priority.toLowerCase() : opt.priority;\n        switch (priority) {\n          case \"low\":\n            str += \"; Priority=Low\";\n            break;\n          case \"medium\":\n            str += \"; Priority=Medium\";\n            break;\n          case \"high\":\n            str += \"; Priority=High\";\n            break;\n          default:\n            throw new TypeError(\"option priority is invalid\");\n        }\n      }\n      if (opt.sameSite) {\n        var sameSite = typeof opt.sameSite === \"string\" ? opt.sameSite.toLowerCase() : opt.sameSite;\n        switch (sameSite) {\n          case true:\n            str += \"; SameSite=Strict\";\n            break;\n          case \"lax\":\n            str += \"; SameSite=Lax\";\n            break;\n          case \"strict\":\n            str += \"; SameSite=Strict\";\n            break;\n          case \"none\":\n            str += \"; SameSite=None\";\n            break;\n          default:\n            throw new TypeError(\"option sameSite is invalid\");\n        }\n      }\n      return str;\n    }\n    function decode(str) {\n      return str.indexOf(\"%\") !== -1 ? decodeURIComponent(str) : str;\n    }\n    function isDate(val) {\n      return __toString.call(val) === \"[object Date]\";\n    }\n    function tryDecode(str, decode2) {\n      try {\n        return decode2(str);\n      } catch (e) {\n        return str;\n      }\n    }\n  }\n});\n\n// source.js\nvar import_cookie = __toESM(require_cookie(), 1);\nvar source_default = import_cookie.default;\nexport {\n  source_default as default\n};\n/*! Bundled license information:\n\ncookie/index.js:\n  (*!\n   * cookie\n   * Copyright(c) 2012-2014 Roman Shtylman\n   * Copyright(c) 2015 Douglas Christopher Wilson\n   * MIT Licensed\n   *)\n*/\n", "import cookieUtils from '@bundled-es-modules/cookie'\nimport { cookieStore } from '../cookieStore'\n\nfunction parseCookies(input: string): Record<string, string> {\n  const parsedCookies = cookieUtils.parse(input)\n  const cookies: Record<string, string> = {}\n\n  for (const cookieName in parsedCookies) {\n    if (typeof parsedCookies[cookieName] !== 'undefined') {\n      cookies[cookieName] = parsedCookies[cookieName]\n    }\n  }\n\n  return cookies\n}\n\nfunction getAllDocumentCookies() {\n  return parseCookies(document.cookie)\n}\n\nfunction getDocumentCookies(request: Request): Record<string, string> {\n  if (typeof document === 'undefined' || typeof location === 'undefined') {\n    return {}\n  }\n\n  switch (request.credentials) {\n    case 'same-origin': {\n      const requestUrl = new URL(request.url)\n\n      // Return document cookies only when requested a resource\n      // from the same origin as the current document.\n      return location.origin === requestUrl.origin\n        ? getAllDocumentCookies()\n        : {}\n    }\n\n    case 'include': {\n      // Return all document cookies.\n      return getAllDocumentCookies()\n    }\n\n    default: {\n      return {}\n    }\n  }\n}\n\nexport function getAllRequestCookies(request: Request): Record<string, string> {\n  /**\n   * @note While the \"cookie\" header is a forbidden header field\n   * in the browser, you can read it in Node.js. We need to respect\n   * it for mocking in Node.js.\n   */\n  const requestCookieHeader = request.headers.get('cookie')\n  const cookiesFromHeaders = requestCookieHeader\n    ? parseCookies(requestCookieHeader)\n    : {}\n\n  const cookiesFromDocument = getDocumentCookies(request)\n\n  // Forward the document cookies to the request headers.\n  for (const name in cookiesFromDocument) {\n    request.headers.append(\n      'cookie',\n      cookieUtils.serialize(name, cookiesFromDocument[name]),\n    )\n  }\n\n  const cookiesFromStore = cookieStore.getCookiesSync(request.url)\n  const storedCookiesObject = Object.fromEntries(\n    cookiesFromStore.map((cookie) => [cookie.key, cookie.value]),\n  )\n\n  // Forward the raw stored cookies to request headers\n  // so they contain metadata like \"expires\", \"secure\", etc.\n  for (const cookie of cookiesFromStore) {\n    request.headers.append('cookie', cookie.toString())\n  }\n\n  return {\n    ...cookiesFromDocument,\n    ...storedCookiesObject,\n    ...cookiesFromHeaders,\n  }\n}\n", "import { ResponseResolutionContext } from '../utils/executeHandlers'\nimport { devUtils } from '../utils/internal/devUtils'\nimport { isStringEqual } from '../utils/internal/isStringEqual'\nimport { getStatusCodeColor } from '../utils/logging/getStatusCodeColor'\nimport { getTimestamp } from '../utils/logging/getTimestamp'\nimport { serializeRequest } from '../utils/logging/serializeRequest'\nimport { serializeResponse } from '../utils/logging/serializeResponse'\nimport {\n  matchRequestUrl,\n  Match,\n  Path,\n  PathParams,\n} from '../utils/matching/matchRequestUrl'\nimport { toPublicUrl } from '../utils/request/toPublicUrl'\nimport { getAllRequestCookies } from '../utils/request/getRequestCookies'\nimport { cleanUrl, getSearchParams } from '../utils/url/cleanUrl'\nimport {\n  RequestHandler,\n  RequestHandlerDefaultInfo,\n  RequestHandlerOptions,\n  ResponseResolver,\n} from './RequestHandler'\n\ntype HttpHandlerMethod = string | RegExp\n\nexport interface HttpHandlerInfo extends RequestHandlerDefaultInfo {\n  method: HttpHandlerMethod\n  path: Path\n}\n\nexport enum HttpMethods {\n  HEAD = 'HEAD',\n  GET = 'GET',\n  POST = 'POST',\n  PUT = 'PUT',\n  PATCH = 'PATCH',\n  OPTIONS = 'OPTIONS',\n  DELETE = 'DELETE',\n}\n\nexport type RequestQuery = {\n  [queryName: string]: string\n}\n\nexport type HttpRequestParsedResult = {\n  match: Match\n  cookies: Record<string, string>\n}\n\nexport type HttpRequestResolverExtras<Params extends PathParams> = {\n  params: Params\n  cookies: Record<string, string>\n}\n\n/**\n * Request handler for HTTP requests.\n * Provides request matching based on method and URL.\n */\nexport class HttpHandler extends RequestHandler<\n  HttpHandlerInfo,\n  HttpRequestParsedResult,\n  HttpRequestResolverExtras<any>\n> {\n  constructor(\n    method: HttpHandlerMethod,\n    path: Path,\n    resolver: ResponseResolver<HttpRequestResolverExtras<any>, any, any>,\n    options?: RequestHandlerOptions,\n  ) {\n    super({\n      info: {\n        header: `${method} ${path}`,\n        path,\n        method,\n      },\n      resolver,\n      options,\n    })\n\n    this.checkRedundantQueryParameters()\n  }\n\n  private checkRedundantQueryParameters() {\n    const { method, path } = this.info\n\n    if (path instanceof RegExp) {\n      return\n    }\n\n    const url = cleanUrl(path)\n\n    // Bypass request handler URLs that have no redundant characters.\n    if (url === path) {\n      return\n    }\n\n    const searchParams = getSearchParams(path)\n    const queryParams: string[] = []\n\n    searchParams.forEach((_, paramName) => {\n      queryParams.push(paramName)\n    })\n\n    devUtils.warn(\n      `Found a redundant usage of query parameters in the request handler URL for \"${method} ${path}\". Please match against a path instead and access query parameters using \"new URL(request.url).searchParams\" instead. Learn more: https://mswjs.io/docs/http/intercepting-requests#querysearch-parameters`,\n    )\n  }\n\n  async parse(args: {\n    request: Request\n    resolutionContext?: ResponseResolutionContext\n  }) {\n    const url = new URL(args.request.url)\n    const match = matchRequestUrl(\n      url,\n      this.info.path,\n      args.resolutionContext?.baseUrl,\n    )\n    const cookies = getAllRequestCookies(args.request)\n\n    return {\n      match,\n      cookies,\n    }\n  }\n\n  predicate(args: { request: Request; parsedResult: HttpRequestParsedResult }) {\n    const hasMatchingMethod = this.matchMethod(args.request.method)\n    const hasMatchingUrl = args.parsedResult.match.matches\n    return hasMatchingMethod && hasMatchingUrl\n  }\n\n  private matchMethod(actualMethod: string): boolean {\n    return this.info.method instanceof RegExp\n      ? this.info.method.test(actualMethod)\n      : isStringEqual(this.info.method, actualMethod)\n  }\n\n  protected extendResolverArgs(args: {\n    request: Request\n    parsedResult: HttpRequestParsedResult\n  }) {\n    return {\n      params: args.parsedResult.match?.params || {},\n      cookies: args.parsedResult.cookies,\n    }\n  }\n\n  async log(args: { request: Request; response: Response }) {\n    const publicUrl = toPublicUrl(args.request.url)\n    const loggedRequest = await serializeRequest(args.request)\n    const loggedResponse = await serializeResponse(args.response)\n    const statusColor = getStatusCodeColor(loggedResponse.status)\n\n    // eslint-disable-next-line no-console\n    console.groupCollapsed(\n      devUtils.formatMessage(\n        `${getTimestamp()} ${args.request.method} ${publicUrl} (%c${\n          loggedResponse.status\n        } ${loggedResponse.statusText}%c)`,\n      ),\n      `color:${statusColor}`,\n      'color:inherit',\n    )\n    // eslint-disable-next-line no-console\n    console.log('Request', loggedRequest)\n    // eslint-disable-next-line no-console\n    console.log('Handler:', this)\n    // eslint-disable-next-line no-console\n    console.log('Response', loggedResponse)\n    // eslint-disable-next-line no-console\n    console.groupEnd()\n  }\n}\n", "import {\n  DefaultBodyType,\n  RequestHandlerOptions,\n  ResponseResolver,\n} from './handlers/RequestHandler'\nimport {\n  HttpMethods,\n  HttpHandler,\n  HttpRequestResolverExtras,\n} from './handlers/HttpHandler'\nimport type { Path, PathParams } from './utils/matching/matchRequestUrl'\n\nexport type HttpRequestHandler = <\n  Params extends PathParams<keyof Params> = PathParams,\n  RequestBodyType extends DefaultBodyType = DefaultBodyType,\n  // Response body type MUST be undefined by default.\n  // This is how we can distinguish between a handler that\n  // returns plain \"Response\" and the one returning \"HttpResponse\"\n  // to enforce a stricter response body type.\n  ResponseBodyType extends DefaultBodyType = undefined,\n  RequestPath extends Path = Path,\n>(\n  path: RequestPath,\n  resolver: HttpResponseResolver<Params, RequestBodyType, ResponseBodyType>,\n  options?: RequestHandlerOptions,\n) => HttpHandler\n\nexport type HttpResponseResolver<\n  Params extends PathParams<keyof Params> = PathParams,\n  RequestBodyType extends DefaultBodyType = DefaultBodyType,\n  ResponseBodyType extends DefaultBodyType = DefaultBodyType,\n> = ResponseResolver<\n  HttpRequestResolverExtras<Params>,\n  RequestBodyType,\n  ResponseBodyType\n>\n\nfunction createHttpHandler<Method extends HttpMethods | RegExp>(\n  method: Method,\n): HttpRequestHandler {\n  return (path, resolver, options = {}) => {\n    return new HttpHandler(method, path, resolver, options)\n  }\n}\n\n/**\n * A namespace to intercept and mock HTTP requests.\n *\n * @example\n * http.get('/user', resolver)\n * http.post('/post/:id', resolver)\n *\n * @see {@link https://mswjs.io/docs/api/http `http` API reference}\n */\nexport const http = {\n  all: createHttpHandler(/.+/),\n  head: createHttpHandler(HttpMethods.HEAD),\n  get: createHttpHandler(HttpMethods.GET),\n  post: createHttpHandler(HttpMethods.POST),\n  put: createHttpHandler(HttpMethods.PUT),\n  delete: createHttpHandler(HttpMethods.DELETE),\n  patch: createHttpHandler(HttpMethods.PATCH),\n  options: createHttpHandler(HttpMethods.OPTIONS),\n}\n", "/**\n * Parses a given value into a JSON.\n * Does not throw an exception on an invalid JSON string.\n */\nexport function jsonParse<ValueType extends Record<string, any>>(\n  value: any,\n): ValueType | undefined {\n  try {\n    return JSON.parse(value)\n  } catch {\n    return undefined\n  }\n}\n", "import { stringToHeaders } from 'headers-polyfill'\nimport { DefaultRequestMultipartBody } from '../../handlers/RequestHandler'\n\ninterface ParsedContentHeaders {\n  name: string\n  filename?: string\n  contentType: string\n}\n\ninterface ContentDispositionDirective {\n  [key: string]: string | undefined\n  name: string\n  filename?: string\n  'form-data': string\n}\n\nfunction parseContentHeaders(headersString: string): ParsedContentHeaders {\n  const headers = stringToHeaders(headersString)\n  const contentType = headers.get('content-type') || 'text/plain'\n  const disposition = headers.get('content-disposition')\n\n  if (!disposition) {\n    throw new Error('\"Content-Disposition\" header is required.')\n  }\n\n  const directives = disposition.split(';').reduce((acc, chunk) => {\n    const [name, ...rest] = chunk.trim().split('=')\n    acc[name] = rest.join('=')\n    return acc\n  }, {} as ContentDispositionDirective)\n\n  const name = directives.name?.slice(1, -1)\n  const filename = directives.filename?.slice(1, -1)\n\n  return {\n    name,\n    filename,\n    contentType,\n  }\n}\n\n/**\n * Parses a given string as a multipart/form-data.\n * Does not throw an exception on an invalid multipart string.\n */\nexport function parseMultipartData<T extends DefaultRequestMultipartBody>(\n  data: string,\n  headers?: Headers,\n): T | undefined {\n  const contentType = headers?.get('content-type')\n\n  if (!contentType) {\n    return undefined\n  }\n\n  const [, ...directives] = contentType.split(/; */)\n  const boundary = directives\n    .filter((d) => d.startsWith('boundary='))\n    .map((s) => s.replace(/^boundary=/, ''))[0]\n\n  if (!boundary) {\n    return undefined\n  }\n\n  const boundaryRegExp = new RegExp(`--+${boundary}`)\n  const fields = data\n    .split(boundaryRegExp)\n    .filter((chunk) => chunk.startsWith('\\r\\n') && chunk.endsWith('\\r\\n'))\n    .map((chunk) => chunk.trimStart().replace(/\\r\\n$/, ''))\n\n  if (!fields.length) {\n    return undefined\n  }\n\n  const parsedBody: DefaultRequestMultipartBody = {}\n\n  try {\n    for (const field of fields) {\n      const [contentHeaders, ...rest] = field.split('\\r\\n\\r\\n')\n      const contentBody = rest.join('\\r\\n\\r\\n')\n      const { contentType, filename, name } =\n        parseContentHeaders(contentHeaders)\n\n      const value =\n        filename === undefined\n          ? contentBody\n          : new File([contentBody], filename, { type: contentType })\n\n      const parsedValue = parsedBody[name]\n\n      if (parsedValue === undefined) {\n        parsedBody[name] = value\n      } else if (Array.isArray(parsedValue)) {\n        parsedBody[name] = [...parsedValue, value]\n      } else {\n        parsedBody[name] = [parsedValue, value]\n      }\n    }\n\n    return parsedBody as T\n  } catch {\n    return undefined\n  }\n}\n", "import type {\n  DocumentNode,\n  OperationDefinitionNode,\n  OperationTypeNode,\n} from 'graphql'\nimport type { GraphQLVariables } from '../../handlers/GraphQLHandler'\nimport { toPublicUrl } from '../request/toPublicUrl'\nimport { devUtils } from './devUtils'\nimport { jsonParse } from './jsonParse'\nimport { parseMultipartData } from './parseMultipartData'\n\ninterface GraphQLInput {\n  query: string | null\n  variables?: GraphQLVariables\n}\n\nexport interface ParsedGraphQLQuery {\n  operationType: OperationTypeNode\n  operationName?: string\n}\n\nexport type ParsedGraphQLRequest<\n  VariablesType extends GraphQLVariables = GraphQLVariables,\n> =\n  | (ParsedGraphQLQuery & {\n      query: string\n      variables?: VariablesType\n    })\n  | undefined\n\nexport function parseDocumentNode(node: DocumentNode): ParsedGraphQLQuery {\n  const operationDef = node.definitions.find((definition) => {\n    return definition.kind === 'OperationDefinition'\n  }) as OperationDefinitionNode\n\n  return {\n    operationType: operationDef?.operation,\n    operationName: operationDef?.name?.value,\n  }\n}\n\nasync function parseQuery(query: string): Promise<ParsedGraphQLQuery | Error> {\n  /**\n   * @note Use `require` to get the \"graphql\" module here.\n   * It has to be scoped to this function because this module leaks to the\n   * root export. It has to be `require` because tools like Jest have trouble\n   * handling dynamic imports. It gets replaced with a dynamic import on build time.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { parse } =await import('graphql').catch((error) => {console.error('[MSW] Failed to parse a GraphQL query: cannot import the \"graphql\" module. Please make sure you install it if you wish to intercept GraphQL requests. See the original import error below.'); throw error})\n\n  try {\n    const ast = parse(query)\n    return parseDocumentNode(ast)\n  } catch (error) {\n    return error as Error\n  }\n}\n\nexport type GraphQLParsedOperationsMap = Record<string, string[]>\nexport type GraphQLMultipartRequestBody = {\n  operations: string\n  map?: string\n} & {\n  [fileName: string]: File\n}\n\nfunction extractMultipartVariables<VariablesType extends GraphQLVariables>(\n  variables: VariablesType,\n  map: GraphQLParsedOperationsMap,\n  files: Record<string, File>,\n) {\n  const operations = { variables }\n\n  for (const [key, pathArray] of Object.entries(map)) {\n    if (!(key in files)) {\n      throw new Error(`Given files do not have a key '${key}' .`)\n    }\n\n    for (const dotPath of pathArray) {\n      const [lastPath, ...reversedPaths] = dotPath.split('.').reverse()\n      const paths = reversedPaths.reverse()\n      let target: Record<string, any> = operations\n\n      for (const path of paths) {\n        if (!(path in target)) {\n          throw new Error(`Property '${paths}' is not in operations.`)\n        }\n\n        target = target[path]\n      }\n\n      target[lastPath] = files[key]\n    }\n  }\n\n  return operations.variables\n}\n\nasync function getGraphQLInput(request: Request): Promise<GraphQLInput | null> {\n  switch (request.method) {\n    case 'GET': {\n      const url = new URL(request.url)\n      const query = url.searchParams.get('query')\n      const variables = url.searchParams.get('variables') || ''\n\n      return {\n        query,\n        variables: jsonParse(variables),\n      }\n    }\n\n    case 'POST': {\n      // Clone the request so we could read its body without locking\n      // the body stream to the downward consumers.\n      const requestClone = request.clone()\n\n      // Handle multipart body GraphQL operations.\n      if (\n        request.headers.get('content-type')?.includes('multipart/form-data')\n      ) {\n        const responseJson = parseMultipartData<GraphQLMultipartRequestBody>(\n          await requestClone.text(),\n          request.headers,\n        )\n\n        if (!responseJson) {\n          return null\n        }\n\n        const { operations, map, ...files } = responseJson\n        const parsedOperations =\n          jsonParse<{ query?: string; variables?: GraphQLVariables }>(\n            operations,\n          ) || {}\n\n        if (!parsedOperations.query) {\n          return null\n        }\n\n        const parsedMap = jsonParse<GraphQLParsedOperationsMap>(map || '') || {}\n        const variables = parsedOperations.variables\n          ? extractMultipartVariables(\n              parsedOperations.variables,\n              parsedMap,\n              files,\n            )\n          : {}\n\n        return {\n          query: parsedOperations.query,\n          variables,\n        }\n      }\n\n      // Handle plain POST GraphQL operations.\n      const requestJson: {\n        query: string\n        variables?: GraphQLVariables\n        operations?: any /** @todo Annotate this */\n      } = await requestClone.json().catch(() => null)\n\n      if (requestJson?.query) {\n        const { query, variables } = requestJson\n\n        return {\n          query,\n          variables,\n        }\n      }\n    }\n\n    default:\n      return null\n  }\n}\n\n/**\n * Determines if a given request can be considered a GraphQL request.\n * Does not parse the query and does not guarantee its validity.\n */\nexport async function parseGraphQLRequest(\n  request: Request,\n): Promise<ParsedGraphQLRequest> {\n  const input = await getGraphQLInput(request)\n\n  if (!input || !input.query) {\n    return\n  }\n\n  const { query, variables } = input\n  const parsedResult = await parseQuery(query)\n\n  if (parsedResult instanceof Error) {\n    const requestPublicUrl = toPublicUrl(request.url)\n\n    throw new Error(\n      devUtils.formatMessage(\n        'Failed to intercept a GraphQL request to \"%s %s\": cannot parse query. See the error message from the parser below.\\n\\n%s',\n        request.method,\n        requestPublicUrl,\n        parsedResult.message,\n      ),\n    )\n  }\n\n  return {\n    query: input.query,\n    operationType: parsedResult.operationType,\n    operationName: parsedResult.operationName,\n    variables,\n  }\n}\n", "import type { DocumentNode, GraphQLError, OperationTypeNode } from 'graphql'\nimport {\n  DefaultBodyType,\n  RequestHandler,\n  RequestHandlerDefaultInfo,\n  RequestHandlerOptions,\n  ResponseResolver,\n} from './RequestHandler'\nimport { getTimestamp } from '../utils/logging/getTimestamp'\nimport { getStatusCodeColor } from '../utils/logging/getStatusCodeColor'\nimport { serializeRequest } from '../utils/logging/serializeRequest'\nimport { serializeResponse } from '../utils/logging/serializeResponse'\nimport { Match, matchRequestUrl, Path } from '../utils/matching/matchRequestUrl'\nimport {\n  ParsedGraphQLRequest,\n  GraphQLMultipartRequestBody,\n  parseGraphQLRequest,\n  parseDocumentNode,\n} from '../utils/internal/parseGraphQLRequest'\nimport { toPublicUrl } from '../utils/request/toPublicUrl'\nimport { devUtils } from '../utils/internal/devUtils'\nimport { getAllRequestCookies } from '../utils/request/getRequestCookies'\n\nexport type ExpectedOperationTypeNode = OperationTypeNode | 'all'\nexport type GraphQLHandlerNameSelector = DocumentNode | RegExp | string\n\nexport type GraphQLQuery = Record<string, any> | null\nexport type GraphQLVariables = Record<string, any>\n\nexport interface GraphQLHandlerInfo extends RequestHandlerDefaultInfo {\n  operationType: ExpectedOperationTypeNode\n  operationName: GraphQLHandlerNameSelector\n}\n\nexport type GraphQLRequestParsedResult = {\n  match: Match\n  cookies: Record<string, string>\n} & (\n  | ParsedGraphQLRequest<GraphQLVariables>\n  /**\n   * An empty version of the ParsedGraphQLRequest\n   * which simplifies the return type of the resolver\n   * when the request is to a non-matching endpoint\n   */\n  | {\n      operationType?: undefined\n      operationName?: undefined\n      query?: undefined\n      variables?: undefined\n    }\n)\n\nexport type GraphQLResolverExtras<Variables extends GraphQLVariables> = {\n  query: string\n  operationName: string\n  variables: Variables\n  cookies: Record<string, string>\n}\n\nexport type GraphQLRequestBody<VariablesType extends GraphQLVariables> =\n  | GraphQLJsonRequestBody<VariablesType>\n  | GraphQLMultipartRequestBody\n  | Record<string, any>\n  | undefined\n\nexport interface GraphQLJsonRequestBody<Variables extends GraphQLVariables> {\n  query: string\n  variables?: Variables\n}\n\nexport type GraphQLResponseBody<BodyType extends DefaultBodyType> =\n  | {\n      data?: BodyType | null\n      errors?: readonly Partial<GraphQLError>[] | null\n      extensions?: Record<string, any>\n    }\n  | null\n  | undefined\n\nexport function isDocumentNode(\n  value: DocumentNode | any,\n): value is DocumentNode {\n  if (value == null) {\n    return false\n  }\n\n  return typeof value === 'object' && 'kind' in value && 'definitions' in value\n}\n\nexport class GraphQLHandler extends RequestHandler<\n  GraphQLHandlerInfo,\n  GraphQLRequestParsedResult,\n  GraphQLResolverExtras<any>\n> {\n  private endpoint: Path\n\n  static parsedRequestCache = new WeakMap<\n    Request,\n    ParsedGraphQLRequest<GraphQLVariables>\n  >()\n\n  constructor(\n    operationType: ExpectedOperationTypeNode,\n    operationName: GraphQLHandlerNameSelector,\n    endpoint: Path,\n    resolver: ResponseResolver<GraphQLResolverExtras<any>, any, any>,\n    options?: RequestHandlerOptions,\n  ) {\n    let resolvedOperationName = operationName\n\n    if (isDocumentNode(operationName)) {\n      const parsedNode = parseDocumentNode(operationName)\n\n      if (parsedNode.operationType !== operationType) {\n        throw new Error(\n          `Failed to create a GraphQL handler: provided a DocumentNode with a mismatched operation type (expected \"${operationType}\", but got \"${parsedNode.operationType}\").`,\n        )\n      }\n\n      if (!parsedNode.operationName) {\n        throw new Error(\n          `Failed to create a GraphQL handler: provided a DocumentNode with no operation name.`,\n        )\n      }\n\n      resolvedOperationName = parsedNode.operationName\n    }\n\n    const header =\n      operationType === 'all'\n        ? `${operationType} (origin: ${endpoint.toString()})`\n        : `${operationType} ${resolvedOperationName} (origin: ${endpoint.toString()})`\n\n    super({\n      info: {\n        header,\n        operationType,\n        operationName: resolvedOperationName,\n      },\n      resolver,\n      options,\n    })\n\n    this.endpoint = endpoint\n  }\n\n  /**\n   * Parses the request body, once per request, cached across all\n   * GraphQL handlers. This is done to avoid multiple parsing of the\n   * request body, which each requires a clone of the request.\n   */\n  async parseGraphQLRequestOrGetFromCache(\n    request: Request,\n  ): Promise<ParsedGraphQLRequest<GraphQLVariables>> {\n    if (!GraphQLHandler.parsedRequestCache.has(request)) {\n      GraphQLHandler.parsedRequestCache.set(\n        request,\n        await parseGraphQLRequest(request).catch((error) => {\n          // eslint-disable-next-line no-console\n          console.error(error)\n          return undefined\n        }),\n      )\n    }\n\n    return GraphQLHandler.parsedRequestCache.get(request)\n  }\n\n  async parse(args: { request: Request }): Promise<GraphQLRequestParsedResult> {\n    /**\n     * If the request doesn't match a specified endpoint, there's no\n     * need to parse it since there's no case where we would handle this\n     */\n    const match = matchRequestUrl(new URL(args.request.url), this.endpoint)\n    const cookies = getAllRequestCookies(args.request)\n\n    if (!match.matches) {\n      return { match, cookies }\n    }\n\n    const parsedResult = await this.parseGraphQLRequestOrGetFromCache(\n      args.request,\n    )\n\n    if (typeof parsedResult === 'undefined') {\n      return { match, cookies }\n    }\n\n    return {\n      match,\n      cookies,\n      query: parsedResult.query,\n      operationType: parsedResult.operationType,\n      operationName: parsedResult.operationName,\n      variables: parsedResult.variables,\n    }\n  }\n\n  predicate(args: {\n    request: Request\n    parsedResult: GraphQLRequestParsedResult\n  }) {\n    if (args.parsedResult.operationType === undefined) {\n      return false\n    }\n\n    if (!args.parsedResult.operationName && this.info.operationType !== 'all') {\n      const publicUrl = toPublicUrl(args.request.url)\n\n      devUtils.warn(`\\\nFailed to intercept a GraphQL request at \"${args.request.method} ${publicUrl}\": anonymous GraphQL operations are not supported.\n\nConsider naming this operation or using \"graphql.operation()\" request handler to intercept GraphQL requests regardless of their operation name/type. Read more: https://mswjs.io/docs/api/graphql/#graphqloperationresolver`)\n      return false\n    }\n\n    const hasMatchingOperationType =\n      this.info.operationType === 'all' ||\n      args.parsedResult.operationType === this.info.operationType\n\n    const hasMatchingOperationName =\n      this.info.operationName instanceof RegExp\n        ? this.info.operationName.test(args.parsedResult.operationName || '')\n        : args.parsedResult.operationName === this.info.operationName\n\n    return (\n      args.parsedResult.match.matches &&\n      hasMatchingOperationType &&\n      hasMatchingOperationName\n    )\n  }\n\n  protected extendResolverArgs(args: {\n    request: Request\n    parsedResult: GraphQLRequestParsedResult\n  }) {\n    return {\n      query: args.parsedResult.query || '',\n      operationName: args.parsedResult.operationName || '',\n      variables: args.parsedResult.variables || {},\n      cookies: args.parsedResult.cookies,\n    }\n  }\n\n  async log(args: {\n    request: Request\n    response: Response\n    parsedResult: GraphQLRequestParsedResult\n  }) {\n    const loggedRequest = await serializeRequest(args.request)\n    const loggedResponse = await serializeResponse(args.response)\n    const statusColor = getStatusCodeColor(loggedResponse.status)\n    const requestInfo = args.parsedResult.operationName\n      ? `${args.parsedResult.operationType} ${args.parsedResult.operationName}`\n      : `anonymous ${args.parsedResult.operationType}`\n\n    // eslint-disable-next-line no-console\n    console.groupCollapsed(\n      devUtils.formatMessage(\n        `${getTimestamp()} ${requestInfo} (%c${loggedResponse.status} ${\n          loggedResponse.statusText\n        }%c)`,\n      ),\n      `color:${statusColor}`,\n      'color:inherit',\n    )\n    // eslint-disable-next-line no-console\n    console.log('Request:', loggedRequest)\n    // eslint-disable-next-line no-console\n    console.log('Handler:', this)\n    // eslint-disable-next-line no-console\n    console.log('Response:', loggedResponse)\n    // eslint-disable-next-line no-console\n    console.groupEnd()\n  }\n}\n", "import type { DocumentNode, OperationTypeNode } from 'graphql'\nimport {\n  ResponseResolver,\n  RequestHandlerOptions,\n} from './handlers/RequestHandler'\nimport {\n  GraphQLHandler,\n  GraphQLVariables,\n  ExpectedOperationTypeNode,\n  GraphQLHandlerNameSelector,\n  GraphQLResolverExtras,\n  GraphQLResponseBody,\n  GraphQLQuery,\n} from './handlers/GraphQLHandler'\nimport type { Path } from './utils/matching/matchRequestUrl'\n\nexport interface TypedDocumentNode<\n  Result = { [key: string]: any },\n  Variables = { [key: string]: any },\n> extends DocumentNode {\n  __apiType?: (variables: Variables) => Result\n  __resultType?: Result\n  __variablesType?: Variables\n}\n\nexport type GraphQLRequestHandler = <\n  Query extends GraphQLQuery = GraphQLQuery,\n  Variables extends GraphQLVariables = GraphQLVariables,\n>(\n  operationName:\n    | GraphQLHandlerNameSelector\n    | DocumentNode\n    | TypedDocumentNode<Query, Variables>,\n  resolver: GraphQLResponseResolver<\n    [Query] extends [never] ? GraphQLQuery : Query,\n    Variables\n  >,\n  options?: RequestHandlerOptions,\n) => GraphQLHandler\n\nexport type GraphQLResponseResolver<\n  Query extends GraphQLQuery = GraphQLQuery,\n  Variables extends GraphQLVariables = GraphQLVariables,\n> = ResponseResolver<\n  GraphQLResolverExtras<Variables>,\n  null,\n  GraphQLResponseBody<[Query] extends [never] ? GraphQLQuery : Query>\n>\n\nfunction createScopedGraphQLHandler(\n  operationType: ExpectedOperationTypeNode,\n  url: Path,\n): GraphQLRequestHandler {\n  return (operationName, resolver, options = {}) => {\n    return new GraphQLHandler(\n      operationType,\n      operationName,\n      url,\n      resolver,\n      options,\n    )\n  }\n}\n\nfunction createGraphQLOperationHandler(url: Path) {\n  return <\n    Query extends GraphQLQuery = GraphQLQuery,\n    Variables extends GraphQLVariables = GraphQLVariables,\n  >(\n    resolver: ResponseResolver<\n      GraphQLResolverExtras<Variables>,\n      null,\n      GraphQLResponseBody<Query>\n    >,\n  ) => {\n    return new GraphQLHandler('all', new RegExp('.*'), url, resolver)\n  }\n}\n\nconst standardGraphQLHandlers = {\n  /**\n   * Intercepts a GraphQL query by a given name.\n   *\n   * @example\n   * graphql.query('GetUser', () => {\n   *   return HttpResponse.json({ data: { user: { name: 'John' } } })\n   * })\n   *\n   * @see {@link https://mswjs.io/docs/api/graphql#graphqlqueryqueryname-resolver `graphql.query()` API reference}\n   */\n  query: createScopedGraphQLHandler('query' as OperationTypeNode, '*'),\n\n  /**\n   * Intercepts a GraphQL mutation by its name.\n   *\n   * @example\n   * graphql.mutation('SavePost', () => {\n   *   return HttpResponse.json({ data: { post: { id: 'abc-123 } } })\n   * })\n   *\n   * @see {@link https://mswjs.io/docs/api/graphql#graphqlmutationmutationname-resolver `graphql.query()` API reference}\n   *\n   */\n  mutation: createScopedGraphQLHandler('mutation' as OperationTypeNode, '*'),\n\n  /**\n   * Intercepts any GraphQL operation, regardless of its type or name.\n   *\n   * @example\n   * graphql.operation(() => {\n   *   return HttpResponse.json({ data: { name: 'John' } })\n   * })\n   *\n   * @see {@link https://mswjs.io/docs/api/graphql#graphqloperationresolver `graphql.operation()` API reference}\n   */\n  operation: createGraphQLOperationHandler('*'),\n}\n\nfunction createGraphQLLink(url: Path): typeof standardGraphQLHandlers {\n  return {\n    operation: createGraphQLOperationHandler(url),\n    query: createScopedGraphQLHandler('query' as OperationTypeNode, url),\n    mutation: createScopedGraphQLHandler('mutation' as OperationTypeNode, url),\n  }\n}\n\n/**\n * A namespace to intercept and mock GraphQL operations\n *\n * @example\n * graphql.query('GetUser', resolver)\n * graphql.mutation('DeletePost', resolver)\n *\n * @see {@link https://mswjs.io/docs/api/graphql `graphql` API reference}\n */\nexport const graphql = {\n  ...standardGraphQLHandlers,\n\n  /**\n   * Intercepts GraphQL operations scoped by the given URL.\n   *\n   * @example\n   * const github = graphql.link('https://api.github.com/graphql')\n   * github.query('GetRepo', resolver)\n   *\n   * @see {@link https://mswjs.io/docs/api/graphql#graphqllinkurl `graphql.link()` API reference}\n   */\n  link: createGraphQLLink,\n}\n", "import { Emitter } from 'strict-event-emitter'\nimport { createRequestId } from '@mswjs/interceptors'\nimport type {\n  WebSocketClientConnectionProtocol,\n  WebSocketConnectionData,\n  WebSocketServerConnectionProtocol,\n} from '@mswjs/interceptors/WebSocket'\nimport {\n  type Match,\n  type Path,\n  type PathParams,\n  matchRequestUrl,\n} from '../utils/matching/matchRequestUrl'\nimport { getCallFrame } from '../utils/internal/getCallFrame'\nimport type { HandlerKind } from './common'\n\ntype WebSocketHandlerParsedResult = {\n  match: Match\n}\n\nexport type WebSocketHandlerEventMap = {\n  connection: [args: WebSocketHandlerConnection]\n}\n\nexport interface WebSocketHandlerConnection {\n  client: WebSocketClientConnectionProtocol\n  server: WebSocketServerConnectionProtocol\n  info: WebSocketConnectionData['info']\n  params: PathParams\n}\n\nexport interface WebSocketResolutionContext {\n  baseUrl?: string\n}\n\nexport const kEmitter = Symbol('kEmitter')\nexport const kSender = Symbol('kSender')\nconst kStopPropagationPatched = Symbol('kStopPropagationPatched')\nconst KOnStopPropagation = Symbol('KOnStopPropagation')\n\nexport class WebSocketHandler {\n  private readonly __kind: HandlerKind\n\n  public id: string\n  public callFrame?: string\n\n  protected [kEmitter]: Emitter<WebSocketHandlerEventMap>\n\n  constructor(private readonly url: Path) {\n    this.id = createRequestId()\n\n    this[kEmitter] = new Emitter()\n    this.callFrame = getCallFrame(new Error())\n    this.__kind = 'EventHandler'\n  }\n\n  public parse(args: {\n    url: URL\n    resolutionContext?: WebSocketResolutionContext\n  }): WebSocketHandlerParsedResult {\n    const clientUrl = new URL(args.url)\n\n    /**\n     * @note Remove the Socket.IO path prefix from the WebSocket\n     * client URL. This is an exception to keep the users from\n     * including the implementation details in their handlers.\n     */\n    clientUrl.pathname = clientUrl.pathname.replace(/^\\/socket.io\\//, '/')\n\n    const match = matchRequestUrl(\n      clientUrl,\n      this.url,\n      args.resolutionContext?.baseUrl,\n    )\n\n    return {\n      match,\n    }\n  }\n\n  public predicate(args: {\n    url: URL\n    parsedResult: WebSocketHandlerParsedResult\n  }): boolean {\n    return args.parsedResult.match.matches\n  }\n\n  public async run(\n    connection: Omit<WebSocketHandlerConnection, 'params'>,\n    resolutionContext?: WebSocketResolutionContext,\n  ): Promise<boolean> {\n    const parsedResult = this.parse({\n      url: connection.client.url,\n      resolutionContext,\n    })\n\n    if (!this.predicate({ url: connection.client.url, parsedResult })) {\n      return false\n    }\n\n    const resolvedConnection: WebSocketHandlerConnection = {\n      ...connection,\n      params: parsedResult.match.params || {},\n    }\n\n    return this.connect(resolvedConnection)\n  }\n\n  protected connect(connection: WebSocketHandlerConnection): boolean {\n    // Support `event.stopPropagation()` for various client/server events.\n    connection.client.addEventListener(\n      'message',\n      createStopPropagationListener(this),\n    )\n    connection.client.addEventListener(\n      'close',\n      createStopPropagationListener(this),\n    )\n\n    connection.server.addEventListener(\n      'open',\n      createStopPropagationListener(this),\n    )\n    connection.server.addEventListener(\n      'message',\n      createStopPropagationListener(this),\n    )\n    connection.server.addEventListener(\n      'error',\n      createStopPropagationListener(this),\n    )\n    connection.server.addEventListener(\n      'close',\n      createStopPropagationListener(this),\n    )\n\n    // Emit the connection event on the handler.\n    // This is what the developer adds listeners for.\n    return this[kEmitter].emit('connection', connection)\n  }\n}\n\nfunction createStopPropagationListener(handler: WebSocketHandler) {\n  return function stopPropagationListener(event: Event) {\n    const propagationStoppedAt = Reflect.get(event, 'kPropagationStoppedAt') as\n      | string\n      | undefined\n\n    if (propagationStoppedAt && handler.id !== propagationStoppedAt) {\n      event.stopImmediatePropagation()\n      return\n    }\n\n    Object.defineProperty(event, KOnStopPropagation, {\n      value(this: WebSocketHandler) {\n        Object.defineProperty(event, 'kPropagationStoppedAt', {\n          value: handler.id,\n        })\n      },\n      configurable: true,\n    })\n\n    // Since the same event instance is shared between all client/server objects,\n    // make sure to patch its `stopPropagation` method only once.\n    if (!Reflect.get(event, kStopPropagationPatched)) {\n      event.stopPropagation = new Proxy(event.stopPropagation, {\n        apply: (target, thisArg, args) => {\n          Reflect.get(event, KOnStopPropagation)?.call(handler)\n          return Reflect.apply(target, thisArg, args)\n        },\n      })\n\n      Object.defineProperty(event, kStopPropagationPatched, {\n        value: true,\n        // If something else attempts to redefine this, throw.\n        configurable: false,\n      })\n    }\n  }\n}\n", "import { WebSocketClientConnectionProtocol } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport {\n  SerializedWebSocketClient,\n  WebSocketClientStore,\n} from './WebSocketClientStore'\n\nexport class WebSocketMemoryClientStore implements WebSocketClientStore {\n  private store: Map<string, SerializedWebSocketClient>\n\n  constructor() {\n    this.store = new Map()\n  }\n\n  public async add(client: WebSocketClientConnectionProtocol): Promise<void> {\n    this.store.set(client.id, { id: client.id, url: client.url.href })\n  }\n\n  public getAll(): Promise<Array<SerializedWebSocketClient>> {\n    return Promise.resolve(Array.from(this.store.values()))\n  }\n\n  public async deleteMany(clientIds: Array<string>): Promise<void> {\n    for (const clientId of clientIds) {\n      this.store.delete(clientId)\n    }\n  }\n}\n", "import { DeferredPromise } from '@open-draft/deferred-promise'\nimport { WebSocketClientConnectionProtocol } from '@mswjs/interceptors/lib/browser/interceptors/WebSocket'\nimport {\n  type SerializedWebSocketClient,\n  WebSocketClientStore,\n} from './WebSocketClientStore'\n\nconst DB_NAME = 'msw-websocket-clients'\nconst DB_STORE_NAME = 'clients'\n\nexport class WebSocketIndexedDBClientStore implements WebSocketClientStore {\n  private db: Promise<IDBDatabase>\n\n  constructor() {\n    this.db = this.createDatabase()\n  }\n\n  public async add(client: WebSocketClientConnectionProtocol): Promise<void> {\n    const promise = new DeferredPromise<void>()\n    const store = await this.getStore()\n\n    /**\n     * @note Use `.put()` instead of `.add()` to allow setting clients\n     * that already exist in the database. This can happen if a single page\n     * has multiple event handlers. Each handler will receive the \"connection\"\n     * event in parallel, and try to set that WebSocket client in the database.\n     */\n    const request = store.put({\n      id: client.id,\n      url: client.url.href,\n    } satisfies SerializedWebSocketClient)\n\n    request.onsuccess = () => {\n      promise.resolve()\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(request.error)\n      promise.reject(\n        new Error(\n          `Failed to add WebSocket client \"${client.id}\". There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  public async getAll(): Promise<Array<SerializedWebSocketClient>> {\n    const promise = new DeferredPromise<Array<SerializedWebSocketClient>>()\n    const store = await this.getStore()\n    const request = store.getAll() as IDBRequest<\n      Array<SerializedWebSocketClient>\n    >\n\n    request.onsuccess = () => {\n      promise.resolve(request.result)\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.log(request.error)\n      promise.reject(\n        new Error(\n          `Failed to get all WebSocket clients. There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  public async deleteMany(clientIds: Array<string>): Promise<void> {\n    const promise = new DeferredPromise<void>()\n    const store = await this.getStore()\n\n    for (const clientId of clientIds) {\n      store.delete(clientId)\n    }\n\n    store.transaction.oncomplete = () => {\n      promise.resolve()\n    }\n    store.transaction.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(store.transaction.error)\n      promise.reject(\n        new Error(\n          `Failed to delete WebSocket clients [${clientIds.join(', ')}]. There is likely an additional output above.`,\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  private async createDatabase(): Promise<IDBDatabase> {\n    const promise = new DeferredPromise<IDBDatabase>()\n    const request = indexedDB.open(DB_NAME, 1)\n\n    request.onsuccess = ({ currentTarget }) => {\n      const db = Reflect.get(currentTarget!, 'result') as IDBDatabase\n\n      if (db.objectStoreNames.contains(DB_STORE_NAME)) {\n        return promise.resolve(db)\n      }\n    }\n\n    request.onupgradeneeded = async ({ currentTarget }) => {\n      const db = Reflect.get(currentTarget!, 'result') as IDBDatabase\n      if (db.objectStoreNames.contains(DB_STORE_NAME)) {\n        return\n      }\n\n      const store = db.createObjectStore(DB_STORE_NAME, { keyPath: 'id' })\n      store.transaction.oncomplete = () => {\n        promise.resolve(db)\n      }\n      store.transaction.onerror = () => {\n        // eslint-disable-next-line no-console\n        console.error(store.transaction.error)\n        promise.reject(\n          new Error(\n            'Failed to create WebSocket client store. There is likely an additional output above.',\n          ),\n        )\n      }\n    }\n    request.onerror = () => {\n      // eslint-disable-next-line no-console\n      console.error(request.error)\n      promise.reject(\n        new Error(\n          'Failed to open an IndexedDB database. There is likely an additional output above.',\n        ),\n      )\n    }\n\n    return promise\n  }\n\n  private async getStore(): Promise<IDBObjectStore> {\n    const db = await this.db\n    return db.transaction(DB_STORE_NAME, 'readwrite').objectStore(DB_STORE_NAME)\n  }\n}\n", "import type {\n  WebSocketData,\n  WebSocketClientConnectionProtocol,\n  WebSocketClientEventMap,\n} from '@mswjs/interceptors/WebSocket'\nimport { WebSocketClientStore } from './WebSocketClientStore'\nimport { WebSocketMemoryClientStore } from './WebSocketMemoryClientStore'\nimport { WebSocketIndexedDBClientStore } from './WebSocketIndexedDBClientStore'\n\nexport type WebSocketBroadcastChannelMessage =\n  | {\n      type: 'extraneous:send'\n      payload: {\n        clientId: string\n        data: WebSocketData\n      }\n    }\n  | {\n      type: 'extraneous:close'\n      payload: {\n        clientId: string\n        code?: number\n        reason?: string\n      }\n    }\n\n/**\n * A manager responsible for accumulating WebSocket client\n * connections across different browser runtimes.\n */\nexport class WebSocketClientManager {\n  private store: WebSocketClientStore\n  private runtimeClients: Map<string, WebSocketClientConnectionProtocol>\n  private allClients: Set<WebSocketClientConnectionProtocol>\n\n  constructor(private channel: BroadcastChannel) {\n    // Store the clients in the IndexedDB in the browser,\n    // otherwise, store the clients in memory.\n    this.store =\n      typeof indexedDB !== 'undefined'\n        ? new WebSocketIndexedDBClientStore()\n        : new WebSocketMemoryClientStore()\n\n    this.runtimeClients = new Map()\n    this.allClients = new Set()\n\n    this.channel.addEventListener('message', (message) => {\n      if (message.data?.type === 'db:update') {\n        this.flushDatabaseToMemory()\n      }\n    })\n\n    if (typeof window !== 'undefined') {\n      window.addEventListener('message', async (message) => {\n        if (message.data?.type === 'msw/worker:stop') {\n          await this.removeRuntimeClients()\n        }\n      })\n    }\n  }\n\n  private async flushDatabaseToMemory() {\n    const storedClients = await this.store.getAll()\n\n    this.allClients = new Set(\n      storedClients.map((client) => {\n        const runtimeClient = this.runtimeClients.get(client.id)\n\n        /**\n         * @note For clients originating in this runtime, use their\n         * direct references. No need to wrap them in a remote connection.\n         */\n        if (runtimeClient) {\n          return runtimeClient\n        }\n\n        return new WebSocketRemoteClientConnection(\n          client.id,\n          new URL(client.url),\n          this.channel,\n        )\n      }),\n    )\n  }\n\n  private async removeRuntimeClients(): Promise<void> {\n    await this.store.deleteMany(Array.from(this.runtimeClients.keys()))\n    this.runtimeClients.clear()\n    await this.flushDatabaseToMemory()\n    this.notifyOthersAboutDatabaseUpdate()\n  }\n\n  /**\n   * All active WebSocket client connections.\n   */\n  get clients(): Set<WebSocketClientConnectionProtocol> {\n    return this.allClients\n  }\n\n  /**\n   * Notify other runtimes about the database update\n   * using the shared `BroadcastChannel` instance.\n   */\n  private notifyOthersAboutDatabaseUpdate(): void {\n    this.channel.postMessage({ type: 'db:update' })\n  }\n\n  private async addClient(\n    client: WebSocketClientConnectionProtocol,\n  ): Promise<void> {\n    await this.store.add(client)\n    // Sync the in-memory clients in this runtime with the\n    // updated database. This pulls in all the stored clients.\n    await this.flushDatabaseToMemory()\n    this.notifyOthersAboutDatabaseUpdate()\n  }\n\n  /**\n   * Adds the given `WebSocket` client connection to the set\n   * of all connections. The given connection is always the complete\n   * connection object because `addConnection()` is called only\n   * for the opened connections in the same runtime.\n   */\n  public async addConnection(\n    client: WebSocketClientConnectionProtocol,\n  ): Promise<void> {\n    // Store this client in the map of clients created in this runtime.\n    // This way, the manager can distinguish between this runtime clients\n    // and extraneous runtime clients when synchronizing clients storage.\n    this.runtimeClients.set(client.id, client)\n\n    // Add the new client to the storage.\n    await this.addClient(client)\n\n    // Handle the incoming BroadcastChannel messages from other runtimes\n    // that attempt to control this runtime (via a remote connection wrapper).\n    // E.g. another runtime calling `client.send()` for the client in this runtime.\n    const handleExtraneousMessage = (\n      message: MessageEvent<WebSocketBroadcastChannelMessage>,\n    ) => {\n      const { type, payload } = message.data\n\n      // Ignore broadcasted messages for other clients.\n      if (\n        typeof payload === 'object' &&\n        'clientId' in payload &&\n        payload.clientId !== client.id\n      ) {\n        return\n      }\n\n      switch (type) {\n        case 'extraneous:send': {\n          client.send(payload.data)\n          break\n        }\n\n        case 'extraneous:close': {\n          client.close(payload.code, payload.reason)\n          break\n        }\n      }\n    }\n\n    const abortController = new AbortController()\n\n    this.channel.addEventListener('message', handleExtraneousMessage, {\n      signal: abortController.signal,\n    })\n\n    // Once closed, this connection cannot be operated on.\n    // This must include the extraneous runtimes as well.\n    client.addEventListener('close', () => abortController.abort(), {\n      once: true,\n    })\n  }\n}\n\n/**\n * A wrapper class to operate with WebSocket client connections\n * from other runtimes. This class maintains 1-1 public API\n * compatibility to the `WebSocketClientConnection` but relies\n * on the given `BroadcastChannel` to communicate instructions\n * with the client connections from other runtimes.\n */\nexport class WebSocketRemoteClientConnection\n  implements WebSocketClientConnectionProtocol\n{\n  constructor(\n    public readonly id: string,\n    public readonly url: URL,\n    private channel: BroadcastChannel,\n  ) {}\n\n  send(data: WebSocketData): void {\n    this.channel.postMessage({\n      type: 'extraneous:send',\n      payload: {\n        clientId: this.id,\n        data,\n      },\n    } as WebSocketBroadcastChannelMessage)\n  }\n\n  close(code?: number | undefined, reason?: string | undefined): void {\n    this.channel.postMessage({\n      type: 'extraneous:close',\n      payload: {\n        clientId: this.id,\n        code,\n        reason,\n      },\n    } as WebSocketBroadcastChannelMessage)\n  }\n\n  addEventListener<EventType extends keyof WebSocketClientEventMap>(\n    _type: EventType,\n    _listener: (\n      this: WebSocket,\n      event: WebSocketClientEventMap[EventType],\n    ) => void,\n    _options?: AddEventListenerOptions | boolean,\n  ): void {\n    throw new Error(\n      'WebSocketRemoteClientConnection.addEventListener is not supported',\n    )\n  }\n\n  removeEventListener<EventType extends keyof WebSocketClientEventMap>(\n    _event: EventType,\n    _listener: (\n      this: WebSocket,\n      event: WebSocketClientEventMap[EventType],\n    ) => void,\n    _options?: EventListenerOptions | boolean,\n  ): void {\n    throw new Error(\n      'WebSocketRemoteClientConnection.removeEventListener is not supported',\n    )\n  }\n}\n", "import { invariant } from 'outvariant'\nimport type {\n  WebSocketData,\n  WebSocketClientConnectionProtocol,\n} from '@mswjs/interceptors/WebSocket'\nimport {\n  WebSocketHandler,\n  kEmitter,\n  type WebSocketHandlerEventMap,\n} from './handlers/WebSocketHandler'\nimport { Path, isPath } from './utils/matching/matchRequestUrl'\nimport { WebSocketClientManager } from './ws/WebSocketClientManager'\n\nfunction isBroadcastChannelWithUnref(\n  channel: BroadcastChannel,\n): channel is BroadcastChannel & NodeJS.RefCounted {\n  return typeof Reflect.get(channel, 'unref') !== 'undefined'\n}\n\nconst webSocketChannel = new BroadcastChannel('msw:websocket-client-manager')\n\nif (isBroadcastChannelWithUnref(webSocketChannel)) {\n  // Allows the Node.js thread to exit if it is the only active handle in the event system.\n  // https://nodejs.org/api/worker_threads.html#broadcastchannelunref\n  webSocketChannel.unref()\n}\n\nexport type WebSocketEventListener<\n  EventType extends keyof WebSocketHandlerEventMap,\n> = (...args: WebSocketHandlerEventMap[EventType]) => void\n\nexport type WebSocketLink = {\n  /**\n   * A set of all WebSocket clients connected\n   * to this link.\n   *\n   * @see {@link https://mswjs.io/docs/api/ws#clients `clients` API reference}\n   */\n  clients: Set<WebSocketClientConnectionProtocol>\n\n  /**\n   * Adds an event listener to this WebSocket link.\n   *\n   * @example\n   * const chat = ws.link('wss://chat.example.com')\n   * chat.addEventListener('connection', listener)\n   *\n   * @see {@link https://mswjs.io/docs/api/ws#onevent-listener `on()` API reference}\n   */\n  addEventListener<EventType extends keyof WebSocketHandlerEventMap>(\n    event: EventType,\n    listener: WebSocketEventListener<EventType>,\n  ): WebSocketHandler\n\n  /**\n   * Broadcasts the given data to all WebSocket clients.\n   *\n   * @example\n   * const service = ws.link('wss://example.com')\n   * service.addEventListener('connection', () => {\n   *   service.broadcast('hello, everyone!')\n   * })\n   *\n   * @see {@link https://mswjs.io/docs/api/ws#broadcastdata `broadcast()` API reference}\n   */\n  broadcast(data: WebSocketData): void\n\n  /**\n   * Broadcasts the given data to all WebSocket clients\n   * except the ones provided in the `clients` argument.\n   *\n   * @example\n   * const service = ws.link('wss://example.com')\n   * service.addEventListener('connection', ({ client }) => {\n   *   service.broadcastExcept(client, 'hi, the rest of you!')\n   * })\n   *\n   * @see {@link https://mswjs.io/docs/api/ws#broadcastexceptclients-data `broadcast()` API reference}\n   */\n  broadcastExcept(\n    clients:\n      | WebSocketClientConnectionProtocol\n      | Array<WebSocketClientConnectionProtocol>,\n    data: WebSocketData,\n  ): void\n}\n\n/**\n * Intercepts outgoing WebSocket connections to the given URL.\n *\n * @example\n * const chat = ws.link('wss://chat.example.com')\n * chat.addEventListener('connection', ({ client }) => {\n *   client.send('hello from server!')\n * })\n */\nfunction createWebSocketLinkHandler(url: Path): WebSocketLink {\n  invariant(url, 'Expected a WebSocket server URL but got undefined')\n\n  invariant(\n    isPath(url),\n    'Expected a WebSocket server URL to be a valid path but got %s',\n    typeof url,\n  )\n\n  const clientManager = new WebSocketClientManager(webSocketChannel)\n\n  return {\n    get clients() {\n      return clientManager.clients\n    },\n    addEventListener(event, listener) {\n      const handler = new WebSocketHandler(url)\n\n      // Add the connection event listener for when the\n      // handler matches and emits a connection event.\n      // When that happens, store that connection in the\n      // set of all connections for reference.\n      handler[kEmitter].on('connection', async ({ client }) => {\n        await clientManager.addConnection(client)\n      })\n\n      // The \"handleWebSocketEvent\" function will invoke\n      // the \"run()\" method on the WebSocketHandler.\n      // If the handler matches, it will emit the \"connection\"\n      // event. Attach the user-defined listener to that event.\n      handler[kEmitter].on(event, listener)\n\n      return handler\n    },\n\n    broadcast(data) {\n      // This will invoke \"send()\" on the immediate clients\n      // in this runtime and post a message to the broadcast channel\n      // to trigger send for the clients in other runtimes.\n      this.broadcastExcept([], data)\n    },\n\n    broadcastExcept(clients, data) {\n      const ignoreClients = Array.prototype\n        .concat(clients)\n        .map((client) => client.id)\n\n      clientManager.clients.forEach((otherClient) => {\n        if (!ignoreClients.includes(otherClient.id)) {\n          otherClient.send(data)\n        }\n      })\n    },\n  }\n}\n\n/**\n * A namespace to intercept and mock WebSocket connections.\n *\n * @example\n * const chat = ws.link('wss://chat.example.com')\n *\n * @see {@link https://mswjs.io/docs/api/ws `ws` API reference}\n * @see {@link https://mswjs.io/docs/basics/handling-websocket-events Handling WebSocket events}\n */\nexport const ws = {\n  link: createWebSocketLinkHandler,\n}\n\nexport { WebSocketData }\n", "import { createRequestId } from '@mswjs/interceptors'\nimport type { RequestHandler } from './handlers/RequestHandler'\nimport {\n  executeHandlers,\n  type ResponseResolutionContext,\n} from './utils/executeHandlers'\n\n/**\n * Finds a response for the given request instance\n * in the array of request handlers.\n * @param handlers The array of request handlers.\n * @param request The `Request` instance.\n * @param resolutionContext Request resolution options.\n * @returns {Response} A mocked response, if any.\n */\nexport const getResponse = async (\n  handlers: Array<RequestHandler>,\n  request: Request,\n  resolutionContext?: ResponseResolutionContext,\n): Promise<Response | undefined> => {\n  const result = await executeHandlers({\n    request,\n    requestId: createRequestId(),\n    handlers,\n    resolutionContext,\n  })\n\n  return result?.response\n}\n", "import { FetchResponse } from '@mswjs/interceptors'\nimport type { DefaultBodyType, JsonBodyType } from './handlers/RequestHandler'\nimport type { NoInfer } from './typeUtils'\nimport {\n  decorateResponse,\n  normalizeResponseInit,\n} from './utils/HttpResponse/decorators'\n\nexport interface HttpResponseInit extends ResponseInit {\n  type?: ResponseType\n}\n\nexport const bodyType: unique symbol = Symbol('bodyType')\nexport type DefaultUnsafeFetchResponse = Response & {\n  [bodyType]?: never\n}\n\nexport interface StrictRequest<BodyType extends JsonBodyType> extends Request {\n  json(): Promise<BodyType>\n}\n\n/**\n * Opaque `Response` type that supports strict body type.\n *\n * @deprecated Please use {@link HttpResponse} instead.\n */\nexport type StrictResponse<BodyType extends DefaultBodyType> =\n  HttpResponse<BodyType>\n\n/**\n * A drop-in replacement for the standard `Response` class\n * to allow additional features, like mocking the response `Set-<PERSON>ie` header.\n *\n * @example\n * new HttpResponse('Hello world', { status: 201 })\n * HttpResponse.json({ name: 'John' })\n * HttpResponse.formData(form)\n *\n * @see {@link https://mswjs.io/docs/api/http-response `HttpResponse` API reference}\n */\nexport class HttpResponse<\n  BodyType extends DefaultBodyType,\n> extends FetchResponse {\n  readonly [bodyType]: BodyType = null as any\n\n  constructor(body?: NoInfer<BodyType> | null, init?: HttpResponseInit) {\n    const responseInit = normalizeResponseInit(init)\n    super(body as BodyInit, responseInit)\n    decorateResponse(this, responseInit)\n  }\n\n  static error(): HttpResponse<any> {\n    return super.error() as HttpResponse<any>\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"text/plain\"` body.\n   * @example\n   * HttpResponse.text('hello world')\n   * HttpResponse.text('Error', { status: 500 })\n   */\n  static text<BodyType extends string>(\n    body?: NoInfer<BodyType> | null,\n    init?: HttpResponseInit,\n  ): HttpResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'text/plain')\n    }\n\n    // Automatically set the \"Content-Length\" response header\n    // for non-empty text responses. This enforces consistency and\n    // brings mocked responses closer to production.\n    if (!responseInit.headers.has('Content-Length')) {\n      responseInit.headers.set(\n        'Content-Length',\n        body ? new Blob([body]).size.toString() : '0',\n      )\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"application/json\"` body.\n   * @example\n   * HttpResponse.json({ firstName: 'John' })\n   * HttpResponse.json({ error: 'Not Authorized' }, { status: 401 })\n   */\n  static json<BodyType extends JsonBodyType>(\n    body?: NoInfer<BodyType> | null | undefined,\n    init?: HttpResponseInit,\n  ): HttpResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'application/json')\n    }\n\n    /**\n     * @note TypeScript is incorrect here.\n     * Stringifying undefined will return undefined.\n     */\n    const responseText = JSON.stringify(body) as string | undefined\n\n    if (!responseInit.headers.has('Content-Length')) {\n      responseInit.headers.set(\n        'Content-Length',\n        responseText ? new Blob([responseText]).size.toString() : '0',\n      )\n    }\n\n    return new HttpResponse(responseText as BodyType, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"application/xml\"` body.\n   * @example\n   * HttpResponse.xml(`<user name=\"John\" />`)\n   * HttpResponse.xml(`<article id=\"abc-123\" />`, { status: 201 })\n   */\n  static xml<BodyType extends string>(\n    body?: BodyType | null,\n    init?: HttpResponseInit,\n  ): HttpResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'text/xml')\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `Content-Type: \"text/html\"` body.\n   * @example\n   * HttpResponse.html(`<p class=\"author\">Jane Doe</p>`)\n   * HttpResponse.html(`<main id=\"abc-123\">Main text</main>`, { status: 201 })\n   */\n  static html<BodyType extends string>(\n    body?: BodyType | null,\n    init?: HttpResponseInit,\n  ): HttpResponse<BodyType> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'text/html')\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with an `ArrayBuffer` body.\n   * @example\n   * const buffer = new ArrayBuffer(3)\n   * const view = new Uint8Array(buffer)\n   * view.set([1, 2, 3])\n   *\n   * HttpResponse.arrayBuffer(buffer)\n   */\n  static arrayBuffer(\n    body?: ArrayBuffer | SharedArrayBuffer,\n    init?: HttpResponseInit,\n  ): HttpResponse<ArrayBuffer | SharedArrayBuffer> {\n    const responseInit = normalizeResponseInit(init)\n\n    if (!responseInit.headers.has('Content-Type')) {\n      responseInit.headers.set('Content-Type', 'application/octet-stream')\n    }\n\n    if (body && !responseInit.headers.has('Content-Length')) {\n      responseInit.headers.set('Content-Length', body.byteLength.toString())\n    }\n\n    return new HttpResponse(body, responseInit)\n  }\n\n  /**\n   * Create a `Response` with a `FormData` body.\n   * @example\n   * const data = new FormData()\n   * data.set('name', 'Alice')\n   *\n   * HttpResponse.formData(data)\n   */\n  static formData(\n    body?: FormData,\n    init?: HttpResponseInit,\n  ): HttpResponse<FormData> {\n    return new HttpResponse(body, normalizeResponseInit(init))\n  }\n}\n", "import { isNodeProcess } from 'is-node-process'\n\nexport const SET_TIMEOUT_MAX_ALLOWED_INT = 2147483647\nexport const MIN_SERVER_RESPONSE_TIME = 100\nexport const MAX_SERVER_RESPONSE_TIME = 400\nexport const NODE_SERVER_RESPONSE_TIME = 5\n\nfunction getRealisticResponseTime(): number {\n  if (isNodeProcess()) {\n    return NODE_SERVER_RESPONSE_TIME\n  }\n\n  return Math.floor(\n    Math.random() * (MAX_SERVER_RESPONSE_TIME - MIN_SERVER_RESPONSE_TIME) +\n      MIN_SERVER_RESPONSE_TIME,\n  )\n}\n\nexport type DelayMode = 'real' | 'infinite'\n\n/**\n * Delays the response by the given duration (ms).\n *\n * @example\n * await delay() // emulate realistic server response time\n * await delay(1200) // delay response by 1200ms\n * await delay('infinite') // delay response infinitely\n *\n * @see {@link https://mswjs.io/docs/api/delay `delay()` API reference}\n */\nexport async function delay(\n  durationOrMode?: DelayMode | number,\n): Promise<void> {\n  let delayTime: number\n\n  if (typeof durationOrMode === 'string') {\n    switch (durationOrMode) {\n      case 'infinite': {\n        // Using `Infinity` as a delay value executes the response timeout immediately.\n        // Instead, use the maximum allowed integer for `setTimeout`.\n        delayTime = SET_TIMEOUT_MAX_ALLOWED_INT\n        break\n      }\n      case 'real': {\n        delayTime = getRealisticResponseTime()\n        break\n      }\n      default: {\n        throw new Error(\n          `Failed to delay a response: unknown delay mode \"${durationOrMode}\". Please make sure you provide one of the supported modes (\"real\", \"infinite\") or a number.`,\n        )\n      }\n    }\n  } else if (typeof durationOrMode === 'undefined') {\n    // Use random realistic server response time when no explicit delay duration was provided.\n    delayTime = getRealisticResponseTime()\n  } else {\n    // Guard against passing values like `Infinity` or `Number.MAX_VALUE`\n    // as the response delay duration. They don't produce the result you may expect.\n    if (durationOrMode > SET_TIMEOUT_MAX_ALLOWED_INT) {\n      throw new Error(\n        `Failed to delay a response: provided delay duration (${durationOrMode}) exceeds the maximum allowed duration for \"setTimeout\" (${SET_TIMEOUT_MAX_ALLOWED_INT}). This will cause the response to be returned immediately. Please use a number within the allowed range to delay the response by exact duration, or consider the \"infinite\" delay mode to delay the response indefinitely.`,\n      )\n    }\n\n    delayTime = durationOrMode\n  }\n\n  return new Promise((resolve) => setTimeout(resolve, delayTime))\n}\n", "import { invariant } from 'outvariant'\n\nexport type BypassRequestInput = string | URL | Request\n\n/**\n * Creates a `Request` instance that will always be ignored by MSW.\n *\n * @example\n * import { bypass } from 'msw'\n *\n * fetch(bypass('/resource'))\n * fetch(bypass(new URL('/resource', 'https://example.com)))\n * fetch(bypass(new Request('https://example.com/resource')))\n *\n * @see {@link https://mswjs.io/docs/api/bypass `bypass()` API reference}\n */\nexport function bypass(input: BypassRequestInput, init?: RequestInit): Request {\n  // Always create a new Request instance.\n  // This way, the \"init\" modifications will propagate\n  // to the bypass request instance automatically.\n  const request = new Request(\n    // If given a Request instance, clone it not to exhaust\n    // the original request's body.\n    input instanceof Request ? input.clone() : input,\n    init,\n  )\n\n  invariant(\n    !request.bodyUsed,\n    'Failed to create a bypassed request to \"%s %s\": given request instance already has its body read. Make sure to clone the intercepted request if you wish to read its body before bypassing it.',\n    request.method,\n    request.url,\n  )\n\n  const requestClone = request.clone()\n\n  /**\n   * Send the internal request header that would instruct MSW\n   * to perform this request as-is, ignoring any matching handlers.\n   * @note Use the `accept` header to support scenarios when the\n   * request cannot have headers (e.g. `sendBeacon` requests).\n   */\n  requestClone.headers.append('accept', 'msw/passthrough')\n\n  return requestClone\n}\n", "import type { HttpResponse } from './HttpResponse'\n\n/**\n * Performs the intercepted request as-is.\n *\n * This stops request handler lookup so no other handlers\n * can affect this request past this point.\n * Unlike `bypass()`, this will not trigger an additional request.\n *\n * @example\n * http.get('/resource', () => {\n *   return passthrough()\n * })\n *\n * @see {@link https://mswjs.io/docs/api/passthrough `passthrough()` API reference}\n */\nexport function passthrough(): HttpResponse<any> {\n  return new Response(null, {\n    status: 302,\n    statusText: 'Passthrough',\n    headers: {\n      'x-msw-intention': 'passthrough',\n    },\n  }) as HttpResponse<any>\n}\n", "import { checkGlobals } from './utils/internal/checkGlobals'\n\nexport { SetupApi } from './SetupApi'\n\n/* HTTP handlers */\nexport { RequestHandler } from './handlers/RequestHandler'\nexport { http } from './http'\nexport { HttpHandler, HttpMethods } from './handlers/HttpHandler'\nexport { graphql } from './graphql'\nexport { GraphQLHandler } from './handlers/GraphQLHandler'\n\n/* WebSocket handler */\nexport { ws, type WebSocketLink } from './ws'\nexport {\n  WebSocketHandler,\n  type WebSocketHandlerEventMap,\n  type WebSocketHandlerConnection,\n} from './handlers/WebSocketHandler'\n\n/* Utils */\nexport { matchRequestUrl } from './utils/matching/matchRequestUrl'\nexport * from './utils/handleRequest'\nexport { getResponse } from './getResponse'\nexport { cleanUrl } from './utils/url/cleanUrl'\n\n/**\n * Type definitions.\n */\n\nexport type { SharedOptions, LifeCycleEventsMap } from './sharedOptions'\n\nexport type {\n  ResponseResolver,\n  ResponseResolverReturnType,\n  AsyncResponseResolverReturnType,\n  RequestHandlerOptions,\n  DefaultBodyType,\n  DefaultRequestMultipartBody,\n  JsonBodyType,\n} from './handlers/RequestHandler'\n\nexport type {\n  RequestQuery,\n  HttpRequestParsedResult,\n} from './handlers/HttpHandler'\nexport type { HttpRequestHandler, HttpResponseResolver } from './http'\n\nexport type {\n  GraphQLQuery,\n  GraphQLVariables,\n  GraphQLRequestBody,\n  GraphQLJsonRequestBody,\n} from './handlers/GraphQLHandler'\nexport type { GraphQLRequestHandler, GraphQLResponseResolver } from './graphql'\n\nexport type { WebSocketData, WebSocketEventListener } from './ws'\n\nexport type { Path, PathParams, Match } from './utils/matching/matchRequestUrl'\nexport type { ParsedGraphQLRequest } from './utils/internal/parseGraphQLRequest'\n\nexport * from './HttpResponse'\nexport * from './delay'\nexport { bypass } from './bypass'\nexport { passthrough } from './passthrough'\nexport { isCommonAssetRequest } from './isCommonAssetRequest'\n\n// Validate environmental globals before executing any code.\n// This ensures that the library gives user-friendly errors\n// when ran in the environments that require additional polyfills\n// from the end user.\ncheckGlobals()\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,eAAe;AAO7B;IACE,OAAO,QAAQ;IACf,SAAS;MACP;IACF;EACF;AACF;;;ACbO,SAAS,cAAc,QAAgB,UAA2B;AACvE,SAAO,OAAO,YAAY,MAAM,SAAS,YAAY;AACvD;;;ACLO,IAAK,mBAAL,CAAKA,qBAAL;AACLA,mBAAA,SAAA,IAAU;AACVA,mBAAA,SAAA,IAAU;AACVA,mBAAA,QAAA,IAAS;AAHC,SAAAA;AAAA,GAAA,mBAAA,CAAA,CAAA;AASL,SAAS,mBAAmB,QAAiC;AAClE,MAAI,SAAS,KAAK;AAChB,WAAO;EACT;AAEA,MAAI,SAAS,KAAK;AAChB,WAAO;EACT;AAEA,SAAO;AACT;;;ACTA,eAAsB,iBACpB,SACwB;AACxB,QAAM,eAAe,QAAQ,MAAM;AACnC,QAAM,cAAc,MAAM,aAAa,KAAK;AAE5C,SAAO;IACL,KAAK,IAAI,IAAI,QAAQ,GAAG;IACxB,QAAQ,QAAQ;IAChB,SAAS,OAAO,YAAY,QAAQ,QAAQ,QAAQ,CAAC;IACrD,MAAM;EACR;AACF;;;ACpBA,IAAM,EAAE,QAAQ,IAAI;AASpB,eAAsB,kBACpB,UAC6B;AAC7B,QAAM,gBAAgB,SAAS,MAAM;AACrC,QAAM,eAAe,MAAM,cAAc,KAAK;AAK9C,QAAM,iBAAiB,cAAc,UAAU;AAC/C,QAAM,qBACJ,cAAc,cAAc,QAAQ,cAAc,KAAK;AAEzD,SAAO;IACL,QAAQ;IACR,YAAY;IACZ,SAAS,OAAO,YAAY,cAAc,QAAQ,QAAQ,CAAC;IAC3D,MAAM;EACR;AACF;;;ACVA,SAAS,MAAM,KAAW;AACxB,MAAM,SAAqB,CAAA;AAC3B,MAAI,IAAI;AAER,SAAO,IAAI,IAAI,QAAQ;AACrB,QAAM,OAAO,IAAI,CAAC;AAElB,QAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAChD,aAAO,KAAK,EAAE,MAAM,YAAY,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AAC3D;;AAGF,QAAI,SAAS,MAAM;AACjB,aAAO,KAAK,EAAE,MAAM,gBAAgB,OAAO,KAAK,OAAO,IAAI,GAAG,EAAC,CAAE;AACjE;;AAGF,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AACvD;;AAGF,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK,EAAE,MAAM,SAAS,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;AACxD;;AAGF,QAAI,SAAS,KAAK;AAChB,UAAI,OAAO;AACX,UAAI,IAAI,IAAI;AAEZ,aAAO,IAAI,IAAI,QAAQ;AACrB,YAAM,OAAO,IAAI,WAAW,CAAC;AAE7B;;UAEG,QAAQ,MAAM,QAAQ;UAEtB,QAAQ,MAAM,QAAQ;UAEtB,QAAQ,MAAM,QAAQ;UAEvB,SAAS;UACT;AACA,kBAAQ,IAAI,GAAG;AACf;;AAGF;;AAGF,UAAI,CAAC;AAAM,cAAM,IAAI,UAAU,6BAAA,OAA6B,CAAC,CAAE;AAE/D,aAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,KAAI,CAAE;AACnD,UAAI;AACJ;;AAGF,QAAI,SAAS,KAAK;AAChB,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,IAAI,IAAI;AAEZ,UAAI,IAAI,CAAC,MAAM,KAAK;AAClB,cAAM,IAAI,UAAU,oCAAA,OAAoC,CAAC,CAAE;;AAG7D,aAAO,IAAI,IAAI,QAAQ;AACrB,YAAI,IAAI,CAAC,MAAM,MAAM;AACnB,qBAAW,IAAI,GAAG,IAAI,IAAI,GAAG;AAC7B;;AAGF,YAAI,IAAI,CAAC,MAAM,KAAK;AAClB;AACA,cAAI,UAAU,GAAG;AACf;AACA;;mBAEO,IAAI,CAAC,MAAM,KAAK;AACzB;AACA,cAAI,IAAI,IAAI,CAAC,MAAM,KAAK;AACtB,kBAAM,IAAI,UAAU,uCAAA,OAAuC,CAAC,CAAE;;;AAIlE,mBAAW,IAAI,GAAG;;AAGpB,UAAI;AAAO,cAAM,IAAI,UAAU,yBAAA,OAAyB,CAAC,CAAE;AAC3D,UAAI,CAAC;AAAS,cAAM,IAAI,UAAU,sBAAA,OAAsB,CAAC,CAAE;AAE3D,aAAO,KAAK,EAAE,MAAM,WAAW,OAAO,GAAG,OAAO,QAAO,CAAE;AACzD,UAAI;AACJ;;AAGF,WAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,IAAI,GAAG,EAAC,CAAE;;AAGzD,SAAO,KAAK,EAAE,MAAM,OAAO,OAAO,GAAG,OAAO,GAAE,CAAE;AAEhD,SAAO;AACT;AAgBM,SAAU,MAAM,KAAa,SAA0B;AAA1B,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAA0B;AAC3D,MAAM,SAAS,MAAM,GAAG;AAChB,MAAAC,MAAuC,QAAO,UAA9C,WAAQA,QAAA,SAAG,OAAIA,KAAE,KAAsB,QAAO,WAA7B,YAAS,OAAA,SAAG,QAAK;AAC1C,MAAM,SAAkB,CAAA;AACxB,MAAI,MAAM;AACV,MAAI,IAAI;AACR,MAAI,OAAO;AAEX,MAAM,aAAa,SAAC,MAAsB;AACxC,QAAI,IAAI,OAAO,UAAU,OAAO,CAAC,EAAE,SAAS;AAAM,aAAO,OAAO,GAAG,EAAE;EACvE;AAEA,MAAM,cAAc,SAAC,MAAsB;AACzC,QAAMC,SAAQ,WAAW,IAAI;AAC7B,QAAIA,WAAU;AAAW,aAAOA;AAC1B,QAAAD,MAA4B,OAAO,CAAC,GAA5B,WAAQA,IAAA,MAAE,QAAKA,IAAA;AAC7B,UAAM,IAAI,UAAU,cAAA,OAAc,UAAQ,MAAA,EAAA,OAAO,OAAK,aAAA,EAAA,OAAc,IAAI,CAAE;EAC5E;AAEA,MAAM,cAAc,WAAA;AAClB,QAAIE,UAAS;AACb,QAAID;AACJ,WAAQA,SAAQ,WAAW,MAAM,KAAK,WAAW,cAAc,GAAI;AACjE,MAAAC,WAAUD;;AAEZ,WAAOC;EACT;AAEA,MAAM,SAAS,SAACD,QAAa;AAC3B,aAAmB,KAAA,GAAA,cAAA,WAAA,KAAA,YAAA,QAAA,MAAS;AAAvB,UAAME,QAAI,YAAA,EAAA;AAAe,UAAIF,OAAM,QAAQE,KAAI,IAAI;AAAI,eAAO;;AACnE,WAAO;EACT;AAEA,MAAM,cAAc,SAACC,SAAc;AACjC,QAAM,OAAO,OAAO,OAAO,SAAS,CAAC;AACrC,QAAM,WAAWA,YAAW,QAAQ,OAAO,SAAS,WAAW,OAAO;AAEtE,QAAI,QAAQ,CAAC,UAAU;AACrB,YAAM,IAAI,UACR,8DAAA,OAA+D,KAAa,MAAI,GAAA,CAAG;;AAIvF,QAAI,CAAC,YAAY,OAAO,QAAQ;AAAG,aAAO,KAAA,OAAK,aAAa,SAAS,GAAC,KAAA;AACtE,WAAO,SAAA,OAAS,aAAa,QAAQ,GAAC,KAAA,EAAA,OAAM,aAAa,SAAS,GAAC,MAAA;EACrE;AAEA,SAAO,IAAI,OAAO,QAAQ;AACxB,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAM,UAAU,WAAW,SAAS;AAEpC,QAAI,QAAQ,SAAS;AACnB,UAAI,SAAS,QAAQ;AAErB,UAAI,SAAS,QAAQ,MAAM,MAAM,IAAI;AACnC,gBAAQ;AACR,iBAAS;;AAGX,UAAI,MAAM;AACR,eAAO,KAAK,IAAI;AAChB,eAAO;;AAGT,aAAO,KAAK;QACV,MAAM,QAAQ;QACd;QACA,QAAQ;QACR,SAAS,WAAW,YAAY,MAAM;QACtC,UAAU,WAAW,UAAU,KAAK;OACrC;AACD;;AAGF,QAAM,QAAQ,QAAQ,WAAW,cAAc;AAC/C,QAAI,OAAO;AACT,cAAQ;AACR;;AAGF,QAAI,MAAM;AACR,aAAO,KAAK,IAAI;AAChB,aAAO;;AAGT,QAAM,OAAO,WAAW,MAAM;AAC9B,QAAI,MAAM;AACR,UAAM,SAAS,YAAW;AAC1B,UAAM,SAAO,WAAW,MAAM,KAAK;AACnC,UAAM,YAAU,WAAW,SAAS,KAAK;AACzC,UAAM,SAAS,YAAW;AAE1B,kBAAY,OAAO;AAEnB,aAAO,KAAK;QACV,MAAM,WAAS,YAAU,QAAQ;QACjC,SAAS,UAAQ,CAAC,YAAU,YAAY,MAAM,IAAI;QAClD;QACA;QACA,UAAU,WAAW,UAAU,KAAK;OACrC;AACD;;AAGF,gBAAY,KAAK;;AAGnB,SAAO;AACT;AA+IM,SAAU,MACd,KACA,SAAwE;AAExE,MAAM,OAAc,CAAA;AACpB,MAAM,KAAK,aAAa,KAAK,MAAM,OAAO;AAC1C,SAAO,iBAAoB,IAAI,MAAM,OAAO;AAC9C;AAKM,SAAU,iBACd,IACA,MACA,SAAqC;AAArC,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAqC;AAE7B,MAAAC,MAA8B,QAAO,QAArC,SAAMA,QAAA,SAAG,SAAC,GAAS;AAAK,WAAA;EAAA,IAACA;AAEjC,SAAO,SAAU,UAAgB;AAC/B,QAAM,IAAI,GAAG,KAAK,QAAQ;AAC1B,QAAI,CAAC;AAAG,aAAO;AAEP,QAAG,OAAgB,EAAC,CAAA,GAAX,QAAU,EAAC;AAC5B,QAAM,SAAS,uBAAO,OAAO,IAAI;2BAExBC,IAAC;AACR,UAAI,EAAEA,EAAC,MAAM;;AAEb,UAAM,MAAM,KAAKA,KAAI,CAAC;AAEtB,UAAI,IAAI,aAAa,OAAO,IAAI,aAAa,KAAK;AAChD,eAAO,IAAI,IAAI,IAAI,EAAEA,EAAC,EAAE,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,IAAI,SAAC,OAAK;AAC/D,iBAAO,OAAO,OAAO,GAAG;QAC1B,CAAC;aACI;AACL,eAAO,IAAI,IAAI,IAAI,OAAO,EAAEA,EAAC,GAAG,GAAG;;;AAVvC,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAG;cAAxB,CAAC;;AAcV,WAAO,EAAE,MAAM,OAAO,OAAM;EAC9B;AACF;AAKA,SAAS,aAAa,KAAW;AAC/B,SAAO,IAAI,QAAQ,6BAA6B,MAAM;AACxD;AAKA,SAAS,MAAM,SAAiC;AAC9C,SAAO,WAAW,QAAQ,YAAY,KAAK;AAC7C;AAqBA,SAAS,eAAe,MAAc,MAAY;AAChD,MAAI,CAAC;AAAM,WAAO;AAElB,MAAM,cAAc;AAEpB,MAAI,QAAQ;AACZ,MAAI,aAAa,YAAY,KAAK,KAAK,MAAM;AAC7C,SAAO,YAAY;AACjB,SAAK,KAAK;;MAER,MAAM,WAAW,CAAC,KAAK;MACvB,QAAQ;MACR,QAAQ;MACR,UAAU;MACV,SAAS;KACV;AACD,iBAAa,YAAY,KAAK,KAAK,MAAM;;AAG3C,SAAO;AACT;AAKA,SAAS,cACP,OACA,MACA,SAA8C;AAE9C,MAAM,QAAQ,MAAM,IAAI,SAAC,MAAI;AAAK,WAAA,aAAa,MAAM,MAAM,OAAO,EAAE;EAAlC,CAAwC;AAC1E,SAAO,IAAI,OAAO,MAAA,OAAM,MAAM,KAAK,GAAG,GAAC,GAAA,GAAK,MAAM,OAAO,CAAC;AAC5D;AAKA,SAAS,eACP,MACA,MACA,SAA8C;AAE9C,SAAO,eAAe,MAAM,MAAM,OAAO,GAAG,MAAM,OAAO;AAC3D;AAoCM,SAAU,eACd,QACA,MACA,SAAmC;AAAnC,MAAA,YAAA,QAAA;AAAA,cAAA,CAAA;EAAmC;AAGjC,MAAAD,MAME,QAAO,QANT,SAAMA,QAAA,SAAG,QAAKA,KACd,KAKE,QAAO,OALT,QAAK,OAAA,SAAG,OAAI,IACZ,KAIE,QAAO,KAJT,MAAG,OAAA,SAAG,OAAI,IACV,KAGE,QAAO,QAHT,SAAM,OAAA,SAAG,SAAC,GAAS;AAAK,WAAA;EAAA,IAAC,IACzB,KAEE,QAAO,WAFT,YAAS,OAAA,SAAG,QAAK,IACjB,KACE,QAAO,UADT,WAAQ,OAAA,SAAG,KAAE;AAEf,MAAM,aAAa,IAAA,OAAI,aAAa,QAAQ,GAAC,KAAA;AAC7C,MAAM,cAAc,IAAA,OAAI,aAAa,SAAS,GAAC,GAAA;AAC/C,MAAI,QAAQ,QAAQ,MAAM;AAG1B,WAAoB,KAAA,GAAA,WAAA,QAAA,KAAA,SAAA,QAAA,MAAQ;AAAvB,QAAM,QAAK,SAAA,EAAA;AACd,QAAI,OAAO,UAAU,UAAU;AAC7B,eAAS,aAAa,OAAO,KAAK,CAAC;WAC9B;AACL,UAAM,SAAS,aAAa,OAAO,MAAM,MAAM,CAAC;AAChD,UAAM,SAAS,aAAa,OAAO,MAAM,MAAM,CAAC;AAEhD,UAAI,MAAM,SAAS;AACjB,YAAI;AAAM,eAAK,KAAK,KAAK;AAEzB,YAAI,UAAU,QAAQ;AACpB,cAAI,MAAM,aAAa,OAAO,MAAM,aAAa,KAAK;AACpD,gBAAM,MAAM,MAAM,aAAa,MAAM,MAAM;AAC3C,qBAAS,MAAA,OAAM,QAAM,MAAA,EAAA,OAAO,MAAM,SAAO,MAAA,EAAA,OAAO,MAAM,EAAA,OAAG,QAAM,KAAA,EAAA,OAAM,MAAM,SAAO,MAAA,EAAA,OAAO,QAAM,GAAA,EAAA,OAAI,GAAG;iBACjG;AACL,qBAAS,MAAA,OAAM,QAAM,GAAA,EAAA,OAAI,MAAM,SAAO,GAAA,EAAA,OAAI,QAAM,GAAA,EAAA,OAAI,MAAM,QAAQ;;eAE/D;AACL,cAAI,MAAM,aAAa,OAAO,MAAM,aAAa,KAAK;AACpD,kBAAM,IAAI,UACR,mBAAA,OAAmB,MAAM,MAAI,+BAAA,CAA+B;;AAIhE,mBAAS,IAAA,OAAI,MAAM,SAAO,GAAA,EAAA,OAAI,MAAM,QAAQ;;aAEzC;AACL,iBAAS,MAAA,OAAM,MAAM,EAAA,OAAG,QAAM,GAAA,EAAA,OAAI,MAAM,QAAQ;;;;AAKtD,MAAI,KAAK;AACP,QAAI,CAAC;AAAQ,eAAS,GAAA,OAAG,aAAW,GAAA;AAEpC,aAAS,CAAC,QAAQ,WAAW,MAAM,MAAA,OAAM,YAAU,GAAA;SAC9C;AACL,QAAM,WAAW,OAAO,OAAO,SAAS,CAAC;AACzC,QAAM,iBACJ,OAAO,aAAa,WAChB,YAAY,QAAQ,SAAS,SAAS,SAAS,CAAC,CAAC,IAAI,KACrD,aAAa;AAEnB,QAAI,CAAC,QAAQ;AACX,eAAS,MAAA,OAAM,aAAW,KAAA,EAAA,OAAM,YAAU,KAAA;;AAG5C,QAAI,CAAC,gBAAgB;AACnB,eAAS,MAAA,OAAM,aAAW,GAAA,EAAA,OAAI,YAAU,GAAA;;;AAI5C,SAAO,IAAI,OAAO,OAAO,MAAM,OAAO,CAAC;AACzC;AAcM,SAAU,aACd,MACA,MACA,SAA8C;AAE9C,MAAI,gBAAgB;AAAQ,WAAO,eAAe,MAAM,IAAI;AAC5D,MAAI,MAAM,QAAQ,IAAI;AAAG,WAAO,cAAc,MAAM,MAAM,OAAO;AACjE,SAAO,eAAe,MAAM,MAAM,OAAO;AAC3C;;;AChoBA,IAAM,UAAU,IAAI,YAAY;;;ACEzB,IAAM,oBAAmC,OAAO,iBAAiB;ACGjE,SAAS,YAAY,KAAsB;AAChD,MAAI;AACF,QAAI,IAAI,GAAG;AACX,WAAO;EACT,SAAS,QAAP;AACA,WAAO;EACT;AACF;ACTO,SAAS,iBACd,YACA,QACe;AACf,QAAM,aAAa,OAAO,sBAAsB,MAAM;AAEtD,QAAM,SAAS,WAAW,KAAK,CAACE,YAAW;AACzC,WAAOA,QAAO,gBAAgB;EAChC,CAAC;AAED,MAAI,QAAQ;AACV,WAAO,QAAQ,IAAI,QAAQ,MAAM;EACnC;AAEA;AACF;ACQO,IAAM,iBAAN,cAA4B,SAAS;EAS1C,OAAO,yBAAyB,QAAyB;AACvD,WAAO,UAAU,OAAO,UAAU;EACpC;EAEA,OAAO,mBAAmB,QAAyB;AACjD,WAAO,eAAc,2BAA2B,SAAS,MAAM;EACjE;;;;;EAMA,OAAO,mBAAmB,QAAyB;AACjD,WAAO,CAAC,eAAc,0BAA0B,SAAS,MAAM;EACjE;EAEA,OAAO,OAAO,KAAyB,UAA0B;AAC/D,QAAI,CAAC,OAAO,QAAQ,YAAY,CAAC,YAAY,GAAG,GAAG;AACjD;IACF;AAEA,UAAM,QAAQ,iBAA2C,SAAS,QAAQ;AAE1E,QAAI,OAAO;AAGT,YAAM,QAAQ,KAAK,IAAI,IAAI,GAAG,CAAC;IACjC,OAAO;AAEL,aAAO,eAAe,UAAU,OAAO;QACrC,OAAO;QACP,YAAY;QACZ,cAAc;QACd,UAAU;MACZ,CAAC;IACH;EACF;;;;EAKA,OAAO,gBAAgB,YAAoC;AACzD,UAAM,UAAU,IAAI,QAAQ;AAC5B,aAAS,OAAO,GAAG,OAAO,WAAW,QAAQ,QAAQ,GAAG;AACtD,cAAQ,OAAO,WAAW,IAAI,GAAG,WAAW,OAAO,CAAC,CAAC;IACvD;AACA,WAAO;EACT;EAEA,YAAY,MAAwB,OAA0B,CAAC,GAAG;AApFpE,QAAAC;AAqFI,UAAM,UAASA,MAAA,KAAK,WAAL,OAAAA,MAAe;AAC9B,UAAM,aAAa,eAAc,yBAAyB,MAAM,IAC5D,SACA;AACJ,UAAM,YAAY,eAAc,mBAAmB,MAAM,IAAI,OAAO;AAEpE,UAAM,WAAW;MACf,QAAQ;MACR,YAAY,KAAK;MACjB,SAAS,KAAK;IAChB,CAAC;AAED,QAAI,WAAW,YAAY;AAKzB,YAAM,QAAQ,iBAA2C,SAAS,IAAI;AAEtE,UAAI,OAAO;AACT,cAAM,SAAS;MACjB,OAAO;AACL,eAAO,eAAe,MAAM,UAAU;UACpC,OAAO;UACP,YAAY;UACZ,cAAc;UACd,UAAU;QACZ,CAAC;MACH;IACF;AAEA,mBAAc,OAAO,KAAK,KAAK,IAAI;EACrC;AACF;AA5FO,IAAM,gBAAN;AAAM,cAKK,4BAA4B,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AALzD,cAOK,6BAA6B,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;ACjCvE,IAAM,cAAc,OAAO,aAAa;;;AEGjC,SAAS,YAAY,KAAU,aAAsB,MAAc;AACxE,SAAO,CAAC,cAAc,IAAI,QAAQ,IAAI,QAAQ,EAAE,OAAO,OAAO,EAAE,KAAK,EAAE;AACzE;;;ACLA,IAAM,2BAA2B;AAE1B,SAAS,gBAAgB,MAAc;AAC5C,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,kBAAkB,EAAE;AACjD;AAMO,SAAS,SAAS,MAAsB;AAG7C,MAAI,KAAK,SAAS,GAAG,GAAG;AACtB,WAAO;EACT;AAGA,SAAO,KAAK,QAAQ,0BAA0B,EAAE;AAClD;;;AChBO,SAAS,cAAc,KAAsB;AAClD,SAAO,gCAAgC,KAAK,GAAG;AACjD;;;ACAO,SAAS,eAAe,MAAc,SAA0B;AAErE,MAAI,cAAc,IAAI,GAAG;AACvB,WAAO;EACT;AAGA,MAAI,KAAK,WAAW,GAAG,GAAG;AACxB,WAAO;EACT;AAIA,QAAM,SAAS,WAAY,OAAO,aAAa,eAAe,SAAS;AAEvE,SAAO;;IAEH,UAAU,IAAI,IAAI,UAAU,IAAI,GAAG,MAAM,EAAE,IAAI;MAC/C;AACN;;;ACZO,SAAS,cAAc,MAAY,SAAwB;AAEhE,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;EACT;AAEA,QAAM,mBAAmB,eAAe,MAAM,OAAO;AAErD,SAAO,SAAS,gBAAgB;AAClC;;;ACHO,SAAS,WAAW,MAAsB;AAC/C,SACE,KAMG;IACC;IACA,CAAC,GAAG,eAAmC,aAAqB;AAC1D,YAAM,aAAa;AAEnB,UAAI,CAAC,eAAe;AAClB,eAAO;MACT;AAEA,aAAO,cAAc,WAAW,GAAG,IAC/B,GAAG,aAAa,GAAG,QAAQ,KAC3B,GAAG,aAAa,GAAG,UAAU;IACnC;EACF,EAKC,QAAQ,qBAAqB,QAAQ,EAMrC,QAAQ,wBAAwB,QAAQ;AAE/C;AAKO,SAAS,gBAAgB,KAAU,MAAY,SAAyB;AAC7E,QAAM,iBAAiB,cAAc,MAAM,OAAO;AAClD,QAAM,YACJ,OAAO,mBAAmB,WACtB,WAAW,cAAc,IACzB;AAEN,QAAMC,YAAW,YAAY,GAAG;AAChC,QAAM,SAAS,MAAM,WAAW,EAAE,QAAQ,mBAAmB,CAAC,EAAEA,SAAQ;AACxE,QAAM,SAAU,UAAW,OAAO,UAA0B,CAAC;AAE7D,SAAO;IACL,SAAS,WAAW;IACpB;EACF;AACF;AAEO,SAAS,OAAO,OAA+B;AACpD,SAAO,OAAO,UAAU,YAAY,iBAAiB;AACvD;;;AC5EA,IAAI,WAAW,OAAO;AACtB,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO;AAC1B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,aAAa,CAAC,IAAI,QAAQ,SAAS,YAAY;AACjD,SAAO,QAAQ,GAAG,GAAG,kBAAkB,EAAE,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,GAAG,GAAG,IAAI;AAC7F;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,EACzG;AACF;AAGA,IAAI,iBAAiB,WAAW;AAAA,EAC9B,+BAA+B,SAAS;AACtC;AACA,YAAQ,QAAQC;AAChB,YAAQ,YAAY;AACpB,QAAI,aAAa,OAAO,UAAU;AAClC,QAAI,mBAAmB,OAAO,UAAU;AACxC,QAAI,mBAAmB;AACvB,QAAI,oBAAoB;AACxB,QAAI,oBAAoB;AACxB,QAAI,kBAAkB;AACtB,aAASA,OAAM,KAAK,KAAK;AACvB,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACrD;AACA,UAAI,MAAM,CAAC;AACX,UAAI,MAAM,IAAI;AACd,UAAI,MAAM;AAAG,eAAO;AACpB,UAAI,MAAM,OAAO,IAAI,UAAU;AAC/B,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,SAAG;AACD,gBAAQ,IAAI,QAAQ,KAAK,KAAK;AAC9B,YAAI,UAAU;AAAI;AAClB,iBAAS,IAAI,QAAQ,KAAK,KAAK;AAC/B,YAAI,WAAW,IAAI;AACjB,mBAAS;AAAA,QACX,WAAW,QAAQ,QAAQ;AACzB,kBAAQ,IAAI,YAAY,KAAK,QAAQ,CAAC,IAAI;AAC1C;AAAA,QACF;AACA,YAAI,cAAc,WAAW,KAAK,OAAO,KAAK;AAC9C,YAAI,YAAY,SAAS,KAAK,OAAO,WAAW;AAChD,YAAI,MAAM,IAAI,MAAM,aAAa,SAAS;AAC1C,YAAI,CAAC,iBAAiB,KAAK,KAAK,GAAG,GAAG;AACpC,cAAI,cAAc,WAAW,KAAK,QAAQ,GAAG,MAAM;AACnD,cAAI,YAAY,SAAS,KAAK,QAAQ,WAAW;AACjD,cAAI,IAAI,WAAW,WAAW,MAAM,MAAM,IAAI,WAAW,YAAY,CAAC,MAAM,IAAI;AAC9E;AACA;AAAA,UACF;AACA,cAAI,MAAM,IAAI,MAAM,aAAa,SAAS;AAC1C,cAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AAAA,QAC/B;AACA,gBAAQ,SAAS;AAAA,MACnB,SAAS,QAAQ;AACjB,aAAO;AAAA,IACT;AACA,aAAS,WAAW,KAAK,OAAO,KAAK;AACnC,SAAG;AACD,YAAI,OAAO,IAAI,WAAW,KAAK;AAC/B,YAAI,SAAS,MAAM,SAAS;AAAG,iBAAO;AAAA,MACxC,SAAS,EAAE,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,aAAS,SAAS,KAAK,OAAO,KAAK;AACjC,aAAO,QAAQ,KAAK;AAClB,YAAI,OAAO,IAAI,WAAW,EAAE,KAAK;AACjC,YAAI,SAAS,MAAM,SAAS;AAAG,iBAAO,QAAQ;AAAA,MAChD;AACA,aAAO;AAAA,IACT;AACA,aAAS,UAAU,MAAM,KAAK,KAAK;AACjC,UAAI,MAAM,OAAO,IAAI,UAAU;AAC/B,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AACA,UAAI,CAAC,iBAAiB,KAAK,IAAI,GAAG;AAChC,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AACA,UAAI,QAAQ,IAAI,GAAG;AACnB,UAAI,CAAC,kBAAkB,KAAK,KAAK,GAAG;AAClC,cAAM,IAAI,UAAU,yBAAyB;AAAA,MAC/C;AACA,UAAI,MAAM,OAAO,MAAM;AACvB,UAAI,CAAC;AAAK,eAAO;AACjB,UAAI,QAAQ,IAAI,QAAQ;AACtB,YAAI,SAAS,KAAK,MAAM,IAAI,MAAM;AAClC,YAAI,CAAC,SAAS,MAAM,GAAG;AACrB,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AACA,eAAO,eAAe;AAAA,MACxB;AACA,UAAI,IAAI,QAAQ;AACd,YAAI,CAAC,kBAAkB,KAAK,IAAI,MAAM,GAAG;AACvC,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AACA,eAAO,cAAc,IAAI;AAAA,MAC3B;AACA,UAAI,IAAI,MAAM;AACZ,YAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,GAAG;AACnC,gBAAM,IAAI,UAAU,wBAAwB;AAAA,QAC9C;AACA,eAAO,YAAY,IAAI;AAAA,MACzB;AACA,UAAI,IAAI,SAAS;AACf,YAAI,UAAU,IAAI;AAClB,YAAI,CAAC,OAAO,OAAO,KAAK,MAAM,QAAQ,QAAQ,CAAC,GAAG;AAChD,gBAAM,IAAI,UAAU,2BAA2B;AAAA,QACjD;AACA,eAAO,eAAe,QAAQ,YAAY;AAAA,MAC5C;AACA,UAAI,IAAI,UAAU;AAChB,eAAO;AAAA,MACT;AACA,UAAI,IAAI,QAAQ;AACd,eAAO;AAAA,MACT;AACA,UAAI,IAAI,aAAa;AACnB,eAAO;AAAA,MACT;AACA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WAAW,IAAI,SAAS,YAAY,IAAI,IAAI;AACnF,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AACA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WAAW,IAAI,SAAS,YAAY,IAAI,IAAI;AACnF,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,OAAO,KAAK;AACnB,aAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,mBAAmB,GAAG,IAAI;AAAA,IAC7D;AACA,aAAS,OAAO,KAAK;AACnB,aAAO,WAAW,KAAK,GAAG,MAAM;AAAA,IAClC;AACA,aAAS,UAAU,KAAK,SAAS;AAC/B,UAAI;AACF,eAAO,QAAQ,GAAG;AAAA,MACpB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAGD,IAAI,gBAAgB,QAAQ,eAAe,GAAG,CAAC;AAC/C,IAAIC,kBAAiB,cAAc;;;AC/LnC,SAAS,aAAa,OAAuC;AAC3D,QAAM,gBAAgBC,gBAAY,MAAM,KAAK;AAC7C,QAAM,UAAkC,CAAC;AAEzC,aAAW,cAAc,eAAe;AACtC,QAAI,OAAO,cAAc,UAAU,MAAM,aAAa;AACpD,cAAQ,UAAU,IAAI,cAAc,UAAU;IAChD;EACF;AAEA,SAAO;AACT;AAEA,SAAS,wBAAwB;AAC/B,SAAO,aAAa,SAAS,MAAM;AACrC;AAEA,SAAS,mBAAmB,SAA0C;AACpE,MAAI,OAAO,aAAa,eAAe,OAAO,aAAa,aAAa;AACtE,WAAO,CAAC;EACV;AAEA,UAAQ,QAAQ,aAAa;IAC3B,KAAK,eAAe;AAClB,YAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;AAItC,aAAO,SAAS,WAAW,WAAW,SAClC,sBAAsB,IACtB,CAAC;IACP;IAEA,KAAK,WAAW;AAEd,aAAO,sBAAsB;IAC/B;IAEA,SAAS;AACP,aAAO,CAAC;IACV;EACF;AACF;AAEO,SAAS,qBAAqB,SAA0C;AAM7E,QAAM,sBAAsB,QAAQ,QAAQ,IAAI,QAAQ;AACxD,QAAM,qBAAqB,sBACvB,aAAa,mBAAmB,IAChC,CAAC;AAEL,QAAM,sBAAsB,mBAAmB,OAAO;AAGtD,aAAW,QAAQ,qBAAqB;AACtC,YAAQ,QAAQ;MACd;MACAA,gBAAY,UAAU,MAAM,oBAAoB,IAAI,CAAC;IACvD;EACF;AAEA,QAAM,mBAAmB,YAAY,eAAe,QAAQ,GAAG;AAC/D,QAAM,sBAAsB,OAAO;IACjC,iBAAiB,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,OAAO,KAAK,CAAC;EAC7D;AAIA,aAAW,UAAU,kBAAkB;AACrC,YAAQ,QAAQ,OAAO,UAAU,OAAO,SAAS,CAAC;EACpD;AAEA,SAAO;IACL,GAAG;IACH,GAAG;IACH,GAAG;EACL;AACF;;;ACtDO,IAAK,eAAL,CAAKC,iBAAL;AACLA,eAAA,MAAA,IAAO;AACPA,eAAA,KAAA,IAAM;AACNA,eAAA,MAAA,IAAO;AACPA,eAAA,KAAA,IAAM;AACNA,eAAA,OAAA,IAAQ;AACRA,eAAA,SAAA,IAAU;AACVA,eAAA,QAAA,IAAS;AAPC,SAAAA;AAAA,GAAA,eAAA,CAAA,CAAA;AA4BL,IAAM,cAAN,cAA0B,eAI/B;EACA,YACE,QACA,MACA,UACA,SACA;AACA,UAAM;MACJ,MAAM;QACJ,QAAQ,GAAG,MAAM,IAAI,IAAI;QACzB;QACA;MACF;MACA;MACA;IACF,CAAC;AAED,SAAK,8BAA8B;EACrC;EAEQ,gCAAgC;AACtC,UAAM,EAAE,QAAQ,KAAK,IAAI,KAAK;AAE9B,QAAI,gBAAgB,QAAQ;AAC1B;IACF;AAEA,UAAM,MAAM,SAAS,IAAI;AAGzB,QAAI,QAAQ,MAAM;AAChB;IACF;AAEA,UAAM,eAAe,gBAAgB,IAAI;AACzC,UAAM,cAAwB,CAAC;AAE/B,iBAAa,QAAQ,CAAC,GAAG,cAAc;AACrC,kBAAY,KAAK,SAAS;IAC5B,CAAC;AAED,aAAS;MACP,+EAA+E,MAAM,IAAI,IAAI;IAC/F;EACF;EAEA,MAAM,MAAM,MAGT;AA9GL,QAAAC;AA+GI,UAAM,MAAM,IAAI,IAAI,KAAK,QAAQ,GAAG;AACpC,UAAMC,SAAQ;MACZ;MACA,KAAK,KAAK;OACVD,MAAA,KAAK,sBAAL,gBAAAA,IAAwB;IAC1B;AACA,UAAM,UAAU,qBAAqB,KAAK,OAAO;AAEjD,WAAO;MACL,OAAAC;MACA;IACF;EACF;EAEA,UAAU,MAAmE;AAC3E,UAAM,oBAAoB,KAAK,YAAY,KAAK,QAAQ,MAAM;AAC9D,UAAM,iBAAiB,KAAK,aAAa,MAAM;AAC/C,WAAO,qBAAqB;EAC9B;EAEQ,YAAY,cAA+B;AACjD,WAAO,KAAK,KAAK,kBAAkB,SAC/B,KAAK,KAAK,OAAO,KAAK,YAAY,IAClC,cAAc,KAAK,KAAK,QAAQ,YAAY;EAClD;EAEU,mBAAmB,MAG1B;AA5IL,QAAAD;AA6II,WAAO;MACL,UAAQA,MAAA,KAAK,aAAa,UAAlB,gBAAAA,IAAyB,WAAU,CAAC;MAC5C,SAAS,KAAK,aAAa;IAC7B;EACF;EAEA,MAAM,IAAI,MAAgD;AACxD,UAAM,YAAY,YAAY,KAAK,QAAQ,GAAG;AAC9C,UAAM,gBAAgB,MAAM,iBAAiB,KAAK,OAAO;AACzD,UAAM,iBAAiB,MAAM,kBAAkB,KAAK,QAAQ;AAC5D,UAAM,cAAc,mBAAmB,eAAe,MAAM;AAG5D,YAAQ;MACN,SAAS;QACP,GAAG,aAAa,CAAC,IAAI,KAAK,QAAQ,MAAM,IAAI,SAAS,OACnD,eAAe,MACjB,IAAI,eAAe,UAAU;MAC/B;MACA,SAAS,WAAW;MACpB;IACF;AAEA,YAAQ,IAAI,WAAW,aAAa;AAEpC,YAAQ,IAAI,YAAY,IAAI;AAE5B,YAAQ,IAAI,YAAY,cAAc;AAEtC,YAAQ,SAAS;EACnB;AACF;;;ACxIA,SAAS,kBACP,QACoB;AACpB,SAAO,CAAC,MAAM,UAAU,UAAU,CAAC,MAAM;AACvC,WAAO,IAAI,YAAY,QAAQ,MAAM,UAAU,OAAO;EACxD;AACF;AAWO,IAAM,OAAO;EAClB,KAAK,kBAAkB,IAAI;EAC3B,MAAM,kBAAkB,YAAY,IAAI;EACxC,KAAK,kBAAkB,YAAY,GAAG;EACtC,MAAM,kBAAkB,YAAY,IAAI;EACxC,KAAK,kBAAkB,YAAY,GAAG;EACtC,QAAQ,kBAAkB,YAAY,MAAM;EAC5C,OAAO,kBAAkB,YAAY,KAAK;EAC1C,SAAS,kBAAkB,YAAY,OAAO;AAChD;;;AC3DO,SAAS,UACd,OACuB;AACvB,MAAI;AACF,WAAO,KAAK,MAAM,KAAK;EACzB,QAAQ;AACN,WAAO;EACT;AACF;;;ACIA,SAAS,oBAAoB,eAA6C;AAhB1E,MAAAE,KAAA;AAiBE,QAAM,UAAU,gBAAgB,aAAa;AAC7C,QAAM,cAAc,QAAQ,IAAI,cAAc,KAAK;AACnD,QAAM,cAAc,QAAQ,IAAI,qBAAqB;AAErD,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,2CAA2C;EAC7D;AAEA,QAAM,aAAa,YAAY,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,UAAU;AAC/D,UAAM,CAACC,OAAM,GAAG,IAAI,IAAI,MAAM,KAAK,EAAE,MAAM,GAAG;AAC9C,QAAIA,KAAI,IAAI,KAAK,KAAK,GAAG;AACzB,WAAO;EACT,GAAG,CAAC,CAAgC;AAEpC,QAAM,QAAOD,MAAA,WAAW,SAAX,gBAAAA,IAAiB,MAAM,GAAG;AACvC,QAAM,YAAW,gBAAW,aAAX,mBAAqB,MAAM,GAAG;AAE/C,SAAO;IACL;IACA;IACA;EACF;AACF;AAMO,SAAS,mBACd,MACA,SACe;AACf,QAAM,cAAc,mCAAS,IAAI;AAEjC,MAAI,CAAC,aAAa;AAChB,WAAO;EACT;AAEA,QAAM,CAAC,EAAE,GAAG,UAAU,IAAI,YAAY,MAAM,KAAK;AACjD,QAAM,WAAW,WACd,OAAO,CAAC,MAAM,EAAE,WAAW,WAAW,CAAC,EACvC,IAAI,CAAC,MAAM,EAAE,QAAQ,cAAc,EAAE,CAAC,EAAE,CAAC;AAE5C,MAAI,CAAC,UAAU;AACb,WAAO;EACT;AAEA,QAAM,iBAAiB,IAAI,OAAO,MAAM,QAAQ,EAAE;AAClD,QAAM,SAAS,KACZ,MAAM,cAAc,EACpB,OAAO,CAAC,UAAU,MAAM,WAAW,MAAM,KAAK,MAAM,SAAS,MAAM,CAAC,EACpE,IAAI,CAAC,UAAU,MAAM,UAAU,EAAE,QAAQ,SAAS,EAAE,CAAC;AAExD,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO;EACT;AAEA,QAAM,aAA0C,CAAC;AAEjD,MAAI;AACF,eAAW,SAAS,QAAQ;AAC1B,YAAM,CAAC,gBAAgB,GAAG,IAAI,IAAI,MAAM,MAAM,UAAU;AACxD,YAAM,cAAc,KAAK,KAAK,UAAU;AACxC,YAAM,EAAE,aAAAE,cAAa,UAAU,KAAK,IAClC,oBAAoB,cAAc;AAEpC,YAAM,QACJ,aAAa,SACT,cACA,IAAI,KAAK,CAAC,WAAW,GAAG,UAAU,EAAE,MAAMA,aAAY,CAAC;AAE7D,YAAM,cAAc,WAAW,IAAI;AAEnC,UAAI,gBAAgB,QAAW;AAC7B,mBAAW,IAAI,IAAI;MACrB,WAAW,MAAM,QAAQ,WAAW,GAAG;AACrC,mBAAW,IAAI,IAAI,CAAC,GAAG,aAAa,KAAK;MAC3C,OAAO;AACL,mBAAW,IAAI,IAAI,CAAC,aAAa,KAAK;MACxC;IACF;AAEA,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;;;ACzEO,SAAS,kBAAkB,MAAwC;AAxB1E,MAAAC;AAyBE,QAAM,eAAe,KAAK,YAAY,KAAK,CAAC,eAAe;AACzD,WAAO,WAAW,SAAS;EAC7B,CAAC;AAED,SAAO;IACL,eAAe,6CAAc;IAC7B,gBAAeA,MAAA,6CAAc,SAAd,gBAAAA,IAAoB;EACrC;AACF;AAEA,eAAe,WAAW,OAAoD;AAQ5E,QAAM,EAAE,OAAAC,OAAM,IAAG,MAAM,OAAO,uBAAS,EAAE,MAAM,CAAC,UAAU;AAAC,YAAQ,MAAM,4LAA4L;AAAG,UAAM;EAAK,CAAC;AAEpR,MAAI;AACF,UAAM,MAAMA,OAAM,KAAK;AACvB,WAAO,kBAAkB,GAAG;EAC9B,SAAS,OAAO;AACd,WAAO;EACT;AACF;AAUA,SAAS,0BACP,WACA,KACA,OACA;AACA,QAAM,aAAa,EAAE,UAAU;AAE/B,aAAW,CAAC,KAAK,SAAS,KAAK,OAAO,QAAQ,GAAG,GAAG;AAClD,QAAI,EAAE,OAAO,QAAQ;AACnB,YAAM,IAAI,MAAM,kCAAkC,GAAG,KAAK;IAC5D;AAEA,eAAW,WAAW,WAAW;AAC/B,YAAM,CAAC,UAAU,GAAG,aAAa,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ;AAChE,YAAM,QAAQ,cAAc,QAAQ;AACpC,UAAI,SAA8B;AAElC,iBAAW,QAAQ,OAAO;AACxB,YAAI,EAAE,QAAQ,SAAS;AACrB,gBAAM,IAAI,MAAM,aAAa,KAAK,yBAAyB;QAC7D;AAEA,iBAAS,OAAO,IAAI;MACtB;AAEA,aAAO,QAAQ,IAAI,MAAM,GAAG;IAC9B;EACF;AAEA,SAAO,WAAW;AACpB;AAEA,eAAe,gBAAgB,SAAgD;AA7F/E,MAAAD;AA8FE,UAAQ,QAAQ,QAAQ;IACtB,KAAK,OAAO;AACV,YAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAM,QAAQ,IAAI,aAAa,IAAI,OAAO;AAC1C,YAAM,YAAY,IAAI,aAAa,IAAI,WAAW,KAAK;AAEvD,aAAO;QACL;QACA,WAAW,UAAU,SAAS;MAChC;IACF;IAEA,KAAK,QAAQ;AAGX,YAAM,eAAe,QAAQ,MAAM;AAGnC,WACEA,MAAA,QAAQ,QAAQ,IAAI,cAAc,MAAlC,gBAAAA,IAAqC,SAAS,wBAC9C;AACA,cAAM,eAAe;UACnB,MAAM,aAAa,KAAK;UACxB,QAAQ;QACV;AAEA,YAAI,CAAC,cAAc;AACjB,iBAAO;QACT;AAEA,cAAM,EAAE,YAAY,KAAK,GAAG,MAAM,IAAI;AACtC,cAAM,mBACJ;UACE;QACF,KAAK,CAAC;AAER,YAAI,CAAC,iBAAiB,OAAO;AAC3B,iBAAO;QACT;AAEA,cAAM,YAAY,UAAsC,OAAO,EAAE,KAAK,CAAC;AACvE,cAAM,YAAY,iBAAiB,YAC/B;UACE,iBAAiB;UACjB;UACA;QACF,IACA,CAAC;AAEL,eAAO;UACL,OAAO,iBAAiB;UACxB;QACF;MACF;AAGA,YAAM,cAIF,MAAM,aAAa,KAAK,EAAE,MAAM,MAAM,IAAI;AAE9C,UAAI,2CAAa,OAAO;AACtB,cAAM,EAAE,OAAO,UAAU,IAAI;AAE7B,eAAO;UACL;UACA;QACF;MACF;IACF;IAEA;AACE,aAAO;EACX;AACF;AAMA,eAAsB,oBACpB,SAC+B;AAC/B,QAAM,QAAQ,MAAM,gBAAgB,OAAO;AAE3C,MAAI,CAAC,SAAS,CAAC,MAAM,OAAO;AAC1B;EACF;AAEA,QAAM,EAAE,OAAO,UAAU,IAAI;AAC7B,QAAM,eAAe,MAAM,WAAW,KAAK;AAE3C,MAAI,wBAAwB,OAAO;AACjC,UAAM,mBAAmB,YAAY,QAAQ,GAAG;AAEhD,UAAM,IAAI;MACR,SAAS;QACP;QACA,QAAQ;QACR;QACA,aAAa;MACf;IACF;EACF;AAEA,SAAO;IACL,OAAO,MAAM;IACb,eAAe,aAAa;IAC5B,eAAe,aAAa;IAC5B;EACF;AACF;;;ACrIO,SAAS,eACd,OACuB;AACvB,MAAI,SAAS,MAAM;AACjB,WAAO;EACT;AAEA,SAAO,OAAO,UAAU,YAAY,UAAU,SAAS,iBAAiB;AAC1E;AAEO,IAAM,kBAAN,MAAM,wBAAuB,eAIlC;EAQA,YACE,eACA,eACA,UACA,UACA,SACA;AACA,QAAI,wBAAwB;AAE5B,QAAI,eAAe,aAAa,GAAG;AACjC,YAAM,aAAa,kBAAkB,aAAa;AAElD,UAAI,WAAW,kBAAkB,eAAe;AAC9C,cAAM,IAAI;UACR,2GAA2G,aAAa,eAAe,WAAW,aAAa;QACjK;MACF;AAEA,UAAI,CAAC,WAAW,eAAe;AAC7B,cAAM,IAAI;UACR;QACF;MACF;AAEA,8BAAwB,WAAW;IACrC;AAEA,UAAM,SACJ,kBAAkB,QACd,GAAG,aAAa,aAAa,SAAS,SAAS,CAAC,MAChD,GAAG,aAAa,IAAI,qBAAqB,aAAa,SAAS,SAAS,CAAC;AAE/E,UAAM;MACJ,MAAM;QACJ;QACA;QACA,eAAe;MACjB;MACA;MACA;IACF,CAAC;AA/CK;AAiDN,SAAK,WAAW;EAClB;;;;;;EAOA,MAAM,kCACJ,SACiD;AACjD,QAAI,CAAC,gBAAe,mBAAmB,IAAI,OAAO,GAAG;AACnD,sBAAe,mBAAmB;QAChC;QACA,MAAM,oBAAoB,OAAO,EAAE,MAAM,CAAC,UAAU;AAElD,kBAAQ,MAAM,KAAK;AACnB,iBAAO;QACT,CAAC;MACH;IACF;AAEA,WAAO,gBAAe,mBAAmB,IAAI,OAAO;EACtD;EAEA,MAAM,MAAM,MAAiE;AAK3E,UAAME,SAAQ,gBAAgB,IAAI,IAAI,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ;AACtE,UAAM,UAAU,qBAAqB,KAAK,OAAO;AAEjD,QAAI,CAACA,OAAM,SAAS;AAClB,aAAO,EAAE,OAAAA,QAAO,QAAQ;IAC1B;AAEA,UAAM,eAAe,MAAM,KAAK;MAC9B,KAAK;IACP;AAEA,QAAI,OAAO,iBAAiB,aAAa;AACvC,aAAO,EAAE,OAAAA,QAAO,QAAQ;IAC1B;AAEA,WAAO;MACL,OAAAA;MACA;MACA,OAAO,aAAa;MACpB,eAAe,aAAa;MAC5B,eAAe,aAAa;MAC5B,WAAW,aAAa;IAC1B;EACF;EAEA,UAAU,MAGP;AACD,QAAI,KAAK,aAAa,kBAAkB,QAAW;AACjD,aAAO;IACT;AAEA,QAAI,CAAC,KAAK,aAAa,iBAAiB,KAAK,KAAK,kBAAkB,OAAO;AACzE,YAAM,YAAY,YAAY,KAAK,QAAQ,GAAG;AAE9C,eAAS,KAAK,6CACwB,KAAK,QAAQ,MAAM,IAAI,SAAS;;4NAEgJ;AACtN,aAAO;IACT;AAEA,UAAM,2BACJ,KAAK,KAAK,kBAAkB,SAC5B,KAAK,aAAa,kBAAkB,KAAK,KAAK;AAEhD,UAAM,2BACJ,KAAK,KAAK,yBAAyB,SAC/B,KAAK,KAAK,cAAc,KAAK,KAAK,aAAa,iBAAiB,EAAE,IAClE,KAAK,aAAa,kBAAkB,KAAK,KAAK;AAEpD,WACE,KAAK,aAAa,MAAM,WACxB,4BACA;EAEJ;EAEU,mBAAmB,MAG1B;AACD,WAAO;MACL,OAAO,KAAK,aAAa,SAAS;MAClC,eAAe,KAAK,aAAa,iBAAiB;MAClD,WAAW,KAAK,aAAa,aAAa,CAAC;MAC3C,SAAS,KAAK,aAAa;IAC7B;EACF;EAEA,MAAM,IAAI,MAIP;AACD,UAAM,gBAAgB,MAAM,iBAAiB,KAAK,OAAO;AACzD,UAAM,iBAAiB,MAAM,kBAAkB,KAAK,QAAQ;AAC5D,UAAM,cAAc,mBAAmB,eAAe,MAAM;AAC5D,UAAM,cAAc,KAAK,aAAa,gBAClC,GAAG,KAAK,aAAa,aAAa,IAAI,KAAK,aAAa,aAAa,KACrE,aAAa,KAAK,aAAa,aAAa;AAGhD,YAAQ;MACN,SAAS;QACP,GAAG,aAAa,CAAC,IAAI,WAAW,OAAO,eAAe,MAAM,IAC1D,eAAe,UACjB;MACF;MACA,SAAS,WAAW;MACpB;IACF;AAEA,YAAQ,IAAI,YAAY,aAAa;AAErC,YAAQ,IAAI,YAAY,IAAI;AAE5B,YAAQ,IAAI,aAAa,cAAc;AAEvC,YAAQ,SAAS;EACnB;AACF;AAnLE,cAPW,iBAOJ,sBAAqB,oBAAI,QAG9B;AAVG,IAAM,iBAAN;;;ACxCP,SAAS,2BACP,eACA,KACuB;AACvB,SAAO,CAAC,eAAe,UAAU,UAAU,CAAC,MAAM;AAChD,WAAO,IAAI;MACT;MACA;MACA;MACA;MACA;IACF;EACF;AACF;AAEA,SAAS,8BAA8B,KAAW;AAChD,SAAO,CAIL,aAKG;AACH,WAAO,IAAI,eAAe,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK,QAAQ;EAClE;AACF;AAEA,IAAM,0BAA0B;;;;;;;;;;;EAW9B,OAAO,2BAA2B,SAA8B,GAAG;;;;;;;;;;;;EAanE,UAAU,2BAA2B,YAAiC,GAAG;;;;;;;;;;;EAYzE,WAAW,8BAA8B,GAAG;AAC9C;AAEA,SAAS,kBAAkB,KAA2C;AACpE,SAAO;IACL,WAAW,8BAA8B,GAAG;IAC5C,OAAO,2BAA2B,SAA8B,GAAG;IACnE,UAAU,2BAA2B,YAAiC,GAAG;EAC3E;AACF;AAWO,IAAM,UAAU;EACrB,GAAG;;;;;;;;;;EAWH,MAAM;AACR;;;ACjHO,IAAM,WAAW,OAAO,UAAU;AAClC,IAAM,UAAU,OAAO,SAAS;AACvC,IAAM,0BAA0B,OAAO,yBAAyB;AAChE,IAAM,qBAAqB,OAAO,oBAAoB;AAtCtD;AAwCO,IAAM,mBAAN,MAAuB;EAQ5B,YAA6B,KAAW;AAPvB;AAEV;AACA;AAEP,wBAAW;AAEkB,SAAA,MAAA;AAC3B,SAAK,KAAK,gBAAgB;AAE1B,SAAK,QAAQ,IAAI,IAAI,QAAQ;AAC7B,SAAK,YAAY,aAAa,IAAI,MAAM,CAAC;AACzC,SAAK,SAAS;EAChB;EAEO,MAAM,MAGoB;AA3DnC,QAAAC;AA4DI,UAAM,YAAY,IAAI,IAAI,KAAK,GAAG;AAOlC,cAAU,WAAW,UAAU,SAAS,QAAQ,kBAAkB,GAAG;AAErE,UAAMC,SAAQ;MACZ;MACA,KAAK;OACLD,MAAA,KAAK,sBAAL,gBAAAA,IAAwB;IAC1B;AAEA,WAAO;MACL,OAAAC;IACF;EACF;EAEO,UAAU,MAGL;AACV,WAAO,KAAK,aAAa,MAAM;EACjC;EAEA,MAAa,IACX,YACA,mBACkB;AAClB,UAAM,eAAe,KAAK,MAAM;MAC9B,KAAK,WAAW,OAAO;MACvB;IACF,CAAC;AAED,QAAI,CAAC,KAAK,UAAU,EAAE,KAAK,WAAW,OAAO,KAAK,aAAa,CAAC,GAAG;AACjE,aAAO;IACT;AAEA,UAAM,qBAAiD;MACrD,GAAG;MACH,QAAQ,aAAa,MAAM,UAAU,CAAC;IACxC;AAEA,WAAO,KAAK,QAAQ,kBAAkB;EACxC;EAEU,QAAQ,YAAiD;AAEjE,eAAW,OAAO;MAChB;MACA,8BAA8B,IAAI;IACpC;AACA,eAAW,OAAO;MAChB;MACA,8BAA8B,IAAI;IACpC;AAEA,eAAW,OAAO;MAChB;MACA,8BAA8B,IAAI;IACpC;AACA,eAAW,OAAO;MAChB;MACA,8BAA8B,IAAI;IACpC;AACA,eAAW,OAAO;MAChB;MACA,8BAA8B,IAAI;IACpC;AACA,eAAW,OAAO;MAChB;MACA,8BAA8B,IAAI;IACpC;AAIA,WAAO,KAAK,QAAQ,EAAE,KAAK,cAAc,UAAU;EACrD;AACF;AA9Fa;AAgGb,SAAS,8BAA8B,SAA2B;AAChE,SAAO,SAAS,wBAAwB,OAAc;AACpD,UAAM,uBAAuB,QAAQ,IAAI,OAAO,uBAAuB;AAIvE,QAAI,wBAAwB,QAAQ,OAAO,sBAAsB;AAC/D,YAAM,yBAAyB;AAC/B;IACF;AAEA,WAAO,eAAe,OAAO,oBAAoB;MAC/C,QAA8B;AAC5B,eAAO,eAAe,OAAO,yBAAyB;UACpD,OAAO,QAAQ;QACjB,CAAC;MACH;MACA,cAAc;IAChB,CAAC;AAID,QAAI,CAAC,QAAQ,IAAI,OAAO,uBAAuB,GAAG;AAChD,YAAM,kBAAkB,IAAI,MAAM,MAAM,iBAAiB;QACvD,OAAO,CAAC,QAAQ,SAAS,SAAS;AAtK1C,cAAAD;AAuKU,WAAAA,MAAA,QAAQ,IAAI,OAAO,kBAAkB,MAArC,gBAAAA,IAAwC,KAAK;AAC7C,iBAAO,QAAQ,MAAM,QAAQ,SAAS,IAAI;QAC5C;MACF,CAAC;AAED,aAAO,eAAe,OAAO,yBAAyB;QACpD,OAAO;;QAEP,cAAc;MAChB,CAAC;IACH;EACF;AACF;;;AC7KO,IAAM,6BAAN,MAAiE;EAGtE,cAAc;AAFN;AAGN,SAAK,QAAQ,oBAAI,IAAI;EACvB;EAEA,MAAa,IAAI,QAA0D;AACzE,SAAK,MAAM,IAAI,OAAO,IAAI,EAAE,IAAI,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC;EACnE;EAEO,SAAoD;AACzD,WAAO,QAAQ,QAAQ,MAAM,KAAK,KAAK,MAAM,OAAO,CAAC,CAAC;EACxD;EAEA,MAAa,WAAW,WAAyC;AAC/D,eAAW,YAAY,WAAW;AAChC,WAAK,MAAM,OAAO,QAAQ;IAC5B;EACF;AACF;;;ACnBA,IAAM,UAAU;AAChB,IAAM,gBAAgB;AAEf,IAAM,gCAAN,MAAoE;EAGzE,cAAc;AAFN;AAGN,SAAK,KAAK,KAAK,eAAe;EAChC;EAEA,MAAa,IAAI,QAA0D;AACzE,UAAM,UAAU,IAAI,gBAAsB;AAC1C,UAAM,QAAQ,MAAM,KAAK,SAAS;AAQlC,UAAM,UAAU,MAAM,IAAI;MACxB,IAAI,OAAO;MACX,KAAK,OAAO,IAAI;IAClB,CAAqC;AAErC,YAAQ,YAAY,MAAM;AACxB,cAAQ,QAAQ;IAClB;AACA,YAAQ,UAAU,MAAM;AAEtB,cAAQ,MAAM,QAAQ,KAAK;AAC3B,cAAQ;QACN,IAAI;UACF,mCAAmC,OAAO,EAAE;QAC9C;MACF;IACF;AAEA,WAAO;EACT;EAEA,MAAa,SAAoD;AAC/D,UAAM,UAAU,IAAI,gBAAkD;AACtE,UAAM,QAAQ,MAAM,KAAK,SAAS;AAClC,UAAM,UAAU,MAAM,OAAO;AAI7B,YAAQ,YAAY,MAAM;AACxB,cAAQ,QAAQ,QAAQ,MAAM;IAChC;AACA,YAAQ,UAAU,MAAM;AAEtB,cAAQ,IAAI,QAAQ,KAAK;AACzB,cAAQ;QACN,IAAI;UACF;QACF;MACF;IACF;AAEA,WAAO;EACT;EAEA,MAAa,WAAW,WAAyC;AAC/D,UAAM,UAAU,IAAI,gBAAsB;AAC1C,UAAM,QAAQ,MAAM,KAAK,SAAS;AAElC,eAAW,YAAY,WAAW;AAChC,YAAM,OAAO,QAAQ;IACvB;AAEA,UAAM,YAAY,aAAa,MAAM;AACnC,cAAQ,QAAQ;IAClB;AACA,UAAM,YAAY,UAAU,MAAM;AAEhC,cAAQ,MAAM,MAAM,YAAY,KAAK;AACrC,cAAQ;QACN,IAAI;UACF,uCAAuC,UAAU,KAAK,IAAI,CAAC;QAC7D;MACF;IACF;AAEA,WAAO;EACT;EAEA,MAAc,iBAAuC;AACnD,UAAM,UAAU,IAAI,gBAA6B;AACjD,UAAM,UAAU,UAAU,KAAK,SAAS,CAAC;AAEzC,YAAQ,YAAY,CAAC,EAAE,cAAc,MAAM;AACzC,YAAM,KAAK,QAAQ,IAAI,eAAgB,QAAQ;AAE/C,UAAI,GAAG,iBAAiB,SAAS,aAAa,GAAG;AAC/C,eAAO,QAAQ,QAAQ,EAAE;MAC3B;IACF;AAEA,YAAQ,kBAAkB,OAAO,EAAE,cAAc,MAAM;AACrD,YAAM,KAAK,QAAQ,IAAI,eAAgB,QAAQ;AAC/C,UAAI,GAAG,iBAAiB,SAAS,aAAa,GAAG;AAC/C;MACF;AAEA,YAAM,QAAQ,GAAG,kBAAkB,eAAe,EAAE,SAAS,KAAK,CAAC;AACnE,YAAM,YAAY,aAAa,MAAM;AACnC,gBAAQ,QAAQ,EAAE;MACpB;AACA,YAAM,YAAY,UAAU,MAAM;AAEhC,gBAAQ,MAAM,MAAM,YAAY,KAAK;AACrC,gBAAQ;UACN,IAAI;YACF;UACF;QACF;MACF;IACF;AACA,YAAQ,UAAU,MAAM;AAEtB,cAAQ,MAAM,QAAQ,KAAK;AAC3B,cAAQ;QACN,IAAI;UACF;QACF;MACF;IACF;AAEA,WAAO;EACT;EAEA,MAAc,WAAoC;AAChD,UAAM,KAAK,MAAM,KAAK;AACtB,WAAO,GAAG,YAAY,eAAe,WAAW,EAAE,YAAY,aAAa;EAC7E;AACF;;;AClHO,IAAM,yBAAN,MAA6B;EAKlC,YAAoB,SAA2B;AAJvC;AACA;AACA;AAEY,SAAA,UAAA;AAGlB,SAAK,QACH,OAAO,cAAc,cACjB,IAAI,8BAA8B,IAClC,IAAI,2BAA2B;AAErC,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,aAAa,oBAAI,IAAI;AAE1B,SAAK,QAAQ,iBAAiB,WAAW,CAACE,aAAY;AAxC1D,UAAAC;AAyCM,YAAIA,MAAAD,SAAQ,SAAR,gBAAAC,IAAc,UAAS,aAAa;AACtC,aAAK,sBAAsB;MAC7B;IACF,CAAC;AAED,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,iBAAiB,WAAW,OAAOD,aAAY;AA/C5D,YAAAC;AAgDQ,cAAIA,MAAAD,SAAQ,SAAR,gBAAAC,IAAc,UAAS,mBAAmB;AAC5C,gBAAM,KAAK,qBAAqB;QAClC;MACF,CAAC;IACH;EACF;EAEA,MAAc,wBAAwB;AACpC,UAAM,gBAAgB,MAAM,KAAK,MAAM,OAAO;AAE9C,SAAK,aAAa,IAAI;MACpB,cAAc,IAAI,CAAC,WAAW;AAC5B,cAAM,gBAAgB,KAAK,eAAe,IAAI,OAAO,EAAE;AAMvD,YAAI,eAAe;AACjB,iBAAO;QACT;AAEA,eAAO,IAAI;UACT,OAAO;UACP,IAAI,IAAI,OAAO,GAAG;UAClB,KAAK;QACP;MACF,CAAC;IACH;EACF;EAEA,MAAc,uBAAsC;AAClD,UAAM,KAAK,MAAM,WAAW,MAAM,KAAK,KAAK,eAAe,KAAK,CAAC,CAAC;AAClE,SAAK,eAAe,MAAM;AAC1B,UAAM,KAAK,sBAAsB;AACjC,SAAK,gCAAgC;EACvC;;;;EAKA,IAAI,UAAkD;AACpD,WAAO,KAAK;EACd;;;;;EAMQ,kCAAwC;AAC9C,SAAK,QAAQ,YAAY,EAAE,MAAM,YAAY,CAAC;EAChD;EAEA,MAAc,UACZ,QACe;AACf,UAAM,KAAK,MAAM,IAAI,MAAM;AAG3B,UAAM,KAAK,sBAAsB;AACjC,SAAK,gCAAgC;EACvC;;;;;;;EAQA,MAAa,cACX,QACe;AAIf,SAAK,eAAe,IAAI,OAAO,IAAI,MAAM;AAGzC,UAAM,KAAK,UAAU,MAAM;AAK3B,UAAM,0BAA0B,CAC9BD,aACG;AACH,YAAM,EAAE,MAAM,QAAQ,IAAIA,SAAQ;AAGlC,UACE,OAAO,YAAY,YACnB,cAAc,WACd,QAAQ,aAAa,OAAO,IAC5B;AACA;MACF;AAEA,cAAQ,MAAM;QACZ,KAAK,mBAAmB;AACtB,iBAAO,KAAK,QAAQ,IAAI;AACxB;QACF;QAEA,KAAK,oBAAoB;AACvB,iBAAO,MAAM,QAAQ,MAAM,QAAQ,MAAM;AACzC;QACF;MACF;IACF;AAEA,UAAM,kBAAkB,IAAI,gBAAgB;AAE5C,SAAK,QAAQ,iBAAiB,WAAW,yBAAyB;MAChE,QAAQ,gBAAgB;IAC1B,CAAC;AAID,WAAO,iBAAiB,SAAS,MAAM,gBAAgB,MAAM,GAAG;MAC9D,MAAM;IACR,CAAC;EACH;AACF;AASO,IAAM,kCAAN,MAEP;EACE,YACkB,IACA,KACR,SACR;AAHgB,SAAA,KAAA;AACA,SAAA,MAAA;AACR,SAAA,UAAA;EACP;EAEH,KAAK,MAA2B;AAC9B,SAAK,QAAQ,YAAY;MACvB,MAAM;MACN,SAAS;QACP,UAAU,KAAK;QACf;MACF;IACF,CAAqC;EACvC;EAEA,MAAM,MAA2B,QAAmC;AAClE,SAAK,QAAQ,YAAY;MACvB,MAAM;MACN,SAAS;QACP,UAAU,KAAK;QACf;QACA;MACF;IACF,CAAqC;EACvC;EAEA,iBACE,OACA,WAIA,UACM;AACN,UAAM,IAAI;MACR;IACF;EACF;EAEA,oBACE,QACA,WAIA,UACM;AACN,UAAM,IAAI;MACR;IACF;EACF;AACF;;;ACnOA,SAAS,4BACP,SACiD;AACjD,SAAO,OAAO,QAAQ,IAAI,SAAS,OAAO,MAAM;AAClD;AAEA,IAAM,mBAAmB,IAAI,iBAAiB,8BAA8B;AAE5E,IAAI,4BAA4B,gBAAgB,GAAG;AAGjD,mBAAiB,MAAM;AACzB;AAuEA,SAAS,2BAA2B,KAA0B;AAC5D,YAAU,KAAK,mDAAmD;AAElE;IACE,OAAO,GAAG;IACV;IACA,OAAO;EACT;AAEA,QAAM,gBAAgB,IAAI,uBAAuB,gBAAgB;AAEjE,SAAO;IACL,IAAI,UAAU;AACZ,aAAO,cAAc;IACvB;IACA,iBAAiB,OAAO,UAAU;AAChC,YAAM,UAAU,IAAI,iBAAiB,GAAG;AAMxC,cAAQ,QAAQ,EAAE,GAAG,cAAc,OAAO,EAAE,OAAO,MAAM;AACvD,cAAM,cAAc,cAAc,MAAM;MAC1C,CAAC;AAMD,cAAQ,QAAQ,EAAE,GAAG,OAAO,QAAQ;AAEpC,aAAO;IACT;IAEA,UAAU,MAAM;AAId,WAAK,gBAAgB,CAAC,GAAG,IAAI;IAC/B;IAEA,gBAAgB,SAAS,MAAM;AAC7B,YAAM,gBAAgB,MAAM,UACzB,OAAO,OAAO,EACd,IAAI,CAAC,WAAW,OAAO,EAAE;AAE5B,oBAAc,QAAQ,QAAQ,CAAC,gBAAgB;AAC7C,YAAI,CAAC,cAAc,SAAS,YAAY,EAAE,GAAG;AAC3C,sBAAY,KAAK,IAAI;QACvB;MACF,CAAC;IACH;EACF;AACF;AAWO,IAAM,KAAK;EAChB,MAAM;AACR;;;ACpJO,IAAM,cAAc,OACzB,UACA,SACA,sBACkC;AAClC,QAAM,SAAS,MAAM,gBAAgB;IACnC;IACA,WAAW,gBAAgB;IAC3B;IACA;EACF,CAAC;AAED,SAAO,iCAAQ;AACjB;;;AChBO,IAAM,WAA0B,OAAO,UAAU;AAZxD,IAAAE;AAwCO,IAAM,gBAAN,MAAM,sBAEH,cAAc;EAGtB,YAAY,MAAiC,MAAyB;AACpE,UAAM,eAAe,sBAAsB,IAAI;AAC/C,UAAM,MAAkB,YAAY;AAJtC,wBAAUA,KAAsB;AAK9B,qBAAiB,MAAM,YAAY;EACrC;EAEA,OAAO,QAA2B;AAChC,WAAO,MAAM,MAAM;EACrB;;;;;;;EAQA,OAAO,KACL,MACA,MACwB;AACxB,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,YAAY;IACvD;AAKA,QAAI,CAAC,aAAa,QAAQ,IAAI,gBAAgB,GAAG;AAC/C,mBAAa,QAAQ;QACnB;QACA,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI;MAC5C;IACF;AAEA,WAAO,IAAI,cAAa,MAAM,YAAY;EAC5C;;;;;;;EAQA,OAAO,KACL,MACA,MACwB;AACxB,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,kBAAkB;IAC7D;AAMA,UAAM,eAAe,KAAK,UAAU,IAAI;AAExC,QAAI,CAAC,aAAa,QAAQ,IAAI,gBAAgB,GAAG;AAC/C,mBAAa,QAAQ;QACnB;QACA,eAAe,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,SAAS,IAAI;MAC5D;IACF;AAEA,WAAO,IAAI,cAAa,cAA0B,YAAY;EAChE;;;;;;;EAQA,OAAO,IACL,MACA,MACwB;AACxB,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,UAAU;IACrD;AAEA,WAAO,IAAI,cAAa,MAAM,YAAY;EAC5C;;;;;;;EAQA,OAAO,KACL,MACA,MACwB;AACxB,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,WAAW;IACtD;AAEA,WAAO,IAAI,cAAa,MAAM,YAAY;EAC5C;;;;;;;;;;EAWA,OAAO,YACL,MACA,MAC+C;AAC/C,UAAM,eAAe,sBAAsB,IAAI;AAE/C,QAAI,CAAC,aAAa,QAAQ,IAAI,cAAc,GAAG;AAC7C,mBAAa,QAAQ,IAAI,gBAAgB,0BAA0B;IACrE;AAEA,QAAI,QAAQ,CAAC,aAAa,QAAQ,IAAI,gBAAgB,GAAG;AACvD,mBAAa,QAAQ,IAAI,kBAAkB,KAAK,WAAW,SAAS,CAAC;IACvE;AAEA,WAAO,IAAI,cAAa,MAAM,YAAY;EAC5C;;;;;;;;;EAUA,OAAO,SACL,MACA,MACwB;AACxB,WAAO,IAAI,cAAa,MAAM,sBAAsB,IAAI,CAAC;EAC3D;AACF;AAvJYA,MAAA;AAHL,IAAM,eAAN;;;ACtCA,IAAM,8BAA8B;AACpC,IAAM,2BAA2B;AACjC,IAAM,2BAA2B;AACjC,IAAM,4BAA4B;AAEzC,SAAS,2BAAmC;AAC1C,MAAI,cAAc,GAAG;AACnB,WAAO;EACT;AAEA,SAAO,KAAK;IACV,KAAK,OAAO,KAAK,2BAA2B,4BAC1C;EACJ;AACF;AAcA,eAAsB,MACpB,gBACe;AACf,MAAI;AAEJ,MAAI,OAAO,mBAAmB,UAAU;AACtC,YAAQ,gBAAgB;MACtB,KAAK,YAAY;AAGf,oBAAY;AACZ;MACF;MACA,KAAK,QAAQ;AACX,oBAAY,yBAAyB;AACrC;MACF;MACA,SAAS;AACP,cAAM,IAAI;UACR,mDAAmD,cAAc;QACnE;MACF;IACF;EACF,WAAW,OAAO,mBAAmB,aAAa;AAEhD,gBAAY,yBAAyB;EACvC,OAAO;AAGL,QAAI,iBAAiB,6BAA6B;AAChD,YAAM,IAAI;QACR,wDAAwD,cAAc,4DAA4D,2BAA2B;MAC/J;IACF;AAEA,gBAAY;EACd;AAEA,SAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,SAAS,CAAC;AAChE;;;ACrDO,SAAS,OAAO,OAA2B,MAA6B;AAI7E,QAAM,UAAU,IAAI;;;IAGlB,iBAAiB,UAAU,MAAM,MAAM,IAAI;IAC3C;EACF;AAEA;IACE,CAAC,QAAQ;IACT;IACA,QAAQ;IACR,QAAQ;EACV;AAEA,QAAM,eAAe,QAAQ,MAAM;AAQnC,eAAa,QAAQ,OAAO,UAAU,iBAAiB;AAEvD,SAAO;AACT;;;AC7BO,SAAS,cAAiC;AAC/C,SAAO,IAAI,SAAS,MAAM;IACxB,QAAQ;IACR,YAAY;IACZ,SAAS;MACP,mBAAmB;IACrB;EACF,CAAC;AACH;;;AC8CA,aAAa;", "names": ["StatusCodeColor", "_a", "value", "result", "char", "prefix", "_a", "i", "symbol", "_a", "cleanUrl", "parse", "source_default", "source_default", "HttpMethods", "_a", "match", "_a", "name", "contentType", "_a", "parse", "match", "_a", "match", "message", "_a", "_a"]}
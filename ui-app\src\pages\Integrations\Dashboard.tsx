import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Database, 
  Zap, 
  Activity, 
  Settings, 
  Plus, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  TrendingUp,
  Users,
  FileText,
  Globe
} from 'lucide-react';
import SectionHeader from '../../components/SectionHeader';

interface DashboardStats {
  totalConnections: number;
  activeConnections: number;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  totalWebhooks: number;
  activeWebhooks: number;
}

interface RecentActivity {
  id: string;
  type: 'sync' | 'webhook' | 'connection';
  title: string;
  description: string;
  status: 'success' | 'error' | 'warning' | 'info';
  timestamp: string;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalConnections: 0,
    activeConnections: 0,
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    totalWebhooks: 0,
    activeWebhooks: 0,
  });

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data loading
    setTimeout(() => {
      setStats({
        totalConnections: 8,
        activeConnections: 6,
        totalSyncs: 156,
        successfulSyncs: 148,
        failedSyncs: 8,
        totalWebhooks: 5,
        activeWebhooks: 4,
      });

      setRecentActivity([
        {
          id: '1',
          type: 'sync',
          title: 'Salesforce Accounts Sync',
          description: 'Successfully synced 145 records',
          status: 'success',
          timestamp: '2 minutes ago',
        },
        {
          id: '2',
          type: 'webhook',
          title: 'New Contact Created',
          description: 'Webhook received from Zoho CRM',
          status: 'success',
          timestamp: '5 minutes ago',
        },
        {
          id: '3',
          type: 'sync',
          title: 'HubSpot Deals Sync',
          description: 'Failed due to connection timeout',
          status: 'error',
          timestamp: '15 minutes ago',
        },

        {
          id: '5',
          type: 'connection',
          title: 'Connection Tested',
          description: 'Mailchimp connection verified',
          status: 'success',
          timestamp: '2 hours ago',
        },
      ]);

      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-blue-500" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'sync':
        return <Zap className="w-4 h-4" />;
      case 'webhook':
        return <Globe className="w-4 h-4" />;
      case 'connection':
        return <Database className="w-4 h-4" />;

      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<Zap className="h-7 w-7" />}
        title="Integrations Dashboard"
        subtitle="Monitor and manage your data integrations"
        actions={
          <>
            <Link
              to="/integrations/connections/new"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Connection
            </Link>

          </>
        }
      />

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Connections */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Database className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Connections</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.activeConnections}/{stats.totalConnections}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Active</span>
              <span className="text-green-600 font-medium">
                {Math.round((stats.activeConnections / stats.totalConnections) * 100)}%
              </span>
            </div>
            <div className="mt-2 bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${(stats.activeConnections / stats.totalConnections) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>



        {/* Syncs */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Zap className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Sync Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round((stats.successfulSyncs / stats.totalSyncs) * 100)}%
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Success</span>
              <span className="text-green-600 font-medium">
                {stats.successfulSyncs}/{stats.totalSyncs}
              </span>
            </div>
            <div className="mt-2 bg-gray-200 rounded-full h-2">
              <div
                className="bg-purple-600 h-2 rounded-full"
                style={{ width: `${(stats.successfulSyncs / stats.totalSyncs) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Webhooks */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Globe className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Webhooks</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.activeWebhooks}/{stats.totalWebhooks}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Active</span>
              <span className="text-green-600 font-medium">
                {Math.round((stats.activeWebhooks / stats.totalWebhooks) * 100)}%
              </span>
            </div>
            <div className="mt-2 bg-gray-200 rounded-full h-2">
              <div
                className="bg-orange-600 h-2 rounded-full"
                style={{ width: `${(stats.activeWebhooks / stats.totalWebhooks) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link
          to="/integrations/connections"
          className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center">
            <Database className="w-8 h-8 text-blue-600" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Manage Connections</h3>
              <p className="text-gray-600">Configure and monitor your data sources</p>
            </div>
          </div>
        </Link>



        <Link
          to="/integrations/monitoring"
          className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center">
            <Activity className="w-8 h-8 text-purple-600" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Sync Monitoring</h3>
              <p className="text-gray-600">Track sync performance and logs</p>
            </div>
          </div>
        </Link>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {getTypeIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                  <p className="text-sm text-gray-500">{activity.description}</p>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(activity.status)}
                  <span className="text-sm text-gray-500">{activity.timestamp}</span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6">
            <Link
              to="/integrations/monitoring"
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              View all activity →
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard; 
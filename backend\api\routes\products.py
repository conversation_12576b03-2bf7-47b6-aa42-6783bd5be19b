from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from typing import Optional
import uuid
from datetime import datetime
import math

from core.db import get_db
from models.product import (
    Product, 
    ProductCreate, 
    ProductUpdate, 
    ProductResponse, 
    ProductListResponse, 
    ProductListItem,
    ProductQueryParams
)

router = APIRouter()

# Helper function to generate product ID
def generate_product_id() -> str:
    return f"prod_{uuid.uuid4().hex[:12]}"

# Helper function to build query filters
def build_product_filters(db_query, params: ProductQueryParams):
    """Build SQLAlchemy query filters based on parameters"""
    
    if params.search:
        search_term = f"%{params.search.lower()}%"
        db_query = db_query.filter(
            or_(
                Product.name.ilike(search_term),
                Product.sku.ilike(search_term),
                Product.description.ilike(search_term)
            )
        )
    
    if params.is_visible is not None:
        db_query = db_query.filter(Product.is_visible == params.is_visible)
    
    if params.is_featured is not None:
        db_query = db_query.filter(Product.is_featured == params.is_featured)
    
    if params.sync_status:
        db_query = db_query.filter(Product.sync_status == params.sync_status)
    
    if params.category:
        # Filter by category (assuming categories are stored as JSON)
        db_query = db_query.filter(
            Product.categories.op('?')(params.category)
        )
    
    return db_query

# Helper function to apply sorting
def apply_sorting(db_query, sort_by: str, sort_order: str):
    """Apply sorting to the query"""
    
    sort_column = getattr(Product, sort_by, Product.created_at)
    
    if sort_order == "asc":
        return db_query.order_by(asc(sort_column))
    else:
        return db_query.order_by(desc(sort_column))

@router.get("/products", response_model=ProductListResponse)
async def get_products(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in name, SKU, or description"),
    category: Optional[str] = Query(None, description="Filter by category"),
    is_visible: Optional[bool] = Query(None, description="Filter by visibility"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    sync_status: Optional[str] = Query(None, description="Filter by sync status"),
    channel: Optional[str] = Query(None, description="Filter by channel"),
    sort_by: str = Query("created_at", regex="^(name|price|created_at|updated_at)$", description="Sort field"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    db: Session = Depends(get_db)
):
    """
    Get all products with filtering, pagination, and sorting
    
    Supports the following operations:
    - Pagination with page and limit
    - Search by name, SKU, or description
    - Filter by visibility, featured status, sync status
    - Sort by name, price, created_at, updated_at
    """
    
    # Create query parameters object
    params = ProductQueryParams(
        page=page,
        limit=limit,
        search=search,
        category=category,
        is_visible=is_visible,
        is_featured=is_featured,
        sync_status=sync_status,
        channel=channel,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    # Build base query
    db_query = db.query(Product)
    
    # Apply filters
    db_query = build_product_filters(db_query, params)
    
    # Get total count before pagination
    total = db_query.count()
    
    # Apply sorting
    db_query = apply_sorting(db_query, sort_by, sort_order)
    
    # Apply pagination
    offset = (page - 1) * limit
    products = db_query.offset(offset).limit(limit).all()
    
    # Calculate total pages
    total_pages = math.ceil(total / limit) if total > 0 else 1
    
    # Convert to list items
    product_items = [
        ProductListItem(
            id=product.id,
            name=product.name,
            sku=product.sku,
            price=product.price,
            inventory_level=product.inventory_level,
            is_visible=product.is_visible,
            is_featured=product.is_featured,
            sync_status=product.sync_status,
            created_at=product.created_at,
            updated_at=product.updated_at
        )
        for product in products
    ]
    
    return ProductListResponse(
        products=product_items,
        total=total,
        page=page,
        limit=limit,
        total_pages=total_pages
    )

@router.get("/products/{product_id}", response_model=ProductResponse)
async def get_product(product_id: str, db: Session = Depends(get_db)):
    """
    Get a specific product by ID
    
    Returns detailed product information including all fields
    """
    
    product = db.query(Product).filter(Product.id == product_id).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    
    # Increment view count
    product.view_count += 1
    db.commit()
    
    return ProductResponse.from_orm(product)

@router.post("/products", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product(product_data: ProductCreate, db: Session = Depends(get_db)):
    """
    Create a new product
    
    Creates a new product with all the provided information
    """
    
    # Check if SKU already exists (if provided)
    if product_data.sku:
        existing_product = db.query(Product).filter(Product.sku == product_data.sku).first()
        if existing_product:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Product with SKU '{product_data.sku}' already exists"
            )
    
    # Generate unique product ID
    product_id = generate_product_id()
    
    # Create product instance
    db_product = Product(
        id=product_id,
        **product_data.dict()
    )
    
    # Add to database
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    
    return ProductResponse.from_orm(db_product)

@router.put("/products/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: str, 
    product_data: ProductUpdate, 
    db: Session = Depends(get_db)
):
    """
    Update an existing product
    
    Updates product information with provided fields
    """
    
    # Get existing product
    product = db.query(Product).filter(Product.id == product_id).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    
    # Check SKU uniqueness if being updated
    if product_data.sku and product_data.sku != product.sku:
        existing_product = db.query(Product).filter(
            and_(Product.sku == product_data.sku, Product.id != product_id)
        ).first()
        if existing_product:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Product with SKU '{product_data.sku}' already exists"
            )
    
    # Update fields
    update_data = product_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(product, field, value)
    
    # Update timestamp
    product.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(product)
    
    return ProductResponse.from_orm(product)

@router.delete("/products/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product(product_id: str, db: Session = Depends(get_db)):
    """
    Delete a product
    
    Permanently removes a product from the database
    """
    
    product = db.query(Product).filter(Product.id == product_id).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    
    db.delete(product)
    db.commit()
    
    return None

@router.post("/products/{product_id}/sync")
async def sync_product(product_id: str, db: Session = Depends(get_db)):
    """
    Sync product with external platforms
    
    Simulates syncing product data with external e-commerce platforms
    """
    
    product = db.query(Product).filter(Product.id == product_id).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with id {product_id} not found"
        )
    
    # Simulate sync process
    # In real implementation, this would sync with BigCommerce, Shopify, etc.
    product.sync_status = "synced"
    product.updated_at = datetime.utcnow()
    
    db.commit()
    
    return {
        "success": True,
        "message": f"Product {product_id} synced successfully",
        "sync_status": "synced",
        "timestamp": datetime.utcnow().isoformat()
    }

import React, { useState } from 'react'
import {
  Calculator,
  Search,
  Filter,
  Download,
  Upload,
  Plus,
  Eye,
  Edit,
  Trash2,
  Check,
  X,
  Clock,
  AlertCircle,
  Building2,
  Package,
  DollarSign,
  Star,
  MoreHorizontal,
  RefreshCw,
  Settings,
  Percent,
  TrendingUp,
  BarChart3,
  Tag,
  Layers
} from 'lucide-react'
import CustomSelect from '../components/CustomSelect'
import StatCard from '../components/StatCard'
import MultiSelectDropdown from '../components/MultiSelectDropdown'
import SectionHeader from '../components/SectionHeader'

const MarkupPage = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedMarkup, setSelectedMarkup] = useState<any>(null)

  // Options for multi-select dropdowns
  const typeOptions = [
    { value: 'supplier', label: 'Supplier' },
    { value: 'classification', label: 'Classification' },
    { value: 'classified_as', label: 'Classified As' }
  ]

  const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'pending', label: 'Pending' }
  ]

  // Mock data for markup rules
  const mockMarkupRules = [
    {
      id: '1',
      name: 'Electronics Supplier Markup',
      type: 'supplier',
      criteria: 'TechCorp Solutions',
      markup_type: 'percentage',
      markup_value: 45,
      status: 'active',
      products_affected: 156,
      created_date: '2024-01-15T10:30:00Z',
      last_updated: '2024-01-20T14:22:00Z',
      priority: 1
    },
    {
      id: '2',
      name: 'Premium Classification Markup',
      type: 'classification',
      criteria: 'Premium Electronics',
      markup_type: 'percentage',
      markup_value: 60,
      status: 'active',
      products_affected: 89,
      created_date: '2024-01-10T09:15:00Z',
      last_updated: '2024-01-18T11:30:00Z',
      priority: 2
    },
    {
      id: '3',
      name: 'Clothing Fixed Markup',
      type: 'classified_as',
      criteria: 'Apparel',
      markup_type: 'fixed',
      markup_value: 25.00,
      status: 'active',
      products_affected: 234,
      created_date: '2024-01-08T16:45:00Z',
      last_updated: '2024-01-19T13:10:00Z',
      priority: 3
    },
    {
      id: '4',
      name: 'Health Supplier Premium',
      type: 'supplier',
      criteria: 'HealthTech Inc',
      markup_type: 'percentage',
      markup_value: 55,
      status: 'inactive',
      products_affected: 67,
      created_date: '2024-01-05T12:20:00Z',
      last_updated: '2024-01-15T10:05:00Z',
      priority: 4
    }
  ]

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: Check, text: 'Active' },
      inactive: { color: 'bg-gray-100 text-gray-800', icon: X, text: 'Inactive' },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pending' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig]
    const Icon = config.icon
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      supplier: { color: 'bg-blue-100 text-blue-800', icon: Building2, text: 'Supplier' },
      classification: { color: 'bg-purple-100 text-purple-800', icon: Tag, text: 'Classification' },
      classified_as: { color: 'bg-orange-100 text-orange-800', icon: Layers, text: 'Classified As' }
    }
    
    const config = typeConfig[type as keyof typeof typeConfig]
    const Icon = config.icon
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const getMarkupDisplay = (markupType: string, value: number) => {
    if (markupType === 'percentage') {
      return (
        <div className="flex items-center">
          <Percent className="h-4 w-4 text-green-500 mr-1" />
          <span className="font-medium text-green-600">{value}%</span>
        </div>
      )
    } else {
      return (
        <div className="flex items-center">
          <DollarSign className="h-4 w-4 text-blue-500 mr-1" />
          <span className="font-medium text-blue-600">${value.toFixed(2)}</span>
        </div>
      )
    }
  }

  const handleEditMarkup = (markup: any) => {
    setSelectedMarkup(markup)
    setShowEditModal(true)
  }

  const handleDeleteMarkup = (markupId: string) => {
    console.log('Deleting markup rule:', markupId)
    // Here you would implement the actual delete logic
  }

  const filteredMarkupRules = mockMarkupRules.filter(rule => {
    const matchesSearch = rule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rule.criteria.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedTypes.length === 0 || selectedTypes.includes(rule.type)
    const matchesStatus = selectedStatuses.length === 0 || selectedStatuses.includes(rule.status)

    return matchesSearch && matchesType && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<Calculator className="h-7 w-7" />}
        title="Markup Rules"
        subtitle="Set pricing markup rules based on suppliers, classifications, and categories"
        actions={
          <>
            <button className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
              <RefreshCw className="h-4 w-4 mr-2" />
              Apply All Rules
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Rule
            </button>
          </>
        }
      />

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="Total Rules"
          value={mockMarkupRules.length}
          subtitle="Markup rules created"
          icon={Calculator}
          iconColor="text-purple-600"
          trend={{
            value: "2",
            isPositive: true,
            period: "last month"
          }}
        />
        <StatCard
          title="Active Rules"
          value={mockMarkupRules.filter(r => r.status === 'active').length}
          subtitle="Currently applied"
          icon={Check}
          iconColor="text-green-600"
          trend={{
            value: "100%",
            isPositive: true,
            period: "success rate"
          }}
        />
        <StatCard
          title="Products Affected"
          value={mockMarkupRules.reduce((sum, r) => sum + r.products_affected, 0)}
          subtitle="Total products"
          icon={Package}
          iconColor="text-blue-600"
          trend={{
            value: "156",
            isPositive: true,
            period: "last update"
          }}
        />
        <StatCard
          title="Avg Markup"
          value={`${Math.round(mockMarkupRules.reduce((sum, r) => sum + (r.markup_type === 'percentage' ? r.markup_value : 0), 0) / mockMarkupRules.filter(r => r.markup_type === 'percentage').length)}%`}
          subtitle="Average percentage"
          icon={Percent}
          iconColor="text-orange-600"
          trend={{
            value: "5.2%",
            isPositive: true,
            period: "vs. industry"
          }}
        />
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search markup rules..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <MultiSelectDropdown
          options={typeOptions}
          value={selectedTypes}
          onChange={setSelectedTypes}
          placeholder="All Types"
          searchPlaceholder="Search types..."
          className="min-w-[200px]"
        />
        <MultiSelectDropdown
          options={statusOptions}
          value={selectedStatuses}
          onChange={setSelectedStatuses}
          placeholder="All Status"
          searchPlaceholder="Search status..."
          className="min-w-[200px]"
        />
        <div className="flex space-x-2">
          <button className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </button>
        </div>
      </div>

      {/* Markup Rules Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rule Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type & Criteria</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Markup</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Products Affected</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMarkupRules.map((rule) => (
                <tr key={rule.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{rule.name}</div>
                      <div className="text-sm text-gray-500">
                        Created: {new Date(rule.created_date).toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      {getTypeBadge(rule.type)}
                      <div className="text-sm text-gray-900 font-medium">{rule.criteria}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getMarkupDisplay(rule.markup_type, rule.markup_value)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Package className="h-4 w-4 text-blue-500 mr-2" />
                      <span className="text-sm font-medium text-gray-900">{rule.products_affected}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(rule.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      #{rule.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="text-gray-400 hover:text-gray-600">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEditMarkup(rule)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <Settings className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteMarkup(rule.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Markup Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Create Markup Rule</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rule Name</label>
                  <input
                    type="text"
                    placeholder="e.g., Electronics Supplier Markup"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rule Type</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select Type</option>
                    <option value="supplier">Supplier</option>
                    <option value="classification">Classification</option>
                    <option value="classified_as">Classified As</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Criteria</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                  <option value="">Select Criteria</option>
                  <option value="techcorp">TechCorp Solutions</option>
                  <option value="healthtech">HealthTech Inc</option>
                  <option value="ecofashion">EcoFashion Co</option>
                  <option value="photogear">PhotoGear Ltd</option>
                </select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Markup Type</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="percentage">Percentage (%)</option>
                    <option value="fixed">Fixed Amount ($)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Markup Value</label>
                  <input
                    type="number"
                    placeholder="45"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                  <input
                    type="number"
                    placeholder="1"
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Lower numbers have higher priority</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create Rule
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default MarkupPage

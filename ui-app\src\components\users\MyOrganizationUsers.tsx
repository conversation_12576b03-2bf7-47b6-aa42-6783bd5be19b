import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, MoreVertical, Mail, UserPlus, X } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useCustomFields } from '../../hooks/useCustomFields'

import PermissionGate from '../PermissionGate'
import InviteUserModal from '../InviteUserModal'

import CustomSelect from '../CustomSelect'
import { RESOURCES, ACTIONS } from '../../types/auth'
import toast from 'react-hot-toast'

const MyOrganizationUsers = () => {
  const { } = useAuth()
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [selectedDepartment, setSelectedDepartment] = useState('')
  const [selectedRole, setSelectedRole] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')

  // Fetch global custom fields
  const { fields: globalFields, loading: globalFieldsLoading, addField, refreshFields } = useCustomFields({ resourceType: 'global' })

  // Helper to ensure a global field exists, create if missing
  const ensureField = async (key: string, label: string, options: { value: string, label: string }[]) => {
    let field = globalFields.find(f => f.key.toLowerCase() === key.toLowerCase() || f.label.toLowerCase() === label.toLowerCase())
    if (!field) {
      await addField({
        label,
        key,
        field_type: 'single_select',
        resource_type: 'global',
        namespace: 'global',
        description: `${label} global field`,
        is_required: false,
        is_searchable: false,
        is_filterable: false,
        is_ai_enabled: false,
        is_active: true,
        field_options: { options },
        validation_rules: {}
      })
      await refreshFields()
      field = globalFields.find(f => f.key.toLowerCase() === key.toLowerCase() || f.label.toLowerCase() === label.toLowerCase())
    }
    return field
  }

  useEffect(() => {
    // Ensure Department, Role, and Status fields exist
    ensureField('department', 'Department', [
      { value: 'executive', label: 'Executive' },
      { value: 'sales', label: 'Sales' },
      { value: 'marketing', label: 'Marketing' },
      { value: 'customer-support', label: 'Customer Support' },
      { value: 'warehouse', label: 'Warehouse' },
      { value: 'finance', label: 'Finance' }
    ])
    ensureField('role', 'Role', [
      { value: 'admin', label: 'Admin' },
      { value: 'manager', label: 'Manager' },
      { value: 'editor', label: 'Editor' },
      { value: 'viewer', label: 'Viewer' },
      { value: 'sales-rep', label: 'Sales Representative' },
      { value: 'inventory-manager', label: 'Inventory Manager' }
    ])
    ensureField('status', 'Status', [
      { value: 'active', label: 'Active' },
      { value: 'away', label: 'Away' },
      { value: 'inactive', label: 'Inactive' },
      { value: 'pending', label: 'Pending' },
      { value: 'expired', label: 'Expired' }
    ])
  }, [])

  // Find the fields
  const departmentField = globalFields.find(f => f.key.toLowerCase() === 'department' || f.label.toLowerCase() === 'department')
  const roleField = globalFields.find(f => f.key.toLowerCase() === 'role' || f.label.toLowerCase() === 'role')
  const statusField = globalFields.find(f => f.key.toLowerCase() === 'status' || f.label.toLowerCase() === 'status')

  // Build options from global fields
  const departmentOptions = [
    { value: '', label: 'All Departments' },
    ...((departmentField?.field_options?.options) || [])
  ]
  const roleOptions = [
    { value: '', label: 'All Roles' },
    ...((roleField?.field_options?.options) || [])
  ]
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    ...((statusField?.field_options?.options) || [])
  ]

  const handleInviteUser = () => {
    setShowInviteModal(true)
  }

  const handleUserClick = (userId: string) => {
    navigate(`/settings/users/my-organization/${userId}`)
  }

  return (
    <div>
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          Manage and invite users to your organization
        </p>
      </div>

          {/* Search and Filters */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 w-64"
                />
              </div>

              {/* Filters */}
              <div className="w-48">
                <CustomSelect
                  options={departmentOptions}
                  value={selectedDepartment}
                  onChange={setSelectedDepartment}
                />
              </div>

              <div className="w-48">
                <CustomSelect
                  options={roleOptions}
                  value={selectedRole}
                  onChange={setSelectedRole}
                />
              </div>

              <div className="w-48">
                <CustomSelect
                  options={statusOptions}
                  value={selectedStatus}
                  onChange={setSelectedStatus}
                />
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <PermissionGate resource={RESOURCES.USERS} action={ACTIONS.INVITE}>
                <button
                  onClick={handleInviteUser}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-900 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite User
                </button>
              </PermissionGate>
            </div>
          </div>

          {/* Users Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{/* Actions */}</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {/* Sample users matching the design */}
                  <tr
                    className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleUserClick('1')}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">JS</span>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">John Smith</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <EMAIL>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      Admin
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      Executive
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <div className="h-2 w-2 bg-gray-400 rounded-full mr-2"></div>
                        May 20, 2025, 10:30 AM
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-gray-400 hover:text-gray-600"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>

                  <tr
                    className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleUserClick('2')}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">SJ</span>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">Sarah Johnson</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <EMAIL>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      Manager
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      Sales
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <div className="h-2 w-2 bg-gray-400 rounded-full mr-2"></div>
                        May 19, 2025, 04:45 PM
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-gray-400 hover:text-gray-600"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>

                  <tr
                    className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleUserClick('3')}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">MB</span>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">Michael Brown</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <EMAIL>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      Editor
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      Marketing
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Away
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <div className="h-2 w-2 bg-gray-400 rounded-full mr-2"></div>
                        May 10, 2025, 06:15 AM
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-gray-400 hover:text-gray-600"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

      {/* Invite User Modal */}
      {showInviteModal && (
        <InviteUserModal
          onClose={() => setShowInviteModal(false)}
          onInvite={() => {
            toast.success('User invited successfully!')
            setShowInviteModal(false)
          }}
        />
      )}
    </div>
  )
}

export default MyOrganizationUsers

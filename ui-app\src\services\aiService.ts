import axios from 'axios';

// AI Service Configuration
const AI_CONFIG = {
  OPENAI_API_KEY: import.meta.env.VITE_OPENAI_API_KEY,
  OPENAI_API_URL: 'https://api.openai.com/v1/chat/completions',
  ANTHROPIC_API_KEY: import.meta.env.VITE_ANTHROPIC_API_KEY,
  ANTHROPIC_API_URL: 'https://api.anthropic.com/v1/messages',
  GOOGLE_API_KEY: import.meta.env.VITE_GOOGLE_API_KEY,
  GOOGLE_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
};

// Debug logging for API key (remove in production)
console.log('OpenAI API Key configured:', !!AI_CONFIG.OPENAI_API_KEY);
console.log('OpenAI API Key length:', AI_CONFIG.OPENAI_API_KEY?.length || 0);
console.log('OpenAI API Key starts with:', AI_CONFIG.OPENAI_API_KEY?.substring(0, 10) || 'none');

export interface AIRequest {
  message: string;
  conversationHistory: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
  pageContext: string;
  systemPrompt?: string;
}

export interface AIResponse {
  content: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// OpenAI GPT Integration
export const openAIService = {
  async generateResponse(request: AIRequest): Promise<AIResponse> {
    if (!AI_CONFIG.OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    const systemPrompt = request.systemPrompt || `You are Nuclues Assistant, an AI assistant for the Nuclues platform. 
    
The user is currently on: ${request.pageContext}

You help users with:
- Product management (create, edit, delete products)
- Order tracking and management  
- Customer information and management
- Platform settings and configuration
- Integration setup and management
- General navigation and feature explanations

Provide helpful, accurate, and context-aware responses. Be concise but thorough.`;

    const messages = [
      { role: 'system', content: systemPrompt },
      ...request.conversationHistory.slice(-10), // Last 10 messages for context
      { role: 'user', content: request.message }
    ];

    try {
      const response = await axios.post(
        AI_CONFIG.OPENAI_API_URL,
        {
          model: 'gpt-4',
          messages,
          max_tokens: 1000,
          temperature: 0.7,
        },
        {
          headers: {
            'Authorization': `Bearer ${AI_CONFIG.OPENAI_API_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return {
        content: response.data.choices[0].message.content,
        model: 'gpt-4',
        usage: response.data.usage,
      };
    } catch (error: any) {
      console.error('OpenAI API Error:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw new Error(`Failed to generate AI response: ${error.message}`);
    }
  },
};

// Anthropic Claude Integration
export const anthropicService = {
  async generateResponse(request: AIRequest): Promise<AIResponse> {
    if (!AI_CONFIG.ANTHROPIC_API_KEY) {
      throw new Error('Anthropic API key not configured');
    }

    const systemPrompt = request.systemPrompt || `You are Nuclues Assistant, an AI assistant for the Nuclues platform. 
    
The user is currently on: ${request.pageContext}

You help users with:
- Product management (create, edit, delete products)
- Order tracking and management  
- Customer information and management
- Platform settings and configuration
- Integration setup and management
- General navigation and feature explanations

Provide helpful, accurate, and context-aware responses. Be concise but thorough.`;

    const messages = [
      ...request.conversationHistory.slice(-10), // Last 10 messages for context
      { role: 'user', content: request.message }
    ];

    try {
      const response = await axios.post(
        AI_CONFIG.ANTHROPIC_API_URL,
        {
          model: 'claude-3-sonnet-20240229',
          max_tokens: 1000,
          messages,
          system: systemPrompt,
        },
        {
          headers: {
            'x-api-key': AI_CONFIG.ANTHROPIC_API_KEY,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01',
          },
        }
      );

      return {
        content: response.data.content[0].text,
        model: 'claude-3-sonnet',
      };
    } catch (error) {
      console.error('Anthropic API Error:', error);
      throw new Error('Failed to generate AI response');
    }
  },
};

// Google Gemini Integration
export const googleAIService = {
  async generateResponse(request: AIRequest): Promise<AIResponse> {
    if (!AI_CONFIG.GOOGLE_API_KEY) {
      throw new Error('Google AI API key not configured');
    }

    const systemPrompt = request.systemPrompt || `You are Nuclues Assistant, an AI assistant for the Nuclues platform. 
    
The user is currently on: ${request.pageContext}

You help users with:
- Product management (create, edit, delete products)
- Order tracking and management  
- Customer information and management
- Platform settings and configuration
- Integration setup and management
- General navigation and feature explanations

Provide helpful, accurate, and context-aware responses. Be concise but thorough.`;

    const conversationHistory = request.conversationHistory
      .slice(-10)
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    const prompt = `${systemPrompt}\n\nConversation History:\n${conversationHistory}\n\nUser: ${request.message}\n\nAssistant:`;

    try {
      const response = await axios.post(
        `${AI_CONFIG.GOOGLE_API_URL}?key=${AI_CONFIG.GOOGLE_API_KEY}`,
        {
          contents: [
            {
              parts: [
                {
                  text: prompt,
                },
              ],
            },
          ],
          generationConfig: {
            maxOutputTokens: 1000,
            temperature: 0.7,
          },
        }
      );

      return {
        content: response.data.candidates[0].content.parts[0].text,
        model: 'gemini-pro',
      };
    } catch (error) {
      console.error('Google AI API Error:', error);
      throw new Error('Failed to generate AI response');
    }
  },
};

// AI Service Factory
export const aiServiceFactory = {
  getService(type: 'openai' | 'anthropic' | 'google' = 'openai') {
    switch (type) {
      case 'openai':
        return openAIService;
      case 'anthropic':
        return anthropicService;
      case 'google':
        return googleAIService;
      default:
        return openAIService;
    }
  },
};

export default aiServiceFactory; 
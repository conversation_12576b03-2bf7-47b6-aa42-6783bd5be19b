import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useLocation } from 'react-router-dom';
import chatApiService from '../services/chatApi';
import aiServiceFactory from '../services/aiService';

// Mock response generator for testing
const generateMockResponse = (message: string, pageContext: string, conversationHistory: ChatMessage[] = []): { content: string; structuredData?: StructuredData } => {
  const messageLower = message.toLowerCase();
  
  // Check conversation context for follow-up questions
  const lastMessage = conversationHistory[conversationHistory.length - 1];
  const hasDiscussedCustomer = conversationHistory.some(msg => 
    msg.content.toLowerCase().includes('customer') || 
    (msg.structuredData && msg.structuredData.type === 'customer')
  );
  const hasDiscussedProduct = conversationHistory.some(msg => 
    msg.content.toLowerCase().includes('product') || 
    (msg.structuredData && msg.structuredData.type === 'product')
  );
  const hasDiscussedOrder = conversationHistory.some(msg => 
    msg.content.toLowerCase().includes('order') || 
    (msg.structuredData && msg.structuredData.type === 'order')
  );
  
  // Check for specific product-related keywords in conversation history
  const hasDiscussedPricing = conversationHistory.some(msg => 
    msg.content.toLowerCase().includes('price') || 
    msg.content.toLowerCase().includes('cheap') || 
    msg.content.toLowerCase().includes('expensive') ||
    msg.content.toLowerCase().includes('cost')
  );
  
  // Handle follow-up questions about navigation
  if (messageLower.includes('where') && messageLower.includes('page') && messageLower.includes('data')) {
    if (hasDiscussedCustomer) {
      return {
        content: "You can view Sarah Johnson's complete customer profile and all customer data in the **Customers section**. Navigate to the sidebar menu and click on 'Customers' to see the full customer list, or you can directly access her profile at `/customers/sarah-johnson`. Would you like me to help you navigate there?",
        structuredData: {
          type: 'customer',
          title: 'Navigate to Customer Data',
          subtitle: 'Access Sarah Johnson\'s complete profile',
          data: {
            customerId: 'sarah-johnson',
            name: 'Sarah Johnson',
            company: 'TechCorp Solutions'
          },
          actions: [
            { label: 'View Customer Profile', action: 'navigate:/customers/sarah-johnson', icon: 'User' },
            { label: 'View All Customers', action: 'navigate:/customers', icon: 'Users' }
          ]
        }
      };
    } else if (hasDiscussedProduct) {
      return {
        content: "You can view the product data and manage your inventory in the **Products section**. Navigate to the sidebar menu and click on 'Products' to see all your products, or you can directly access the product details at `/products/wireless-headphones-pro`. Would you like me to help you navigate there?",
        structuredData: {
          type: 'product',
          title: 'Navigate to Product Data',
          subtitle: 'Access product management',
          data: {
            productId: 'wireless-headphones-pro',
            name: 'Wireless Bluetooth Headphones Pro'
          },
          actions: [
            { label: 'View Product Details', action: 'navigate:/products/wireless-headphones-pro', icon: 'Package' },
            { label: 'View All Products', action: 'navigate:/products', icon: 'Package' }
          ]
        }
      };
    } else if (hasDiscussedOrder) {
      return {
        content: "You can view the order details and manage all orders in the **Orders section**. Navigate to the sidebar menu and click on 'Orders' to see all your orders, or you can directly access the specific order at `/orders/ORD-2024-0015`. Would you like me to help you navigate there?",
        structuredData: {
          type: 'order',
          title: 'Navigate to Order Data',
          subtitle: 'Access order management',
          data: {
            orderId: 'ORD-2024-0015',
            customer: 'Michael Chen'
          },
          actions: [
            { label: 'View Order Details', action: 'navigate:/orders/ORD-2024-0015', icon: 'ShoppingCart' },
            { label: 'View All Orders', action: 'navigate:/orders', icon: 'ShoppingCart' }
          ]
        }
      };
    }
  }

  // Handle product comparison questions
  if (messageLower.includes('compare') && (messageLower.includes('product') || hasDiscussedProduct)) {
    return {
      content: "Here's a quick comparison of your key products:\n\n🏆 **Best Seller**: Wireless Bluetooth Headphones Pro - $46,800 revenue\n💰 **Most Expensive**: Smart Fitness Watch Series 5 - $299.99\n👕 **Cheapest**: Organic Cotton T-Shirt - $19.99\n\nWant me to show you detailed comparisons or help you analyze pricing strategies?",
      structuredData: {
        type: 'list',
        title: 'Product Comparison',
        subtitle: 'Key product metrics',
        data: [
          { name: 'Wireless Bluetooth Headphones Pro', price: 200, revenue: 46800, category: 'Electronics' },
          { name: 'Smart Fitness Watch Series 5', price: 299.99, revenue: 56700, category: 'Electronics' },
          { name: 'Organic Cotton Premium T-Shirt', price: 19.99, revenue: 1340, category: 'Clothing' }
        ],
        actions: [
          { label: 'View All Products', action: 'navigate:/products', icon: 'Package' }
        ]
      }
    };
  }
  
  // Handle specific customer questions
  if (messageLower.includes('top customer') || messageLower.includes('best customer') || messageLower.includes('who is my top customer')) {
    return {
      content: "Great question! 🏆 Your top customer is **Sarah Johnson** from TechCorp Solutions. She's been absolutely crushing it:\n\n• **47 orders** with an average value of $1,904\n• **$89,450 in total revenue** - that's impressive!\n• **Last order was just 3 days ago** - she's very active\n• **Been a customer since March 2023** - great retention\n\nSarah mainly buys electronics and office supplies. Want me to show you her complete profile or help you find similar high-value customers?",
      structuredData: {
        type: 'customer',
        title: 'Top Customer Profile',
        subtitle: 'Sarah Johnson - TechCorp Solutions',
        data: {
          name: 'Sarah Johnson',
          company: 'TechCorp Solutions',
          totalOrders: 47,
          totalRevenue: 89450,
          averageOrderValue: 1904,
          lastOrder: '3 days ago',
          customerSince: 'March 2023',
          primaryPurchases: 'Electronics and office supplies'
        },
        actions: [
          { label: 'View Customer Profile', action: 'navigate:/customers/sarah-johnson', icon: 'User' },
          { label: 'View All Customers', action: 'navigate:/customers', icon: 'Users' }
        ]
      }
    };
  }
  
  if (messageLower.includes('customer') && (messageLower.includes('list') || messageLower.includes('all') || messageLower.includes('show'))) {
    return {
      content: "Here are your top 5 customers by revenue:\n\n1. **Sarah Johnson** (TechCorp) - $89,450\n2. **Michael Chen** (InnovateLabs) - $67,200\n3. **Emily Rodriguez** (GlobalTech) - $54,800\n4. **David Kim** (StartupXYZ) - $42,300\n5. **Lisa Thompson** (EnterpriseCorp) - $38,900\n\nYou can view detailed customer information in the Customers section of your platform.",
      structuredData: {
        type: 'list',
        title: 'Top Customers by Revenue',
        subtitle: 'Your most valuable customers',
        data: [
          { id: '1', name: 'Sarah Johnson', company: 'TechCorp', revenue: 89450, orders: 47 },
          { id: '2', name: 'Michael Chen', company: 'InnovateLabs', revenue: 67200, orders: 38 },
          { id: '3', name: 'Emily Rodriguez', company: 'GlobalTech', revenue: 54800, orders: 29 },
          { id: '4', name: 'David Kim', company: 'StartupXYZ', revenue: 42300, orders: 22 },
          { id: '5', name: 'Lisa Thompson', company: 'EnterpriseCorp', revenue: 38900, orders: 19 }
        ],
        actions: [
          { label: 'View All Customers', action: 'navigate:/customers', icon: 'Users' }
        ]
      }
    };
  }
  
  // Handle order questions
  if (messageLower.includes('top order') || messageLower.includes('largest order') || messageLower.includes('biggest order')) {
    return {
      content: "Your biggest order was a monster! 💰 **Michael Chen** from InnovateLabs dropped **$12,450** on January 15th:\n\n• **Order #ORD-2024-0015** - definitely one to remember\n• **15 wireless headphones, 8 smart watches, 12 charging pads** - they went all out!\n• **Status: Delivered** - everything went smoothly\n\nThis was a huge boost to your monthly revenue. Want me to show you the full order details or help you find more high-value customers like Michael?",
      structuredData: {
        type: 'order',
        title: 'Largest Order',
        subtitle: 'Michael Chen - InnovateLabs',
        data: {
          id: 'ORD-2024-0015',
          customer: 'Michael Chen',
          company: 'InnovateLabs',
          amount: 12450,
          items: '15 wireless headphones, 8 smart watches, 12 charging pads',
          date: 'January 15th, 2024',
          status: 'Delivered'
        },
        actions: [
          { label: 'View Order Details', action: 'navigate:/orders/ORD-2024-0015', icon: 'ShoppingCart' },
          { label: 'View All Orders', action: 'navigate:/orders', icon: 'ShoppingCart' }
        ]
      }
    };
  }
  
  if (messageLower.includes('recent order') || messageLower.includes('latest order')) {
    return {
      content: "Your latest order just came in! 📦 **Emily Rodriguez** from GlobalTech just placed an order for **$2,340**:\n\n• **Order #ORD-2024-0023** - fresh off the press\n• **3 smart watches, 2 wireless headphones** - nice selection!\n• **Status: Processing** - we're on it\n• **Placed today** - very recent\n\nWant me to show you the full details or help you track more recent activity?",
      structuredData: {
        type: 'order',
        title: 'Most Recent Order',
        subtitle: 'Emily Rodriguez - GlobalTech',
        data: {
          id: 'ORD-2024-0023',
          customer: 'Emily Rodriguez',
          company: 'GlobalTech',
          amount: 2340,
          items: '3 smart watches, 2 wireless headphones',
          date: 'Today',
          status: 'Processing'
        },
        actions: [
          { label: 'View Order Details', action: 'navigate:/orders/ORD-2024-0023', icon: 'ShoppingCart' },
          { label: 'View All Orders', action: 'navigate:/orders', icon: 'ShoppingCart' }
        ]
      }
    };
  }
  
  // Handle product questions
  if (messageLower.includes('top product') || messageLower.includes('best selling') || messageLower.includes('popular product')) {
    return {
      content: "Your star product is definitely the **Wireless Bluetooth Headphones Pro**! 🎧\n\nThis thing is flying off the shelves:\n• **234 units sold** - that's amazing!\n• **$46,800 in revenue** - your cash cow\n• **Only 45 units left** - might want to restock soon\n• **4.8/5 star rating** - customers love it\n\nIt's in the Electronics category and it's consistently your top performer. Should I show you the details or help you check your inventory levels?",
      structuredData: {
        type: 'product',
        title: 'Best-Selling Product',
        subtitle: 'Wireless Bluetooth Headphones Pro',
        data: {
          name: 'Wireless Bluetooth Headphones Pro',
          category: 'Electronics',
          unitsSold: 234,
          revenue: 46800,
          inventory: 45,
          rating: '4.8/5 stars'
        },
        actions: [
          { label: 'View Product Details', action: 'navigate:/products/wireless-headphones-pro', icon: 'Package' },
          { label: 'View All Products', action: 'navigate:/products', icon: 'Package' }
        ]
      }
    };
  }

  // Handle cheapest product questions
  if (messageLower.includes('cheapest') || messageLower.includes('lowest price') || messageLower.includes('least expensive')) {
    return {
      content: "Your cheapest product is the **Organic Cotton Premium T-Shirt**! 👕\n\n• **Price: $19.99** - very affordable\n• **Category: Clothing** - casual wear\n• **67 units sold** - steady sales\n• **4.2/5 star rating** - good reviews\n• **Inventory: 23 units** - running low\n\nIt's a great entry-level product that brings in customers. Want me to show you more budget-friendly options or help you check the pricing strategy?",
      structuredData: {
        type: 'product',
        title: 'Cheapest Product',
        subtitle: 'Organic Cotton Premium T-Shirt',
        data: {
          name: 'Organic Cotton Premium T-Shirt',
          category: 'Clothing',
          price: 19.99,
          unitsSold: 67,
          revenue: 1340,
          inventory: 23,
          rating: '4.2/5 stars'
        },
        actions: [
          { label: 'View Product Details', action: 'navigate:/products/organic-cotton-tshirt', icon: 'Package' },
          { label: 'View All Products', action: 'navigate:/products', icon: 'Package' }
        ]
      }
    };
  }

  // Handle expensive product questions
  if (messageLower.includes('most expensive') || messageLower.includes('highest price') || messageLower.includes('costliest')) {
    return {
      content: "Your most expensive product is the **Smart Fitness Watch Series 5**! ⌚\n\n• **Price: $299.99** - premium pricing\n• **Category: Electronics** - high-end tech\n• **189 units sold** - impressive for the price\n• **4.7/5 star rating** - customers love it\n• **Revenue: $56,700** - your highest earner\n\nIt's a premium product that brings in serious revenue. Want me to show you the details or help you analyze the pricing strategy?",
      structuredData: {
        type: 'product',
        title: 'Most Expensive Product',
        subtitle: 'Smart Fitness Watch Series 5',
        data: {
          name: 'Smart Fitness Watch Series 5',
          category: 'Electronics',
          price: 299.99,
          unitsSold: 189,
          revenue: 56700,
          inventory: 12,
          rating: '4.7/5 stars'
        },
        actions: [
          { label: 'View Product Details', action: 'navigate:/products/smart-fitness-watch', icon: 'Package' },
          { label: 'View All Products', action: 'navigate:/products', icon: 'Package' }
        ]
      }
    };
  }
  
  if (messageLower.includes('product') && (messageLower.includes('list') || messageLower.includes('all') || messageLower.includes('show'))) {
    return {
      content: "Here are your top 5 products by sales:\n\n1. **Wireless Bluetooth Headphones Pro** - 234 units sold\n2. **Smart Fitness Watch Series 5** - 189 units sold\n3. **Stainless Steel Water Bottle 32oz** - 156 units sold\n4. **Wireless Charging Pad Fast Charge** - 78 units sold\n5. **Organic Cotton Premium T-Shirt** - 67 units sold\n\nYou can manage all products in the Products section.",
      structuredData: {
        type: 'list',
        title: 'Top Products by Sales',
        subtitle: 'Your best-performing products',
        data: [
          { id: '1', name: 'Wireless Bluetooth Headphones Pro', category: 'Electronics', unitsSold: 234, revenue: 46800 },
          { id: '2', name: 'Smart Fitness Watch Series 5', category: 'Electronics', unitsSold: 189, revenue: 56700 },
          { id: '3', name: 'Stainless Steel Water Bottle 32oz', category: 'Home & Garden', unitsSold: 156, revenue: 3900 },
          { id: '4', name: 'Wireless Charging Pad Fast Charge', category: 'Electronics', unitsSold: 78, revenue: 3900 },
          { id: '5', name: 'Organic Cotton Premium T-Shirt', category: 'Clothing', unitsSold: 67, revenue: 2680 }
        ],
        actions: [
          { label: 'View All Products', action: 'navigate:/products', icon: 'Package' }
        ]
      }
    };
  }

  // Handle general product questions
  if (messageLower.includes('product') && !messageLower.includes('list') && !messageLower.includes('all') && !messageLower.includes('show') && !messageLower.includes('top') && !messageLower.includes('best') && !messageLower.includes('cheapest') && !messageLower.includes('expensive')) {
    return {
      content: "I can help you with all sorts of product questions! 🛍️ Here are some things you can ask me:\n\n• **\"What's my best product?\"** - I'll show you top performers\n• **\"Which is the cheapest product?\"** - I'll find your budget options\n• **\"Show me all products\"** - I'll list everything\n• **\"Compare products\"** - I'll give you a side-by-side view\n\nWhat specific product info are you looking for?",
      structuredData: {
        type: 'list',
        title: 'Product Information Options',
        subtitle: 'What would you like to know?',
        data: [
          { name: 'Best Selling Products', description: 'Top performers by sales' },
          { name: 'Product Pricing', description: 'Cheapest and most expensive' },
          { name: 'Product Categories', description: 'Electronics, Clothing, etc.' },
          { name: 'Inventory Status', description: 'Stock levels and availability' }
        ],
        actions: [
          { label: 'View All Products', action: 'navigate:/products', icon: 'Package' }
        ]
      }
    };
  }
  
  // Handle API/mock related questions
  if (messageLower.includes('mock') || messageLower.includes('api') || messageLower.includes('result')) {
    return {
      content: "I'm currently running on a mock system for demonstration purposes. This allows you to test the chat functionality without needing the full backend infrastructure. The responses are context-aware and intelligent, but they're pre-programmed rather than AI-generated. When the full system is deployed, you'll get real AI responses based on your actual platform data."
    };
  }
  
  // Handle general questions about the system
  if (messageLower.includes('what') || messageLower.includes('how') || messageLower.includes('tell me')) {
    if (messageLower.includes('dashboard') || messageLower.includes('overview')) {
      return {
        content: "The Dashboard provides an overview of your Nuclues platform. Here you can see:\n• Key performance metrics\n• Recent activity\n• Quick access to main features\n• System status and notifications\n\nYou can navigate to specific sections using the sidebar menu to dive deeper into products, orders, customers, and more."
      };
    }
  }
  
  // Context-specific responses
  if (pageContext.includes('Products')) {
    if (messageLower.includes('create') || messageLower.includes('add')) {
      return {
        content: "To create a new product, navigate to the Products section and click 'Create Product'. You can add product details, images, pricing, and inventory information. Would you like me to guide you through the product creation process?"
      };
    } else if (messageLower.includes('edit') || messageLower.includes('modify')) {
      return {
        content: "To edit a product, find it in the products list and click on it to open the details page. From there, you can modify any product information. What specific product would you like to edit?"
      };
    } else if (messageLower.includes('delete') || messageLower.includes('remove')) {
      return {
        content: "To delete a product, go to the product details page and look for the delete option. Please note that this action cannot be undone. Are you sure you want to delete a product?"
      };
    }
  } else if (pageContext.includes('Orders')) {
    if (messageLower.includes('view') || messageLower.includes('see')) {
      return {
        content: "You can view all orders in the Orders section. Orders are organized by platform (Shopify, BigCommerce, Amazon). You can filter and search through orders to find specific ones. What type of orders are you looking for?"
      };
    } else if (messageLower.includes('status') || messageLower.includes('track')) {
      return {
        content: "Order status information is available in the order details. Each order shows its current status, shipping information, and customer details. Which order would you like to check?"
      };
    }
  } else if (pageContext.includes('Customers')) {
    if (messageLower.includes('add') || messageLower.includes('create')) {
      return {
        content: "To add a new customer, go to the Customers section and click 'Add Customer'. You'll need to provide basic information like name, email, and contact details. Would you like me to help you with the customer creation process?"
      };
    } else if (messageLower.includes('find') || messageLower.includes('search')) {
      return {
        content: "You can search for customers using the search bar in the Customers section. You can search by name, email, or customer ID. What information do you have about the customer you're looking for?"
      };
    }
  } else if (pageContext.includes('Settings')) {
    if (messageLower.includes('configure') || messageLower.includes('setup')) {
      return {
        content: "The Settings section allows you to configure various aspects of your Nuclues platform. You can manage users, roles, integrations, and platform preferences. What specific setting would you like to configure?"
      };
    } else if (messageLower.includes('user') || messageLower.includes('permission')) {
      return {
        content: "User management and permissions can be configured in the Settings > Users section. You can add new users, assign roles, and manage permissions. What would you like to do with user management?"
      };
    }
  } else if (pageContext.includes('Integrations')) {
    if (messageLower.includes('connect') || messageLower.includes('setup')) {
      return {
        content: "To connect external platforms, go to the Integrations section. You can connect Shopify, BigCommerce, Amazon, and other platforms. Each integration requires specific configuration. Which platform would you like to connect?"
      };
    } else if (messageLower.includes('sync') || messageLower.includes('import')) {
      return {
        content: "Data synchronization happens automatically once integrations are configured. You can also manually trigger syncs from the integration settings. What type of data would you like to sync?"
      };
    }
  }

  // General responses
  if (messageLower.includes('help') || messageLower.includes('how')) {
    return {
      content: "I'm here to help you navigate and use the Nuclues platform effectively. You can ask me about products, orders, customers, settings, integrations, or any other platform features. What would you like to know more about?"
    };
  } else if (messageLower.includes('what') && messageLower.includes('can')) {
    return {
      content: "I can help you with:\n• Product management (create, edit, delete products)\n• Order tracking and management\n• Customer information and management\n• Platform settings and configuration\n• Integration setup and management\n• General navigation and feature explanations\n\nWhat would you like to do?"
    };
  } else if (messageLower.includes('navigate') || messageLower.includes('go to')) {
    return {
      content: "You can navigate through the platform using the sidebar menu. The main sections are:\n• Dashboard (overview)\n• Products (manage your catalog)\n• Orders (view and manage orders)\n• Customers (manage customer data)\n• Settings (configure your platform)\n• Integrations (connect external platforms)\n\nWhere would you like to go?"
    };
  }

  // Handle greetings and casual conversation
  if (messageLower.includes('hi') || messageLower.includes('hello') || messageLower.includes('hey')) {
    return {
      content: "Hi there! 👋 I'm your Nuclues assistant. I can help you with anything related to your platform - from checking customer data and product performance to navigating different sections. What would you like to know about today?"
    };
  }

  // Handle "how are you" type questions
  if (messageLower.includes('how are you') || messageLower.includes('how do you do')) {
    return {
      content: "I'm doing great, thanks for asking! 😊 I'm here and ready to help you with your Nuclues platform. Whether you need to check sales data, manage products, or explore customer insights, I'm your go-to assistant. What can I help you with?"
    };
  }

  // Handle "what can you do" type questions
  if (messageLower.includes('what can you do') || messageLower.includes('help me') || messageLower.includes('capabilities')) {
    return {
      content: "I'm your AI assistant for the Nuclues platform! Here's what I can help you with:\n\n📊 **Analytics & Insights**: Check your top customers, best-selling products, revenue trends\n📦 **Product Management**: Find products, check inventory, view performance\n👥 **Customer Data**: Look up customer profiles, order history, spending patterns\n📋 **Order Tracking**: Check order status, recent orders, largest transactions\n⚙️ **Navigation**: Help you find specific pages and features\n\nJust ask me anything like \"Who is my top customer?\" or \"Show me my best products\" and I'll get you the info you need!"
    };
  }

  // Handle "thanks" and "thank you"
  if (messageLower.includes('thank') || messageLower.includes('thanks')) {
    return {
      content: "You're very welcome! 😊 I'm here whenever you need help with your Nuclues platform. Feel free to ask me anything else!"
    };
  }

  // Handle "bye" and "goodbye"
  if (messageLower.includes('bye') || messageLower.includes('goodbye') || messageLower.includes('see you')) {
    return {
      content: "Goodbye! 👋 It was great helping you today. Don't hesitate to come back if you need anything else with your Nuclues platform!"
    };
  }

  // Handle "how's business" type questions
  if (messageLower.includes('how is business') || messageLower.includes('how are sales') || messageLower.includes('business performance')) {
    return {
      content: "Your business is looking great! 🚀 Based on what I can see:\n\n• **Top customer** Sarah Johnson has brought in $89,450\n• **Best product** (Wireless Headphones) has sold 234 units\n• **Biggest order** was $12,450 from Michael Chen\n\nYou've got some really solid numbers! Want me to dive deeper into any specific area?"
    };
  }

  // Dashboard-specific responses
  if (pageContext.includes('Dashboard')) {
    return {
      content: "I can see you're on the Dashboard! This is your command center where you can get a quick overview of everything. I can help you dive deeper into any specific area - just let me know what interests you most:\n\n• Want to see your top performers? Ask about customers or products\n• Need to check recent activity? I can show you orders and trends\n• Looking for specific data? I can help you navigate to the right sections\n\nWhat would you like to explore?"
    };
  }

  // Default response with more helpful information
  return {
    content: `I'm not quite sure what you're asking about "${message}". 🤔 But I'm here to help! Since you're on the ${pageContext} page, here are some things I can assist you with:\n\n• **Data insights**: "Who is my top customer?" or "What's my best product?"\n• **Recent activity**: "Show me recent orders" or "What's new?"\n• **Navigation**: "Take me to the products page" or "Where can I find customer data?"\n• **General help**: "What can you do?" or "Help me with..."\n\nJust ask me anything and I'll do my best to help! 😊`
  };
};

export interface StructuredData {
  type: 'customer' | 'product' | 'order' | 'revenue' | 'list';
  title: string;
  subtitle?: string;
  data: any;
  actions?: Array<{
    label: string;
    action: string;
    icon?: string;
  }>;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  pageContext?: string;
  structuredData?: StructuredData;
}

export interface ChatConversation {
  id: string;
  name: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  pageContext?: string;
  folderId?: string;
}

export interface ChatFolder {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  color?: string;
}

interface ChatContextType {
  // State
  conversations: ChatConversation[];
  folders: ChatFolder[];
  currentConversation: ChatConversation | null;
  isChatOpen: boolean;
  isChatExpanded: boolean;
  isLoading: boolean;
  
  // Actions
  openChat: () => void;
  closeChat: () => void;
  toggleChat: () => void;
  expandChat: () => void;
  collapseChat: () => void;
  sendMessage: (content: string) => Promise<void>;
  createNewConversation: (name?: string, folderId?: string) => void;
  switchConversation: (conversationId: string) => void;
  deleteConversation: (conversationId: string) => void;
  renameConversation: (conversationId: string, name: string) => void;
  createFolder: (name: string, color?: string) => void;
  renameFolder: (folderId: string, name: string) => void;
  deleteFolder: (folderId: string) => void;
  moveConversationToFolder: (conversationId: string, folderId?: string) => void;
  getCurrentPageContext: () => string;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const location = useLocation();
  const [conversations, setConversations] = useState<ChatConversation[]>([]);
  const [folders, setFolders] = useState<ChatFolder[]>([]);
  const [currentConversation, setCurrentConversation] = useState<ChatConversation | null>(null);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isChatExpanded, setIsChatExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get current page context for AI responses
  const getCurrentPageContext = useCallback(() => {
    const path = location.pathname;
    const pathSegments = path.split('/').filter(Boolean);
    
    // Map routes to readable context
    const routeContexts: Record<string, string> = {
      '/': 'Dashboard - Overview of your Nuclues platform',
      '/products': 'Products Management - View and manage all products',
      '/products/create': 'Create Product - Add new products to your catalog',
      '/orders': 'Orders Management - View and manage customer orders',
      '/customers': 'Customer Management - Manage customer information',
      '/settings': 'Settings - Configure your platform settings',
      '/media': 'Media Management - Manage images and files',
      '/categories': 'Categories - Organize products by categories',
      '/brands': 'Brands - Manage product brands',
      '/integrations': 'Integrations - Connect with external platforms',
    };

    // Find the best matching context
    for (const [route, context] of Object.entries(routeContexts)) {
      if (path.startsWith(route)) {
        return context;
      }
    }

    // Fallback for dynamic routes
    if (path.includes('/products/')) return 'Product Details - Viewing specific product information';
    if (path.includes('/orders/')) return 'Order Details - Viewing specific order information';
    if (path.includes('/customers/')) return 'Customer Details - Viewing specific customer information';
    
    return 'General Navigation - You are browsing the Nuclues platform';
  }, [location.pathname]);

  const openChat = useCallback(() => {
    setIsChatOpen(true);
    if (!currentConversation) {
      createNewConversation();
    }
  }, [currentConversation]);

  const closeChat = useCallback(() => {
    setIsChatOpen(false);
    setIsChatExpanded(false);
  }, []);

  const toggleChat = useCallback(() => {
    if (isChatOpen) {
      closeChat();
    } else {
      openChat();
    }
  }, [isChatOpen, openChat, closeChat]);

  const expandChat = useCallback(() => {
    setIsChatExpanded(true);
  }, []);

  const collapseChat = useCallback(() => {
    setIsChatExpanded(false);
  }, []);

  const createNewConversation = useCallback((name?: string, folderId?: string) => {
    const newConversation: ChatConversation = {
      id: `conv_${Date.now()}`,
      name: name || `New Conversation ${conversations.length + 1}`,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      pageContext: getCurrentPageContext(),
      folderId,
    };
    
    setConversations(prev => [newConversation, ...prev]);
    setCurrentConversation(newConversation);
  }, [conversations.length, getCurrentPageContext]);

  const switchConversation = useCallback((conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
      setCurrentConversation(conversation);
    }
  }, [conversations]);

  const deleteConversation = useCallback((conversationId: string) => {
    setConversations(prev => prev.filter(c => c.id !== conversationId));
    if (currentConversation?.id === conversationId) {
      setCurrentConversation(null);
    }
  }, [currentConversation]);

  const renameConversation = useCallback((conversationId: string, name: string) => {
    setConversations(prev => 
      prev.map(c => 
        c.id === conversationId 
          ? { ...c, name, updatedAt: new Date() }
          : c
      )
    );
    
    if (currentConversation?.id === conversationId) {
      setCurrentConversation(prev => prev ? { ...prev, name, updatedAt: new Date() } : null);
    }
  }, [currentConversation]);

  // Folder management functions
  const createFolder = useCallback((name: string, color?: string) => {
    const newFolder: ChatFolder = {
      id: `folder_${Date.now()}`,
      name,
      createdAt: new Date(),
      updatedAt: new Date(),
      color: color || '#3B82F6', // Default blue color
    };
    
    setFolders(prev => [newFolder, ...prev]);
  }, []);

  const renameFolder = useCallback((folderId: string, name: string) => {
    setFolders(prev => 
      prev.map(f => 
        f.id === folderId 
          ? { ...f, name, updatedAt: new Date() }
          : f
      )
    );
  }, []);

  const deleteFolder = useCallback((folderId: string) => {
    // Move conversations out of the folder before deleting
    setConversations(prev => 
      prev.map(c => 
        c.folderId === folderId 
          ? { ...c, folderId: undefined }
          : c
      )
    );
    
    setFolders(prev => prev.filter(f => f.id !== folderId));
  }, []);

  const moveConversationToFolder = useCallback((conversationId: string, folderId?: string) => {
    setConversations(prev => 
      prev.map(c => 
        c.id === conversationId 
          ? { ...c, folderId, updatedAt: new Date() }
          : c
      )
    );
    
    if (currentConversation?.id === conversationId) {
      setCurrentConversation(prev => prev ? { ...prev, folderId, updatedAt: new Date() } : null);
    }
  }, [currentConversation]);

  const sendMessage = useCallback(async (content: string) => {
    if (!currentConversation || !content.trim()) return;

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
      pageContext: getCurrentPageContext(),
    };

    // Add user message to conversation
    const updatedConversation = {
      ...currentConversation,
      messages: [...currentConversation.messages, userMessage],
      updatedAt: new Date(),
    };

    setCurrentConversation(updatedConversation);
    setConversations(prev => 
      prev.map(c => c.id === currentConversation.id ? updatedConversation : c)
    );

    setIsLoading(true);

    try {
      // Try to use real AI first, fallback to mock if not configured
      let aiResponse: string;
      let structuredData: StructuredData | undefined;
      
      try {
        const aiService = aiServiceFactory.getService('openai'); // You can change this to 'anthropic' or 'google'
        console.log('Attempting to use OpenAI API...');
        console.log('API Key available:', !!import.meta.env.VITE_OPENAI_API_KEY);
        console.log('Page context:', getCurrentPageContext());
        console.log('User message:', content);
        
        const response = await aiService.generateResponse({
          message: content,
          conversationHistory: currentConversation.messages.slice(-10).map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          pageContext: getCurrentPageContext(),
        });
        console.log('OpenAI response received:', response.content.substring(0, 100) + '...');
        aiResponse = response.content;
      } catch (aiError) {
        console.error('AI service error details:', aiError);
        console.log('AI service not available, using mock response');
        // Fallback to mock response if AI service is not configured
        const mockResponse = generateMockResponse(content, getCurrentPageContext(), currentConversation.messages);
        aiResponse = mockResponse.content;
        structuredData = mockResponse.structuredData;
      }
      
      const assistantMessage: ChatMessage = {
        id: `msg_${Date.now()}_assistant`,
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date(),
        pageContext: getCurrentPageContext(),
        structuredData: structuredData,
      };

      const finalConversation = {
        ...updatedConversation,
        messages: [...updatedConversation.messages, assistantMessage],
        updatedAt: new Date(),
      };

      setCurrentConversation(finalConversation);
      setConversations(prev => 
        prev.map(c => c.id === currentConversation.id ? finalConversation : c)
      );
    } catch (error) {
      console.error('Failed to send message:', error);
      // Add error message to conversation
      const errorMessage: ChatMessage = {
        id: `msg_${Date.now()}_error`,
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date(),
        pageContext: getCurrentPageContext(),
      };

      const errorConversation = {
        ...updatedConversation,
        messages: [...updatedConversation.messages, errorMessage],
        updatedAt: new Date(),
      };

      setCurrentConversation(errorConversation);
      setConversations(prev => 
        prev.map(c => c.id === currentConversation.id ? errorConversation : c)
      );
    } finally {
      setIsLoading(false);
    }
  }, [currentConversation, getCurrentPageContext]);

  const value: ChatContextType = {
    conversations,
    folders,
    currentConversation,
    isChatOpen,
    isChatExpanded,
    isLoading,
    openChat,
    closeChat,
    toggleChat,
    expandChat,
    collapseChat,
    sendMessage,
    createNewConversation,
    switchConversation,
    deleteConversation,
    renameConversation,
    createFolder,
    renameFolder,
    deleteFolder,
    moveConversationToFolder,
    getCurrentPageContext,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
}; 
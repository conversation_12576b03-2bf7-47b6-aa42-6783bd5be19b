import React, { useState, useEffect, useMemo } from 'react'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Eye, 
  Tag,
  ChevronDown,
  ChevronUp,
  Download,
  RefreshCw,
  Archive,
  Copy,
  ExternalLink,
  AlertTriangle,
  CheckCircle,
  X,
  Building,
  Globe
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import SectionHeader from '../components/SectionHeader'

interface Brand {
  id: string
  name: string
  description: string
  parentBrand?: string
  website?: string
  logo?: string
  productCount: number
  status: 'active' | 'inactive'
  country: string
  foundedYear?: number
  createdAt: string
  updatedAt: string
  slug?: string
  sortOrder?: number
}

interface SortConfig {
  key: keyof Brand
  direction: 'asc' | 'desc'
}

const BrandsPage: React.FC = () => {
  const [brands, setBrands] = useState<Brand[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  const [levelFilter, setLevelFilter] = useState<'all' | 'root' | 'child'>('all')
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'name', direction: 'asc' })
  const [openActionMenu, setOpenActionMenu] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  // Mock data for demonstration with hierarchical structure
  useEffect(() => {
    const mockBrands: Brand[] = [
      {
        id: '1',
        name: 'Apple',
        description: 'Technology company that designs, develops, and sells consumer electronics',
        website: 'https://www.apple.com',
        productCount: 45,
        status: 'active',
        country: 'United States',
        foundedYear: 1976,
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
        slug: 'apple',
        sortOrder: 1
      },
      {
        id: '2',
        name: 'iPhone',
        description: 'Apple\'s flagship smartphone line',
        parentBrand: '1',
        website: 'https://www.apple.com/iphone',
        productCount: 12,
        status: 'active',
        country: 'United States',
        foundedYear: 2007,
        createdAt: '2024-01-16',
        updatedAt: '2024-01-21',
        slug: 'apple-iphone',
        sortOrder: 2
      },
      {
        id: '3',
        name: 'Mac',
        description: 'Apple\'s line of personal computers',
        parentBrand: '1',
        website: 'https://www.apple.com/mac',
        productCount: 8,
        status: 'active',
        country: 'United States',
        foundedYear: 1984,
        createdAt: '2024-01-17',
        updatedAt: '2024-01-22',
        slug: 'apple-mac',
        sortOrder: 3
      },
      {
        id: '4',
        name: 'MacBook',
        description: 'Apple\'s line of laptop computers',
        parentBrand: '3',
        website: 'https://www.apple.com/macbook',
        productCount: 4,
        status: 'active',
        country: 'United States',
        foundedYear: 2006,
        createdAt: '2024-01-18',
        updatedAt: '2024-01-23',
        slug: 'apple-mac-macbook',
        sortOrder: 4
      },
      {
        id: '5',
        name: 'Nike',
        description: 'American multinational corporation engaged in the design, development, manufacturing, and worldwide marketing of footwear',
        website: 'https://www.nike.com',
        productCount: 89,
        status: 'active',
        country: 'United States',
        foundedYear: 1964,
        createdAt: '2024-01-10',
        updatedAt: '2024-01-18',
        slug: 'nike',
        sortOrder: 5
      },
      {
        id: '6',
        name: 'Nike Air',
        description: 'Nike\'s signature air-cushioned footwear technology',
        parentBrand: '5',
        website: 'https://www.nike.com/air',
        productCount: 23,
        status: 'active',
        country: 'United States',
        foundedYear: 1978,
        createdAt: '2024-01-11',
        updatedAt: '2024-01-19',
        slug: 'nike-air',
        sortOrder: 6
      },
      {
        id: '7',
        name: 'Nike Jordan',
        description: 'Nike\'s collaboration with Michael Jordan',
        parentBrand: '5',
        website: 'https://www.nike.com/jordan',
        productCount: 15,
        status: 'active',
        country: 'United States',
        foundedYear: 1984,
        createdAt: '2024-01-12',
        updatedAt: '2024-01-20',
        slug: 'nike-jordan',
        sortOrder: 7
      },
      {
        id: '8',
        name: 'Samsung',
        description: 'South Korean multinational electronics company',
        website: 'https://www.samsung.com',
        productCount: 67,
        status: 'active',
        country: 'South Korea',
        foundedYear: 1938,
        createdAt: '2024-01-05',
        updatedAt: '2024-01-15',
        slug: 'samsung',
        sortOrder: 8
      },
      {
        id: '9',
        name: 'Samsung Galaxy',
        description: 'Samsung\'s line of Android smartphones',
        parentBrand: '8',
        website: 'https://www.samsung.com/galaxy',
        productCount: 18,
        status: 'active',
        country: 'South Korea',
        foundedYear: 2009,
        createdAt: '2024-01-06',
        updatedAt: '2024-01-16',
        slug: 'samsung-galaxy',
        sortOrder: 9
      },
      {
        id: '10',
        name: 'Adidas',
        description: 'German multinational corporation, founded and headquartered in Herzogenaurach, Germany',
        website: 'https://www.adidas.com',
        productCount: 56,
        status: 'active',
        country: 'Germany',
        foundedYear: 1949,
        createdAt: '2024-01-12',
        updatedAt: '2024-01-16',
        slug: 'adidas',
        sortOrder: 10
      },
      {
        id: '11',
        name: 'Sony',
        description: 'Japanese multinational conglomerate corporation headquartered in Kōnan, Minato, Tokyo',
        website: 'https://www.sony.com',
        productCount: 34,
        status: 'active',
        country: 'Japan',
        foundedYear: 1946,
        createdAt: '2024-01-08',
        updatedAt: '2024-01-14',
        slug: 'sony',
        sortOrder: 11
      },
      {
        id: '12',
        name: 'Sony PlayStation',
        description: 'Sony\'s line of gaming consoles',
        parentBrand: '11',
        website: 'https://www.playstation.com',
        productCount: 6,
        status: 'active',
        country: 'Japan',
        foundedYear: 1994,
        createdAt: '2024-01-09',
        updatedAt: '2024-01-15',
        slug: 'sony-playstation',
        sortOrder: 12
      }
    ]
    
    setTimeout(() => {
      setBrands(mockBrands)
      setLoading(false)
    }, 1000)
  }, [])

  // Build hierarchical structure
  const buildHierarchy = (brands: Brand[]) => {
    const brandMap = new Map<string, Brand>()
    const rootBrands: Brand[] = []
    const childBrands: Brand[] = []

    // Create a map for quick lookup
    brands.forEach(brand => {
      brandMap.set(brand.id, brand)
      if (brand.parentBrand) {
        childBrands.push(brand)
      } else {
        rootBrands.push(brand)
      }
    })

    // Sort root brands
    rootBrands.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))

    return { brandMap, rootBrands, childBrands }
  }

  // Get brand path (breadcrumb)
  const getBrandPath = (brand: Brand, brandMap: Map<string, Brand>): string[] => {
    const path: string[] = [brand.name]
    let currentBrand = brand

    while (currentBrand.parentBrand && brandMap.has(currentBrand.parentBrand)) {
      currentBrand = brandMap.get(currentBrand.parentBrand)!
      path.unshift(currentBrand.name)
    }

    return path
  }

  // Get brand level (depth)
  const getBrandLevel = (brand: Brand, brandMap: Map<string, Brand>): number => {
    let level = 0
    let currentBrand = brand

    while (currentBrand.parentBrand && brandMap.has(currentBrand.parentBrand)) {
      level++
      currentBrand = brandMap.get(currentBrand.parentBrand)!
    }

    return level
  }

  // Get all children of a brand
  const getBrandChildren = (brandId: string): Brand[] => {
    return brands.filter(brand => brand.parentBrand === brandId)
  }

  // Check if brand has children
  const hasChildren = (brandId: string): boolean => {
    return brands.some(brand => brand.parentBrand === brandId)
  }

  // Filter and sort brands with hierarchy
  const filteredAndSortedBrands = useMemo(() => {
    const { brandMap, rootBrands, childBrands } = buildHierarchy(brands)
    
    let filtered = brands.filter(brand => {
      const matchesSearch = brand.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          brand.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          brand.country.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === 'all' || brand.status === statusFilter
      
      // Level filter
      let matchesLevel = true
      if (levelFilter !== 'all') {
        const level = getBrandLevel(brand, brandMap)
        if (levelFilter === 'root') {
          matchesLevel = level === 0
        } else if (levelFilter === 'child') {
          matchesLevel = level > 0
        }
      }
      
      return matchesSearch && matchesStatus && matchesLevel
    })

    // Sort brands hierarchically
    filtered.sort((a, b) => {
      // First sort by hierarchy level
      const aLevel = getBrandLevel(a, brandMap)
      const bLevel = getBrandLevel(b, brandMap)
      
      if (aLevel !== bLevel) {
        return aLevel - bLevel
      }
      
      // Then sort by the selected sort config
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue)
        return sortConfig.direction === 'asc' ? comparison : -comparison
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue
      }
      
      return 0
    })

    return filtered
  }, [brands, searchTerm, statusFilter, levelFilter, sortConfig])

  // Handle sorting
  const handleSort = (key: keyof Brand) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }))
  }

  // Get sort icon
  const getSortIcon = (key: keyof Brand) => {
    if (sortConfig.key !== key) {
      return <ChevronDown className="h-4 w-4 text-gray-400" />
    }
    return sortConfig.direction === 'asc' 
      ? <ChevronUp className="h-4 w-4 text-blue-600" />
      : <ChevronDown className="h-4 w-4 text-blue-600" />
  }

  const handleSelectAll = () => {
    if (selectedBrands.length === filteredAndSortedBrands.length) {
      setSelectedBrands([])
    } else {
      setSelectedBrands(filteredAndSortedBrands.map(brand => brand.id))
    }
  }

  const handleSelectBrand = (brandId: string) => {
    setSelectedBrands(prev =>
      prev.includes(brandId)
        ? prev.filter(id => id !== brandId)
        : [...prev, brandId]
    )
  }

  const handleDeleteBrands = async () => {
    if (selectedBrands.length === 0) {
      toast.error('Please select brands to delete')
      return
    }
    
    setIsDeleting(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setBrands(prev => prev.filter(brand => !selectedBrands.includes(brand.id)))
      setSelectedBrands([])
      setShowDeleteConfirm(false)
      toast.success(`${selectedBrands.length} brands deleted successfully`)
    } catch (error) {
      toast.error('Failed to delete brands')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleBulkAction = (action: string) => {
    if (selectedBrands.length === 0) {
      toast.error('Please select brands first')
      return
    }

    switch (action) {
      case 'activate':
        setBrands(prev => prev.map(brand => 
          selectedBrands.includes(brand.id) ? { ...brand, status: 'active' as const } : brand
        ))
        toast.success(`${selectedBrands.length} brands activated`)
        break
      case 'deactivate':
        setBrands(prev => prev.map(brand => 
          selectedBrands.includes(brand.id) ? { ...brand, status: 'inactive' as const } : brand
        ))
        toast.success(`${selectedBrands.length} brands deactivated`)
        break
      case 'archive':
        toast.success(`${selectedBrands.length} brands archived`)
        break
      default:
        break
    }
    setSelectedBrands([])
  }

  const toggleActionMenu = (brandId: string) => {
    setOpenActionMenu(openActionMenu === brandId ? null : brandId)
  }

  // Close action menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openActionMenu && !(event.target as Element).closest('.action-menu')) {
        setOpenActionMenu(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [openActionMenu])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading brands...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<Tag className="h-7 w-7" />}
        title="Brands"
        subtitle="Manage your product brands and organize them hierarchically"
        actions={
          <div className="flex items-center space-x-3">
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              Add Brand
            </button>
          </div>
        }
      />

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Tag className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-600">Total Brands</div>
              <div className="text-2xl font-bold text-gray-900">{brands.length}</div>
              <div className="text-xs text-gray-500">
                {(() => {
                  const { rootBrands, childBrands } = buildHierarchy(brands)
                  return `${rootBrands.length} root, ${childBrands.length} sub-brands`
                })()}
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-600">Active Brands</div>
              <div className="text-2xl font-bold text-green-600">
                {brands.filter(brand => brand.status === 'active').length}
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Building className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-600">Total Products</div>
              <div className="text-2xl font-bold text-purple-600">
                {brands.reduce((sum, brand) => sum + brand.productCount, 0).toLocaleString()}
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Globe className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-600">Countries</div>
              <div className="text-2xl font-bold text-orange-600">
                {new Set(brands.map(brand => brand.country)).size}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search brands by name, description, or country..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
                showFilters 
                  ? 'border-blue-500 text-blue-700 bg-blue-50' 
                  : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
              }`}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
            {selectedBrands.length > 0 && (
              <>
                <button
                  onClick={() => handleBulkAction('activate')}
                  className="inline-flex items-center px-3 py-2 border border-green-300 rounded-md text-sm font-medium text-green-700 bg-white hover:bg-green-50 transition-colors"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Activate ({selectedBrands.length})
                </button>
                <button
                  onClick={() => handleBulkAction('deactivate')}
                  className="inline-flex items-center px-3 py-2 border border-yellow-300 rounded-md text-sm font-medium text-yellow-700 bg-white hover:bg-yellow-50 transition-colors"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Deactivate ({selectedBrands.length})
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-colors"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete ({selectedBrands.length})
                </button>
              </>
            )}
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Level</label>
                <select
                  value={levelFilter}
                  onChange={(e) => setLevelFilter(e.target.value as 'all' | 'root' | 'child')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Levels</option>
                  <option value="root">Root Brands</option>
                  <option value="child">Sub-brands</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select
                  value={`${sortConfig.key}-${sortConfig.direction}`}
                  onChange={(e) => {
                    const [key, direction] = e.target.value.split('-')
                    setSortConfig({ key: key as keyof Brand, direction: direction as 'asc' | 'desc' })
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="name-asc">Name (A-Z)</option>
                  <option value="name-desc">Name (Z-A)</option>
                  <option value="productCount-desc">Most Products</option>
                  <option value="productCount-asc">Least Products</option>
                  <option value="updatedAt-desc">Recently Updated</option>
                  <option value="updatedAt-asc">Oldest Updated</option>
                </select>
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => {
                    setSearchTerm('')
                    setStatusFilter('all')
                    setLevelFilter('all')
                    setSortConfig({ key: 'name', direction: 'asc' })
                  }}
                  className="w-full px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Brands Table */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedBrands.length === filteredAndSortedBrands.length && filteredAndSortedBrands.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Brand</span>
                    {getSortIcon('name')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('country')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Country</span>
                    {getSortIcon('country')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('productCount')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Products</span>
                    {getSortIcon('productCount')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Status</span>
                    {getSortIcon('status')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('updatedAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</span>
                    {getSortIcon('updatedAt')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Level
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAndSortedBrands.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center">
                    <div className="text-gray-500">
                      <Tag className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-lg font-medium">No brands found</p>
                      <p className="text-sm">Try adjusting your search or filters</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredAndSortedBrands.map((brand) => (
                  <tr key={brand.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedBrands.includes(brand.id)}
                        onChange={() => handleSelectBrand(brand.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                            <Building className="h-5 w-5 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-4 flex-1">
                          <div className="flex items-center space-x-2">
                            {/* Indentation for hierarchy */}
                            {(() => {
                              const { brandMap } = buildHierarchy(brands)
                              const level = getBrandLevel(brand, brandMap)
                              return Array.from({ length: level }, (_, i) => (
                                <div key={i} className="w-4 h-px bg-gray-300"></div>
                              ))
                            })()}
                            
                            {/* Brand name with hierarchy indicator */}
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium text-gray-900">{brand.name}</span>
                              {brand.parentBrand && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                  Sub-brand
                                </span>
                              )}
                              {hasChildren(brand.id) && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-600">
                                  Parent
                                </span>
                              )}
                            </div>
                          </div>
                          
                          {/* Brand path/breadcrumb */}
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {(() => {
                              const { brandMap } = buildHierarchy(brands)
                              const path = getBrandPath(brand, brandMap)
                              if (path.length > 1) {
                                return path.slice(0, -1).join(' > ') + ' > ' + brand.name
                              }
                              return brand.description
                            })()}
                          </div>
                          
                          {brand.website && (
                            <a 
                              href={brand.website} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
                            >
                              <Globe className="h-3 w-3 mr-1" />
                              Website
                            </a>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {brand.country}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {brand.productCount.toLocaleString()} products
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        brand.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {brand.status === 'active' ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <AlertTriangle className="h-3 w-3 mr-1" />
                        )}
                        {brand.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(brand.updatedAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {(() => {
                        const { brandMap } = buildHierarchy(brands)
                        const level = getBrandLevel(brand, brandMap)
                        return (
                          <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                            level === 0 
                              ? 'bg-blue-100 text-blue-800' 
                              : level === 1 
                              ? 'bg-green-100 text-green-800'
                              : 'bg-purple-100 text-purple-800'
                          }`}>
                            {level === 0 ? 'Root' : level === 1 ? 'Sub' : `Level ${level}`}
                          </span>
                        )
                      })()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button 
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors"
                          title="View Brand"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button 
                          className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50 transition-colors"
                          title="Edit Brand"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <div className="relative action-menu">
                          <button
                            onClick={() => toggleActionMenu(brand.id)}
                            className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50 transition-colors"
                            title="More Actions"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </button>
                          {openActionMenu === brand.id && (
                            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                              <div className="py-1">
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                  <Copy className="h-4 w-4 mr-2" />
                                  Duplicate
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                  <Archive className="h-4 w-4 mr-2" />
                                  Archive
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                  <ExternalLink className="h-4 w-4 mr-2" />
                                  View Products
                                </button>
                                <hr className="my-1" />
                                <button className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 text-center mt-4">Delete Brands</h3>
              <p className="text-sm text-gray-500 text-center mt-2">
                Are you sure you want to delete {selectedBrands.length} selected brand{selectedBrands.length > 1 ? 's' : ''}? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isDeleting}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteBrands}
                  disabled={isDeleting}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 flex items-center"
                >
                  {isDeleting && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>}
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create Brand Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Create New Brand</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Brand Name *</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Enter brand name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    rows={3}
                    placeholder="Enter brand description"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Parent Brand</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="">No parent brand (Root level)</option>
                    {(() => {
                      const { brandMap } = buildHierarchy(brands)
                      return brands
                        .filter(brand => brand.status === 'active') // Only show active brands as parents
                        .map(brand => {
                          const level = getBrandLevel(brand, brandMap)
                          const indent = '—'.repeat(level)
                          return (
                            <option key={brand.id} value={brand.id}>
                              {indent} {brand.name} {hasChildren(brand.id) ? '(has children)' : ''}
                            </option>
                          )
                        })
                    })()}
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Select a parent brand to create a sub-brand, or leave empty for a root brand
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                  <input
                    type="url"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="https://www.example.com"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Enter country"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Founded Year</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="e.g., 1990"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    Create Brand
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default BrandsPage 
import React, { useState } from 'react';
import { ChevronDown, ChevronRight, FolderPlus, Folder, Plus, Minus } from 'lucide-react';

export interface CategoryNode {
  id: string;
  name: string;
  children?: CategoryNode[];
}

interface CategoryTreeSelectProps {
  categories: CategoryNode[];
  value: string[];
  onChange: (selected: string[]) => void;
}

const CategoryTreeSelect: React.FC<CategoryTreeSelectProps> = ({
  categories,
  value,
  onChange,
}) => {
  const [expanded, setExpanded] = useState<Record<string, boolean>>({});
  const [hovered, setHovered] = useState<string | null>(null);

  const toggleExpand = (id: string) => {
    setExpanded(prev => ({ ...prev, [id]: !prev[id] }));
  };

  const handleSelect = (id: string) => {
    if (value.includes(id)) {
      onChange(value.filter(v => v !== id));
    } else {
      onChange([...value, id]);
    }
  };

  // Render vertical dotted line for tree structure
  const renderLine = (level: number, isLast: boolean) => (
    <span
      className={`absolute left-0 top-0 h-full border-l border-dotted border-gray-300 ${isLast ? 'border-transparent' : ''}`}
      style={{ left: `${level * 24 + 12}px`, width: '1px' }}
    />
  );

  const renderTree = (nodes: CategoryNode[], level = 0, parentLast = false) => (
    <ul className={level === 0 ? 'space-y-1 relative' : 'ml-6 space-y-1 relative'}>
      {nodes.map((node, idx) => {
        const isSelected = value.includes(node.id);
        const isExpanded = expanded[node.id];
        const isLast = idx === nodes.length - 1;
        return (
          <li key={node.id} className="relative">
            {/* Vertical dotted line */}
            {level > 0 && renderLine(level - 1, parentLast)}
            <div
              className={`flex items-center gap-2 px-2 py-1 rounded cursor-pointer group relative ${
                isSelected ? 'bg-blue-50' : hovered === node.id ? 'bg-gray-50' : ''
              }`}
              onMouseEnter={() => setHovered(node.id)}
              onMouseLeave={() => setHovered(null)}
              style={{ marginLeft: `${level * 24}px` }}
            >
              {/* Expand/collapse button */}
              {node.children && node.children.length > 0 ? (
                <button
                  type="button"
                  className="flex items-center justify-center w-6 h-6 rounded-full border border-gray-300 bg-white text-gray-500 hover:bg-blue-50 hover:text-blue-600 focus:outline-none"
                  onClick={() => toggleExpand(node.id)}
                  tabIndex={-1}
                  style={{ boxShadow: '0 1px 2px rgba(0,0,0,0.02)' }}
                >
                  {isExpanded ? <Minus className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                </button>
              ) : (
                <span className="w-6 h-6 inline-block" />
              )}
              {/* Checkbox */}
              <input
                type="checkbox"
                checked={isSelected}
                onChange={() => handleSelect(node.id)}
                className="accent-blue-600"
              />
              {/* Folder icon */}
              <Folder className="h-4 w-4 text-blue-400" />
              {/* Category name */}
              <span className="text-sm text-gray-900 flex-1">{node.name}</span>
              {/* Add sub-category button (UI only) */}
              {hovered === node.id && (
                <button
                  type="button"
                  className="flex items-center text-blue-600 hover:underline text-xs px-2 py-1"
                  tabIndex={-1}
                >
                  <FolderPlus className="h-4 w-4 mr-1" /> Add sub-category
                </button>
              )}
            </div>
            {/* Children */}
            {node.children && node.children.length > 0 && isExpanded && (
              renderTree(node.children, level + 1, isLast)
            )}
          </li>
        );
      })}
    </ul>
  );

  return (
    <div className="border border-gray-200 rounded-lg p-2 bg-white max-h-80 overflow-y-auto shadow-sm relative">
      {/* Add category button at the top right (UI only) */}
      <button
        type="button"
        className="absolute top-2 right-2 flex items-center text-blue-600 hover:underline text-sm px-2 py-1 z-10 bg-white"
        tabIndex={-1}
      >
        <Plus className="h-4 w-4 mr-1" /> Add category
      </button>
      {renderTree(categories)}
    </div>
  );
};

// Sample usage (for demonstration)
export const SampleCategoryTreeDemo = () => {
  const [selected, setSelected] = useState<string[]>([]);
  const sampleTree: CategoryNode[] = [
    {
      id: '1',
      name: 'New Arrivals',
    },
    {
      id: '2',
      name: 'Disposables',
      children: [
        { id: '2-1', name: '0% Nicotine' },
        { id: '2-2', name: '2% Nicotine' },
        { id: '2-3', name: '3% Nicotine' },
        { id: '2-4', name: '5% Nicotine' },
        { id: '2-5', name: 'Disposable Pods' },
        { id: '2-6', name: 'Disposable Starter Kit' },
        { id: '2-7', name: 'Disposable Hookah' },
        { id: '2-8', name: 'Synthetic Nic Disposables' },
        { id: '2-9', name: 'North Vape' },
        { id: '2-10', name: 'Pillow Talk' },
      ],
    },
    {
      id: '3',
      name: 'Promos',
    },
    {
      id: '4',
      name: 'Deals',
    },
    {
      id: '5',
      name: 'E-Liquids',
    },
  ];
  return (
    <div className="max-w-2xl mx-auto mt-8">
      <CategoryTreeSelect categories={sampleTree} value={selected} onChange={setSelected} />
    </div>
  );
};

export default CategoryTreeSelect; 
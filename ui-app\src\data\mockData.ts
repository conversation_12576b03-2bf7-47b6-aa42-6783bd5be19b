// Mock data structures for the application
import { ComponentType } from 'react'

export interface App {
  id: string
  name: string
  description: string
  icon: string | ComponentType<any>
  isConnected: boolean
  lastSync?: string
  status: 'connected' | 'disconnected' | 'error' | 'syncing'
  connectionDetails?: {
    apiKey?: string
    storeUrl?: string
    accessToken?: string
    clientId?: string
    connectedAt?: string
    lastError?: string
  }
  syncStats?: {
    totalSyncs: number
    successfulSyncs: number
    failedSyncs: number
    lastSyncDuration: string
    avgSyncDuration: string
  }
}

export interface Job {
  id: string
  name: string
  description: string
  app: string
  appId: string
  status: 'running' | 'paused' | 'error' | 'completed'
  schedule: string
  lastRun?: string
  nextRun?: string
  duration?: string
  successRate?: number
  totalRuns?: number
  config: {
    syncTypes: string[]
    batchSize: number
    retryAttempts: number
    timeout: number
    mapping?: Record<string, string>
  }
  logs?: JobLog[]
}

export interface JobLog {
  id: string
  jobId: string
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'success'
  message: string
  details?: any
}

export interface Product {
  id: string
  name: string
  sku: string
  price: number
  inventory: number
  status: 'active' | 'inactive'
  source: string
  lastUpdated: string
}

export interface Order {
  id: string
  customer: string
  email: string
  date: string
  total: string
  status: 'Completed' | 'Processing' | 'Shipped' | 'Cancelled'
  source: string
  items: number
}

export interface Customer {
  id: string
  name: string
  email: string
  totalOrders: number
  totalSpent: string
  lastOrder: string
  source: string
}

// Mock Apps Data
export const mockApps: App[] = [
  {
    id: 'shopify',
    name: 'Shopify',
    description: 'Sync products, orders, and customers from your Shopify store',
    icon: 'ShoppingBag',
    isConnected: true,
    status: 'connected',
    lastSync: '2 minutes ago',
    connectionDetails: {
      storeUrl: 'my-store.myshopify.com',
      accessToken: 'shpat_***************',
      connectedAt: '2023-06-15T10:30:00Z',
    },
    syncStats: {
      totalSyncs: 156,
      successfulSyncs: 153,
      failedSyncs: 3,
      lastSyncDuration: '45s',
      avgSyncDuration: '52s'
    }
  },
  {
    id: 'bigcommerce',
    name: 'BigCommerce',
    description: 'Import and sync your BigCommerce store data',
    icon: 'Store',
    isConnected: false,
    status: 'disconnected',
    syncStats: {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncDuration: '0s',
      avgSyncDuration: '0s'
    }
  },
  {
    id: 'woocommerce',
    name: 'WooCommerce',
    description: 'Connect your WordPress WooCommerce store',
    icon: 'Package',
    isConnected: false,
    status: 'disconnected',
    syncStats: {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncDuration: '0s',
      avgSyncDuration: '0s'
    }
  }
]

// Mock Jobs Data
export const mockJobs: Job[] = [
  {
    id: '1',
    name: 'Shopify Product Sync',
    description: 'Sync products from Shopify store every hour',
    app: 'Shopify',
    appId: 'shopify',
    status: 'running',
    schedule: 'Every hour',
    lastRun: '2 minutes ago',
    nextRun: 'In 58 minutes',
    duration: '45s',
    successRate: 98,
    totalRuns: 156,
    config: {
      syncTypes: ['products', 'inventory'],
      batchSize: 100,
      retryAttempts: 3,
      timeout: 30,
      mapping: {
        'title': 'name',
        'handle': 'slug',
        'vendor': 'brand'
      }
    }
  },
  {
    id: '2',
    name: 'BigCommerce Order Import',
    description: 'Import new orders from BigCommerce',
    app: 'BigCommerce',
    appId: 'bigcommerce',
    status: 'paused',
    schedule: 'Every 30 minutes',
    lastRun: '1 hour ago',
    nextRun: 'Paused',
    duration: '1m 23s',
    successRate: 95,
    totalRuns: 89,
    config: {
      syncTypes: ['orders', 'customers'],
      batchSize: 50,
      retryAttempts: 2,
      timeout: 60
    }
  },
  {
    id: '3',
    name: 'Customer Data Sync',
    description: 'Sync customer information across platforms',
    app: 'Multiple',
    appId: 'multiple',
    status: 'error',
    schedule: 'Daily at 2:00 AM',
    lastRun: '6 hours ago',
    nextRun: 'In 18 hours',
    duration: 'Failed',
    successRate: 76,
    totalRuns: 23,
    config: {
      syncTypes: ['customers'],
      batchSize: 200,
      retryAttempts: 5,
      timeout: 120
    }
  }
]

// Mock Products Data
export const mockProducts: Product[] = [
  {
    id: 'prod_001',
    name: 'Wireless Bluetooth Headphones',
    sku: 'WBH-001',
    price: 99.99,
    inventory: 45,
    status: 'active',
    source: 'Shopify',
    lastUpdated: '2023-06-22T14:30:00Z'
  },
  {
    id: 'prod_002',
    name: 'Smart Fitness Watch',
    sku: 'SFW-002',
    price: 199.99,
    inventory: 23,
    status: 'active',
    source: 'BigCommerce',
    lastUpdated: '2023-06-22T12:15:00Z'
  },
  {
    id: 'prod_003',
    name: 'Organic Cotton T-Shirt',
    sku: 'OCT-003',
    price: 29.99,
    inventory: 0,
    status: 'inactive',
    source: 'Shopify',
    lastUpdated: '2023-06-21T16:45:00Z'
  }
]

// Mock Price List Data
import { Channel, CustomerGroup, Supplier, Classification, Tag, PriceListItem } from '../types/priceList'

export const mockChannels: Channel[] = [
  {
    id: 'ch1',
    name: 'Channel 1',
    description: 'Primary sales channel',
    isActive: true
  },
  {
    id: 'ch2',
    name: 'Channel 2',
    description: 'Secondary sales channel',
    isActive: true
  }
]

export const mockCustomerGroups: CustomerGroup[] = [
  { id: 'wholesale', name: 'Wholesale', channel_id: 'ch1' },
  { id: 'value_tier', name: 'Value Tier', channel_id: 'ch1' },
  { id: 'tier_1', name: 'Tier 1 Price List', channel_id: 'ch1' },
  { id: 'tier_2', name: 'Tier 2 Price List', channel_id: 'ch1' },
  { id: 'tier_pro', name: 'Tier Pro', channel_id: 'ch1' },
  { id: 'distributor', name: 'Distributor', channel_id: 'ch1' },
  { id: 'mvd_warehouse', name: 'MVD Warehouse', channel_id: 'ch1' },
  { id: 'tcd_warehouse', name: 'TCD Warehouse', channel_id: 'ch1' },
  { id: 'vip', name: 'VIP', channel_id: 'ch1' }
]

export const mockSuppliers: Supplier[] = [
  {
    id: 'sup_001',
    name: 'AudioTech Supplies',
    contact_email: '<EMAIL>',
    is_primary: true
  },
  {
    id: 'sup_002',
    name: 'FitTech Distribution',
    contact_email: '<EMAIL>',
    is_primary: false
  },
  {
    id: 'sup_003',
    name: 'Organic Textiles Co',
    contact_email: '<EMAIL>',
    is_primary: true
  }
]

export const mockClassifications: Classification[] = [
  { id: 'electronics', name: 'Electronics', description: 'Electronic devices and accessories' },
  { id: 'wearables', name: 'Wearables', description: 'Wearable technology devices' },
  { id: 'apparel', name: 'Apparel', description: 'Clothing and fashion items' },
  { id: 'audio', name: 'Audio Equipment', description: 'Audio devices and accessories' }
]

export const mockTags: Tag[] = [
  { id: 'bestseller', name: 'Bestseller', color: '#10B981' },
  { id: 'new_arrival', name: 'New Arrival', color: '#3B82F6' },
  { id: 'clearance', name: 'Clearance', color: '#EF4444' },
  { id: 'premium', name: 'Premium', color: '#8B5CF6' }
]

export const mockPriceListItems: PriceListItem[] = [
  {
    id: 'pl_001',
    product_id: 'prod_001',
    product_name: 'Wireless Bluetooth Headphones',
    sku: 'WBH-001',
    classification: mockClassifications[0],
    current_stock: 45,
    cost: 60.00,
    pricing: {
      wholesale: { ch1: 89.99, ch2: 92.99 },
      value_tier: { ch1: 94.99, ch2: 97.99 },
      tier_1: { ch1: 99.99, ch2: 102.99 },
      tier_2: { ch1: 104.99, ch2: 107.99 },
      tier_pro: { ch1: 109.99, ch2: 112.99 },
      distributor: { ch1: 79.99, ch2: 82.99 },
      mvd_warehouse: { ch1: 84.99, ch2: 87.99 },
      tcd_warehouse: { ch1: 87.99, ch2: 90.99 },
      vip: { ch1: 74.99, ch2: 77.99 }
    },
    sales_data: {
      ch1: {
        csp_count: 125,
        monthly_sales: { jan: 45, feb: 52, mar: 38, apr: 61, may: 47, jun: 55 }
      },
      ch2: {
        csp_count: 89,
        monthly_sales: { jan: 32, feb: 28, mar: 41, apr: 35, may: 39, jun: 44 }
      }
    },
    weeks_on_hand: 8.2,
    turn_rate: 6.3,
    purchaser: 'John Smith',
    supplier: mockSuppliers[0],
    last_updated_by: 'Admin User',
    date_created: '2024-01-15T10:00:00Z',
    last_updated_date: '2024-01-20T14:30:00Z',
    tags: [mockTags[0], mockTags[3]]
  },
  {
    id: 'pl_002',
    product_id: 'prod_002',
    product_name: 'Smart Fitness Watch',
    sku: 'SFW-002',
    classification: mockClassifications[1],
    current_stock: 23,
    cost: 120.00,
    pricing: {
      wholesale: { ch1: 179.99, ch2: 184.99 },
      value_tier: { ch1: 189.99, ch2: 194.99 },
      tier_1: { ch1: 199.99, ch2: 204.99 },
      tier_2: { ch1: 209.99, ch2: 214.99 },
      tier_pro: { ch1: 219.99, ch2: 224.99 },
      distributor: { ch1: 169.99, ch2: 174.99 },
      mvd_warehouse: { ch1: 174.99, ch2: 179.99 },
      tcd_warehouse: { ch1: 177.99, ch2: 182.99 },
      vip: { ch1: 159.99, ch2: 164.99 }
    },
    sales_data: {
      ch1: {
        csp_count: 98,
        monthly_sales: { jan: 28, feb: 35, mar: 42, apr: 31, may: 38, jun: 45 }
      },
      ch2: {
        csp_count: 67,
        monthly_sales: { jan: 19, feb: 23, mar: 27, apr: 21, may: 25, jun: 29 }
      }
    },
    weeks_on_hand: 12.5,
    turn_rate: 4.2,
    purchaser: 'Sarah Johnson',
    supplier: mockSuppliers[1],
    last_updated_by: 'Manager User',
    date_created: '2024-01-10T09:00:00Z',
    last_updated_date: '2024-01-18T11:15:00Z',
    tags: [mockTags[1]]
  },
  {
    id: 'pl_003',
    product_id: 'prod_003',
    product_name: 'Organic Cotton T-Shirt',
    sku: 'OCT-003',
    classification: mockClassifications[2],
    current_stock: 0,
    cost: 15.00,
    pricing: {
      wholesale: { ch1: 24.99, ch2: 26.99 },
      value_tier: { ch1: 27.99, ch2: 29.99 },
      tier_1: { ch1: 29.99, ch2: 31.99 },
      tier_2: { ch1: 32.99, ch2: 34.99 },
      tier_pro: { ch1: 35.99, ch2: 37.99 },
      distributor: { ch1: 22.99, ch2: 24.99 },
      mvd_warehouse: { ch1: 25.99, ch2: 27.99 },
      tcd_warehouse: { ch1: 26.99, ch2: 28.99 },
      vip: { ch1: 19.99, ch2: 21.99 }
    },
    sales_data: {
      ch1: {
        csp_count: 156,
        monthly_sales: { jan: 78, feb: 65, mar: 82, apr: 91, may: 73, jun: 88 }
      },
      ch2: {
        csp_count: 134,
        monthly_sales: { jan: 62, feb: 58, mar: 71, apr: 69, may: 64, jun: 76 }
      }
    },
    weeks_on_hand: 0,
    turn_rate: 8.7,
    purchaser: 'Mike Davis',
    supplier: mockSuppliers[2],
    last_updated_by: 'Inventory Manager',
    date_created: '2024-01-05T08:30:00Z',
    last_updated_date: '2024-01-22T16:45:00Z',
    tags: [mockTags[2]]
  },
  {
    id: 'pl_004',
    product_id: 'prod_004',
    product_name: 'Wireless Charging Pad',
    sku: 'WCP-004',
    classification: mockClassifications[0],
    current_stock: 67,
    cost: 25.00,
    pricing: {
      wholesale: { ch1: 39.99, ch2: 42.99 },
      value_tier: { ch1: 44.99, ch2: 47.99 },
      tier_1: { ch1: 49.99, ch2: 52.99 },
      tier_2: { ch1: 54.99, ch2: 57.99 },
      tier_pro: { ch1: 59.99, ch2: 62.99 },
      distributor: { ch1: 34.99, ch2: 37.99 },
      mvd_warehouse: { ch1: 37.99, ch2: 40.99 },
      tcd_warehouse: { ch1: 39.99, ch2: 42.99 },
      vip: { ch1: 29.99, ch2: 32.99 }
    },
    sales_data: {
      ch1: {
        csp_count: 89,
        monthly_sales: { jan: 34, feb: 41, mar: 29, apr: 47, may: 38, jun: 52 }
      },
      ch2: {
        csp_count: 72,
        monthly_sales: { jan: 28, feb: 33, mar: 25, apr: 39, may: 31, jun: 44 }
      }
    },
    weeks_on_hand: 15.3,
    turn_rate: 3.4,
    purchaser: 'Emily Chen',
    supplier: mockSuppliers[0],
    last_updated_by: 'Product Manager',
    date_created: '2024-01-08T12:00:00Z',
    last_updated_date: '2024-01-19T09:30:00Z',
    tags: [mockTags[1], mockTags[3]]
  },
  {
    id: 'pl_005',
    product_id: 'prod_005',
    product_name: 'Bluetooth Speaker',
    sku: 'BTS-005',
    classification: mockClassifications[3],
    current_stock: 34,
    cost: 45.00,
    pricing: {
      wholesale: { ch1: 69.99, ch2: 74.99 },
      value_tier: { ch1: 79.99, ch2: 84.99 },
      tier_1: { ch1: 89.99, ch2: 94.99 },
      tier_2: { ch1: 99.99, ch2: 104.99 },
      tier_pro: { ch1: 109.99, ch2: 114.99 },
      distributor: { ch1: 59.99, ch2: 64.99 },
      mvd_warehouse: { ch1: 64.99, ch2: 69.99 },
      tcd_warehouse: { ch1: 67.99, ch2: 72.99 },
      vip: { ch1: 54.99, ch2: 59.99 }
    },
    sales_data: {
      ch1: {
        csp_count: 143,
        monthly_sales: { jan: 56, feb: 48, mar: 63, apr: 71, may: 59, jun: 67 }
      },
      ch2: {
        csp_count: 118,
        monthly_sales: { jan: 42, feb: 38, mar: 51, apr: 58, may: 45, jun: 53 }
      }
    },
    weeks_on_hand: 6.8,
    turn_rate: 7.6,
    purchaser: 'David Wilson',
    supplier: mockSuppliers[0],
    last_updated_by: 'Sales Manager',
    date_created: '2024-01-12T15:20:00Z',
    last_updated_date: '2024-01-21T13:45:00Z',
    tags: [mockTags[0]]
  }
]

// Mock Orders Data
export const mockOrders: Order[] = [
  {
    id: 'ORD-1001',
    customer: 'John Doe',
    email: '<EMAIL>',
    date: '2023-06-17',
    total: '$570.50',
    status: 'Completed',
    source: 'Shopify',
    items: 3
  },
  {
    id: 'ORD-1002',
    customer: 'Jane Smith',
    email: '<EMAIL>',
    date: '2023-06-21',
    total: '$125.00',
    status: 'Processing',
    source: 'BigCommerce',
    items: 1
  },
  {
    id: 'ORD-1003',
    customer: 'Bob Johnson',
    email: '<EMAIL>',
    date: '2023-06-22',
    total: '$89.99',
    status: 'Shipped',
    source: 'Shopify',
    items: 2
  }
]

// Mock Customers Data
export const mockCustomers: Customer[] = [
  {
    id: 'cust_001',
    name: 'John Doe',
    email: '<EMAIL>',
    totalOrders: 12,
    totalSpent: '$1,245.67',
    lastOrder: '2023-06-17',
    source: 'Shopify'
  },
  {
    id: 'cust_002',
    name: 'Jane Smith',
    email: '<EMAIL>',
    totalOrders: 8,
    totalSpent: '$892.34',
    lastOrder: '2023-06-21',
    source: 'BigCommerce'
  },
  {
    id: 'cust_003',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    totalOrders: 15,
    totalSpent: '$2,156.89',
    lastOrder: '2023-06-22',
    source: 'Shopify'
  }
]

// Mock Job Logs
export const mockJobLogs: JobLog[] = [
  {
    id: 'log_001',
    jobId: '1',
    timestamp: '2023-06-22T14:30:00Z',
    level: 'success',
    message: 'Successfully synced 45 products from Shopify',
    details: { synced: 45, updated: 12, created: 3 }
  },
  {
    id: 'log_002',
    jobId: '1',
    timestamp: '2023-06-22T13:30:00Z',
    level: 'info',
    message: 'Starting product sync from Shopify',
    details: { batchSize: 100 }
  },
  {
    id: 'log_003',
    jobId: '2',
    timestamp: '2023-06-22T12:00:00Z',
    level: 'error',
    message: 'Failed to connect to BigCommerce API',
    details: { error: 'Invalid API credentials' }
  }
]

// Dashboard metrics
export const mockDashboardMetrics = {
  totalRevenue: {
    value: '$45,231.89',
    change: '+12.5%',
    trend: 'up' as const
  },
  orders: {
    value: '356',
    change: '****%',
    trend: 'up' as const
  },
  conversionRate: {
    value: '3.2%',
    change: '****%',
    trend: 'up' as const
  },
  products: {
    value: '1,423',
    change: '-2.3%',
    trend: 'down' as const
  }
}

// WhatsApp Mock Data Interfaces
export interface WhatsAppAccount {
  id: string
  name: string
  phone: string
  status: 'connected' | 'disconnected' | 'connecting'
  created_at: string
  updated_at: string
}

export interface WhatsAppContact {
  id: string
  account_id: string
  name: string
  phone: string
  profile_picture_url?: string
  is_business: boolean
  labels: string[]
  custom_fields: Record<string, any>
  created_at: string
  updated_at: string
}

export interface WhatsAppConversation {
  id: string
  account_id: string
  contact_id: string
  contact_name: string
  contact_phone: string
  is_pinned: boolean
  is_archived: boolean
  is_muted: boolean
  unread_count: number
  last_message_at: string
  created_at: string
  updated_at: string
}

export interface WhatsAppMessage {
  id: string
  conversation_id: string
  account_id: string
  contact_phone: string
  content: string
  type: 'text' | 'image' | 'video' | 'document' | 'audio' | 'contact'
  direction: 'inbound' | 'outbound'
  status: 'sent' | 'delivered' | 'read' | 'failed'
  timestamp: string
  created_at: string
}

// WhatsApp Mock Data
export const mockWhatsAppData = {
  accounts: [
    {
      id: 'acc_001',
      name: 'Business Account',
      phone: '+**********',
      status: 'connected' as const,
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-22T14:30:00Z'
    },
    {
      id: 'acc_002',
      name: 'Support Account',
      phone: '+**********',
      status: 'disconnected' as const,
      created_at: '2024-01-10T09:00:00Z',
      updated_at: '2024-01-20T16:45:00Z'
    }
  ] as WhatsAppAccount[],

  contacts: [
    {
      id: 'contact_001',
      account_id: 'acc_001',
      name: 'John Doe',
      phone: '+**********',
      profile_picture_url: 'https://example.com/avatar1.jpg',
      is_business: false,
      labels: ['customer', 'vip'],
      custom_fields: { company: 'Acme Corp', position: 'Manager' },
      created_at: '2024-01-15T11:00:00Z',
      updated_at: '2024-01-22T10:30:00Z'
    },
    {
      id: 'contact_002',
      account_id: 'acc_001',
      name: 'Jane Smith',
      phone: '+**********',
      profile_picture_url: 'https://example.com/avatar2.jpg',
      is_business: true,
      labels: ['business', 'partner'],
      custom_fields: { company: 'Tech Solutions', position: 'CEO' },
      created_at: '2024-01-16T14:20:00Z',
      updated_at: '2024-01-21T15:45:00Z'
    }
  ] as WhatsAppContact[],

  conversations: [
    {
      id: 'conv_001',
      account_id: 'acc_001',
      contact_id: 'contact_001',
      contact_name: 'John Doe',
      contact_phone: '+**********',
      is_pinned: true,
      is_archived: false,
      is_muted: false,
      unread_count: 2,
      last_message_at: '2024-01-22T14:30:00Z',
      created_at: '2024-01-15T11:00:00Z',
      updated_at: '2024-01-22T14:30:00Z'
    },
    {
      id: 'conv_002',
      account_id: 'acc_001',
      contact_id: 'contact_002',
      contact_name: 'Jane Smith',
      contact_phone: '+**********',
      is_pinned: false,
      is_archived: false,
      is_muted: true,
      unread_count: 0,
      last_message_at: '2024-01-21T15:45:00Z',
      created_at: '2024-01-16T14:20:00Z',
      updated_at: '2024-01-21T15:45:00Z'
    }
  ] as WhatsAppConversation[],

  messages: [
    {
      id: 'msg_001',
      conversation_id: 'conv_001',
      account_id: 'acc_001',
      contact_phone: '+**********',
      content: 'Hi, I have a question about your products',
      type: 'text' as const,
      direction: 'inbound' as const,
      status: 'read' as const,
      timestamp: '2024-01-22T14:25:00Z',
      created_at: '2024-01-22T14:25:00Z'
    },
    {
      id: 'msg_002',
      conversation_id: 'conv_001',
      account_id: 'acc_001',
      contact_phone: '+**********',
      content: 'Sure! I\'d be happy to help. What would you like to know?',
      type: 'text' as const,
      direction: 'outbound' as const,
      status: 'delivered' as const,
      timestamp: '2024-01-22T14:28:00Z',
      created_at: '2024-01-22T14:28:00Z'
    },
    {
      id: 'msg_003',
      conversation_id: 'conv_001',
      account_id: 'acc_001',
      contact_phone: '+**********',
      content: 'I\'m interested in your wireless charging pad',
      type: 'text' as const,
      direction: 'inbound' as const,
      status: 'read' as const,
      timestamp: '2024-01-22T14:30:00Z',
      created_at: '2024-01-22T14:30:00Z'
    }
  ] as WhatsAppMessage[]
}

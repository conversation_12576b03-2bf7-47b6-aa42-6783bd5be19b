export interface Customer {
  id: string
  name: string
  email: string
  phone: string
  company: string
  type: 'Individual' | 'Business' | 'Enterprise' | 'Government'
  status: 'Active' | 'Inactive' | 'Suspended' | 'Pending'
  tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'Diamond'
  credit_limit: number
  credit_used: number
  payment_terms: string
  tax_exempt: boolean
  date_created: string
  last_order_date: string
  total_orders: number
  total_spent: number
  lifetime_value: number
  average_order_value: number
  tags: string[]
  notes: string
  assigned_rep: string
  territory: string
  source: string
  website: string
  industry: string
  employee_count: number
  annual_revenue: number
  
  // Contact Information
  primary_contact: Contact
  billing_contact?: Contact
  shipping_contact?: Contact
  
  // Addresses
  billing_address: Address
  shipping_addresses: Address[]
  
  // Financial
  payment_methods: PaymentMethod[]
  credit_history: CreditHistory[]
  
  // Preferences
  communication_preferences: CommunicationPreferences
  order_preferences: OrderPreferences
  
  // Relationships
  parent_customer_id?: string
  child_customers: string[]
  
  // Metrics
  metrics: CustomerMetrics
  
  // New fields for extended customer information
  logo_url?: string
  timezone?: string
  social_links?: SocialLink[]
  outstanding_balance?: number
  last_payment_date?: string
  bank_details?: BankDetails
  invoicing_preferences?: InvoicingPreferences
  lifecycle_stage?: 'Lead' | 'Prospect' | 'Customer' | 'Evangelist' | 'Churned'
  support_tickets?: SupportTicket[]
  feedback_surveys?: FeedbackSurvey[]
  integration_status?: IntegrationStatus[]
  api_usage?: ApiUsage
  
  // Fields from Salesforce/CRM screenshot
  abuser?: boolean
  account_approved_date_time?: string
  account_closed_date_time?: string
  account_created_manually?: string // Picklist (could be 'Yes'/'No' or similar)
  account_manager_assigned_date_time?: string
  account_manager_division?: string
  account_manager_email?: string
  account_manager_mobile?: string
  
  // Comprehensive Salesforce/CRM fields
  account_number?: string
  account_owner_id?: string
  account_owner_name?: string
  account_owner_email?: string
  account_owner_phone?: string
  account_owner_username?: string
  account_owner_alias?: string
  account_record_type?: string
  active?: boolean
  business_registration_number?: string
  customer_priority?: string
  description?: string
  duns_number?: string
  fax?: string
  last_activity_date?: string
  last_modified_by?: string
  last_modified_date?: string
  ownership?: string
  rating?: string
  sic_code?: string
  site?: string
  ticker_symbol?: string
  billing_city?: string
  billing_country?: string
  billing_postal_code?: string
  billing_state?: string
  billing_street?: string
  shipping_city?: string
  shipping_country?: string
  shipping_postal_code?: string
  shipping_state?: string
  shipping_street?: string
  parent_account_id?: string
  parent_account_name?: string
  created_by?: string
  created_by_id?: string
  created_date?: string
  last_modified_by_id?: string
  last_modified_by_name?: string
  last_referenced_date?: string
  last_viewed_date?: string
  is_deleted?: boolean
  is_partner?: boolean
  is_customer_portal?: boolean
  is_prospect?: boolean
  is_vendor?: boolean
  is_active?: boolean
  annual_revenue_currency?: string
  account_site?: string
  account_type?: string
  account_status?: string
  account_source?: string
  account_rating?: string
  account_priority?: string
  account_ownership?: string
  account_industry?: string
  account_description?: string
  account_fax?: string
  account_website?: string
  account_phone?: string
  account_email?: string
  account_address?: string
  account_city?: string
  account_state?: string
  account_country?: string
  account_postal_code?: string
  account_street?: string
  account_ticker_symbol?: string
  account_sic_code?: string
  account_duns_number?: string
  account_business_registration_number?: string
  
  // Fields from screenshots
  payment_term?: string
  payment_term_approved_by?: string
  payment_term_approved_date_time?: string
  payment_term_notes?: string
  payment_term_issues?: string
  payment_term_risk?: string
  unpaid_balance?: number
  ach_echeck_approved?: boolean
  ach_echeck_approved_by?: string
  ach_echecked_approved_date_time?: string
  ach_form_received_uploaded?: boolean
  ach_form_uploaded_by?: string
  ach_form_received_uploaded_date?: string
  net_terms_received_and_uploaded?: boolean
  net_term_agreement_uploaded_by?: string
  net_terms_uploaded_date_time?: string
  customer_credit_card_fees_exempt?: boolean
  credit_card_fees_exempt_approved_by?: string
  credit_card_fees_exempt_approved_date?: string
  federal_tax_identification?: string
  read_agree_midwest_terms?: boolean
  are_you_a_business?: boolean
  how_did_you_hear_about_us?: string
  last_order_number?: string
  first_order_placed_date?: string
  total_spent_last_30_days?: number
  last_order_amount?: number
  order_payment_method?: string
  order_shipping_method?: string
  days_since_last_order?: number
  attempt1_date_capture?: string
  user_who_last_modified_notes?: string
  notes_last_modified_date_time?: string
  cash_and_carry_check_approved?: boolean
  check_approved_by?: string
  check_approved_date_time?: string
  check_approved_amount_limit?: number
  noted_for_check_cnc?: string
  does_customer_have_account_with_ctos?: boolean
  bc_customer_group?: string
  bc_group_name?: string
  bc_email?: string
  bc_store_credit?: number
  bc_customerid?: string
  bc_modified_time?: string
  cell_mobile_number?: string
  sms_consent?: boolean
  pricelist?: string
  main_contact_name?: string
  main_contact_phone?: string
  days_since_account_created?: number
  months_and_days?: string
  approved_date?: string
  approved_user?: string
  account_rejected_date_time?: string
  account_on_hold_date_time?: string
  usps_exempt_reminder_email?: boolean
  market_influencer?: string
  old_record?: boolean
  pre_rejected_customer?: boolean
  re_approved_date_time?: string
  coupon_code?: string
  customer_group?: string
  related_account?: string
  related_account_manager?: string
  company_google_search?: string
  first_call_date?: string
  last_call_date?: string
  joined?: string
  approval_path?: string
  state_licensed?: string
  authorized_state_license?: boolean
  rejected_closed_hold_reason?: string
  retailer_distributor?: string
  type_of_business?: string
  no_of_locations?: number
  how_many_customers_you_sell_to_monthly?: number
  estimated_monthly_purchasing?: number
  auto_create_account_at_cbdstore?: string
  do_you_want_to_be_tax_exempt?: boolean
  do_not_notify_the_customer?: boolean
  tax_exempt_state_list?: string
  price_leak_incident?: string
  customer_price_risk?: string
  no_activity_in_days?: number
  user_who_reject_closed_hold_this_account?: string
  customer_badge?: string
  state_website_verification_link?: string
  block_credit_card_payment?: boolean
  block_cash_and_carry_payment?: boolean
  ach_auth_temp?: boolean
  net_terms_auth_temp?: boolean
  morning_media_screen?: boolean
  date_installed?: string
}

export interface Contact {
  id: string
  name: string
  title: string
  email: string
  phone: string
  mobile?: string
  department?: string
  is_primary: boolean
  is_decision_maker: boolean
  communication_preferences: string[]
}

export interface Address {
  id: string
  type: 'billing' | 'shipping' | 'both'
  name: string
  company?: string
  address_line_1: string
  address_line_2?: string
  city: string
  state: string
  postal_code: string
  country: string
  is_default: boolean
  delivery_instructions?: string
  access_code?: string
}

export interface PaymentMethod {
  id: string
  type: 'credit_card' | 'bank_transfer' | 'check' | 'net_terms' | 'cash'
  name: string
  details: string
  is_default: boolean
  is_active: boolean
  expiry_date?: string
}

export interface CreditHistory {
  id: string
  date: string
  type: 'credit_check' | 'limit_increase' | 'limit_decrease' | 'payment_default'
  amount: number
  reason: string
  approved_by: string
  notes?: string
}

export interface CommunicationPreferences {
  email_notifications: boolean
  sms_notifications: boolean
  phone_calls: boolean
  postal_mail: boolean
  preferred_contact_time: string
  language: string
  frequency: 'immediate' | 'daily' | 'weekly' | 'monthly'
}

export interface OrderPreferences {
  preferred_shipping_method: string
  preferred_payment_method: string
  auto_reorder: boolean
  special_instructions: string
  requires_po_number: boolean
  approval_required: boolean
  price_list_id?: string
}

export interface CustomerMetrics {
  orders_last_30_days: number
  orders_last_90_days: number
  orders_last_year: number
  revenue_last_30_days: number
  revenue_last_90_days: number
  revenue_last_year: number
  average_days_between_orders: number
  return_rate: number
  satisfaction_score: number
  nps_score: number
  churn_risk: 'low' | 'medium' | 'high'
  engagement_score: number
}

export interface CustomerActivity {
  id: string
  customer_id: string
  type: 'order_placed' | 'payment_received' | 'contact_made' | 'quote_sent' | 'meeting_scheduled' | 'note_added' | 'status_changed'
  description: string
  date: string
  user: string
  metadata?: Record<string, any>
}

export interface CustomerNote {
  id: string
  customer_id: string
  content: string
  type: 'general' | 'sales' | 'support' | 'billing' | 'internal'
  visibility: 'public' | 'internal'
  created_by: string
  created_at: string
  updated_at?: string
  tags: string[]
}

export interface CustomerDocument {
  id: string
  customer_id: string
  name: string
  type: 'contract' | 'agreement' | 'certificate' | 'license' | 'other'
  file_url: string
  file_size: number
  uploaded_by: string
  uploaded_at: string
  expiry_date?: string
  status: 'active' | 'expired' | 'pending_renewal'
}

export interface CustomerFilters {
  search?: string
  status?: string[]
  type?: string[]
  tier?: string[]
  territory?: string[]
  assigned_rep?: string[]
  tags?: string[]
  credit_status?: string[]
  last_order_from?: string
  last_order_to?: string
  total_spent_min?: number
  total_spent_max?: number
  date_created_from?: string
  date_created_to?: string
}

export interface CustomerListResponse {
  customers: Customer[]
  total: number
  page: number
  per_page: number
  total_pages: number
}

// New supporting types
export interface SocialLink {
  type: 'LinkedIn' | 'Twitter' | 'Facebook' | 'Instagram' | 'Other'
  url: string
}

export interface BankDetails {
  account_name: string
  account_number: string
  bank_name: string
  ifsc_code?: string
  swift_code?: string
}

export interface InvoicingPreferences {
  method: 'email' | 'paper'
  email?: string
  address?: string
}

export interface SupportTicket {
  id: string
  subject: string
  status: 'open' | 'closed' | 'pending'
  created_at: string
  updated_at?: string
}

export interface FeedbackSurvey {
  id: string
  score: number
  comment?: string
  date: string
}

export interface IntegrationStatus {
  name: string
  status: 'connected' | 'disconnected' | 'error'
  last_sync?: string
}

export interface ApiUsage {
  requests_last_30_days: number
  requests_total: number
}

// Order-related interfaces
export interface Order {
  id: string
  order_number: string
  customer_id: string
  customer_name: string
  status: 'Pending' | 'Confirmed' | 'Processing' | 'Shipped' | 'Delivered' | 'Cancelled' | 'Returned' | 'Refunded'
  order_date: string
  required_date?: string
  shipped_date?: string
  delivered_date?: string
  cancelled_date?: string
  total_amount: number
  tax_amount: number
  shipping_amount: number
  discount_amount: number
  subtotal: number
  currency: string
  payment_method: string
  payment_status: 'Pending' | 'Paid' | 'Partially Paid' | 'Failed' | 'Refunded'
  shipping_method: string
  shipping_address: Address
  billing_address: Address
  items: OrderItem[]
  notes?: string
  internal_notes?: string
  po_number?: string
  tracking_number?: string
  carrier?: string
  estimated_delivery?: string
  actual_delivery?: string
  created_by: string
  created_at: string
  updated_at?: string
  updated_by?: string
  tags: string[]
  priority: 'Low' | 'Normal' | 'High' | 'Urgent'
  source: 'Web' | 'Phone' | 'Email' | 'In-Person' | 'API'
  approval_required: boolean
  approved_by?: string
  approved_at?: string
  risk_level: 'Low' | 'Medium' | 'High'
  fraud_score?: number
  customer_rating?: number
  customer_feedback?: string
}

export interface OrderItem {
  id: string
  order_id: string
  product_id: string
  product_name: string
  product_sku: string
  product_category: string
  quantity: number
  unit_price: number
  total_price: number
  discount_amount: number
  tax_amount: number
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  status: 'Pending' | 'Confirmed' | 'Processing' | 'Shipped' | 'Delivered' | 'Cancelled' | 'Returned'
  backordered: boolean
  backorder_quantity?: number
  expected_backorder_date?: string
  notes?: string
  custom_fields?: Record<string, any>
}

export interface OrderFilters {
  search?: string
  status?: string[]
  payment_status?: string[]
  date_from?: string
  date_to?: string
  amount_min?: number
  amount_max?: number
  payment_method?: string[]
  shipping_method?: string[]
  priority?: string[]
  source?: string[]
  tags?: string[]
}

export interface OrderListResponse {
  orders: Order[]
  total: number
  page: number
  per_page: number
  total_pages: number
}

import React, { useState } from 'react'
import {
  Users,
  Search,
  Filter,
  Download,
  Upload,
  Plus,
  Eye,
  Edit,
  Trash2,
  Check,
  X,
  Clock,
  AlertCircle,
  Building2,
  Package,
  DollarSign,
  Star,
  MoreHorizontal,
  RefreshCw,
  Settings,
  Mail,
  Phone,
  Globe,
  MapPin,
  TrendingUp,
  BarChart3,
  Shield,
  Key,
  Network
} from 'lucide-react'
import StatCard from '../components/StatCard'
import SectionHeader from '../components/SectionHeader'

const VendorNetworkPage = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedTier, setSelectedTier] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedVendor, setSelectedVendor] = useState<any>(null)

  // Mock data for vendors (from Guest Organization distributors)
  const mockVendors = [
    {
      id: 'vendor_1',
      organization_name: 'TechCorp Solutions',
      contact_person: {
        name: '<PERSON>',
        title: 'Partnership Manager',
        email: '<EMAIL>',
        phone: '+****************'
      },
      product_share_count: 156,
      price_list: 'Distributor Pricing',
      last_sync: '2024-01-15T10:30:00Z',
      status: 'active',
      user_type: 'distributor',
      guest_org_id: 'guest_org_1'
    },
    {
      id: 'vendor_2',
      organization_name: 'HealthTech Inc',
      contact_person: {
        name: 'Sarah Johnson',
        title: 'Business Development',
        email: '<EMAIL>',
        phone: '+****************'
      },
      product_share_count: 89,
      price_list: 'Standard Pricing',
      last_sync: '2024-01-14T15:45:00Z',
      status: 'active',
      user_type: 'distributor',
      guest_org_id: 'guest_org_2'
    },
    {
      id: 'vendor_3',
      organization_name: 'EcoFashion Co',
      contact_person: {
        name: 'Mike Green',
        title: 'Founder',
        email: '<EMAIL>',
        phone: '+****************'
      },
      product_share_count: 234,
      price_list: 'Wholesale Pricing',
      last_sync: null,
      status: 'pending',
      user_type: 'distributor',
      guest_org_id: 'guest_org_3'
    },
    {
      id: 'vendor_4',
      organization_name: 'PhotoGear Ltd',
      contact_person: {
        name: 'Lisa Chen',
        title: 'Operations Manager',
        email: '<EMAIL>',
        phone: '+****************'
      },
      product_share_count: 67,
      price_list: 'Premium Pricing',
      last_sync: '2024-01-10T08:20:00Z',
      status: 'suspended',
      user_type: 'distributor',
      guest_org_id: 'guest_org_4'
    }
  ]

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: Check, text: 'Active' },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pending' },
      suspended: { color: 'bg-red-100 text-red-800', icon: X, text: 'Suspended' },
      inactive: { color: 'bg-gray-100 text-gray-800', icon: AlertCircle, text: 'Inactive' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig]
    const Icon = config.icon
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const handleEditVendor = (vendor: any) => {
    setSelectedVendor(vendor)
    setShowEditModal(true)
  }

  const handleDeleteVendor = (vendorId: string) => {
    console.log('Deleting vendor:', vendorId)
    // Here you would implement the actual delete logic
  }

  const filteredVendors = mockVendors.filter(vendor => {
    const matchesSearch = vendor.organization_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.contact_person.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !selectedStatus || vendor.status === selectedStatus
    const matchesTier = !selectedTier || vendor.user_type === selectedTier

    return matchesSearch && matchesStatus && matchesTier
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<Network className="h-7 w-7" />}
        title="Vendor Network"
        subtitle="Manage your vendor partnerships and connections"
        actions={
          <>
            <button className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700">
              <RefreshCw className="h-4 w-4 mr-2 inline" />
              Sync All
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2 inline" />
              Add Vendor
            </button>
          </>
        }
      />

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="Total Vendors"
          value={mockVendors.length}
          subtitle="Registered distributors"
          icon={Users}
          iconColor="text-purple-600"
          trend={{
            value: "2",
            isPositive: true,
            period: "last quarter"
          }}
        />
        <StatCard
          title="Active Vendors"
          value={mockVendors.filter(v => v.status === 'active').length}
          subtitle="Currently active"
          icon={Check}
          iconColor="text-green-600"
          trend={{
            value: "100%",
            isPositive: true,
            period: "uptime"
          }}
        />
        <StatCard
          title="Total Revenue"
          value="$18,677"
          subtitle="Generated revenue"
          icon={DollarSign}
          iconColor="text-blue-600"
          trend={{
            value: "12.5%",
            isPositive: true,
            period: "last month"
          }}
        />
        <StatCard
          title="Products Shared"
          value={mockVendors.reduce((sum, v) => sum + v.product_share_count, 0)}
          subtitle="Total shared items"
          icon={Package}
          iconColor="text-orange-600"
          trend={{
            value: "45",
            isPositive: true,
            period: "last 30 days"
          }}
        />
      </div>

      {/* Filters */}
      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search vendors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input pl-10 w-full"
            />
          </div>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="pending">Pending</option>
            <option value="suspended">Suspended</option>
            <option value="inactive">Inactive</option>
          </select>
          <select
            value={selectedTier}
            onChange={(e) => setSelectedTier(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="">All Types</option>
            <option value="distributor">Distributor</option>
          </select>
          <div className="flex space-x-2">
            <button className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
            <button className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </button>
          </div>
        </div>
      </div>

      {/* Vendors Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Share</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price List</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Sync</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredVendors.map((vendor) => (
                <tr key={vendor.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                          <Building2 className="h-5 w-5 text-gray-500" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{vendor.organization_name}</div>
                        <div className="text-sm text-gray-500">{getStatusBadge(vendor.status)}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">{vendor.contact_person.name}</div>
                      <div className="text-xs text-gray-500">{vendor.contact_person.title}</div>
                      <div className="flex items-center mt-1">
                        <Mail className="h-3 w-3 text-gray-400 mr-1" />
                        <span className="text-xs text-gray-500">{vendor.contact_person.email}</span>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center">
                        <Package className="h-4 w-4 text-blue-500 mr-2" />
                        <span className="font-medium">{vendor.product_share_count}</span>
                      </div>
                      <div className="text-xs text-gray-500">Products shared</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="font-medium">{vendor.price_list}</div>
                      <div className="text-xs text-gray-500">Active pricing</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {vendor.last_sync ? (
                        <div>
                          <div className="text-sm text-gray-900">
                            {new Date(vendor.last_sync).toLocaleDateString()}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(vendor.last_sync).toLocaleTimeString()}
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-500">Never synced</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="text-gray-400 hover:text-gray-600">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEditVendor(vendor)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <Settings className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteVendor(vendor.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Vendor Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Vendor</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                <input type="text" className="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input type="email" className="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                <input type="tel" className="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                <input type="url" className="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <textarea className="w-full px-3 py-2 border border-gray-300 rounded-md" rows={3}></textarea>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tier</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option value="basic">Basic</option>
                  <option value="standard">Standard</option>
                  <option value="premium">Premium</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Commission Rate (%)</label>
                <input type="number" className="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Add Vendor
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default VendorNetworkPage

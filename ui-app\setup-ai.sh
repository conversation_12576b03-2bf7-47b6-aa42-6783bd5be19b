#!/bin/bash

# Setup script for AI integration
echo "🤖 Setting up AI integration for Nuclues Chat..."

# Create .env file with OpenAI API key
cat > .env << EOF
# AI Service API Keys
VITE_OPENAI_API_KEY=********************************************************************************************************************************************************************

# Backend API URL
VITE_API_URL=http://localhost:3000
EOF

echo "✅ .env file created with your OpenAI API key"
echo "🔄 Restarting development server..."

# Kill existing vite process
pkill -f "vite" 2>/dev/null || true

# Start development server
npm run dev &

echo "🚀 Development server started!"
echo "📝 You can now test the AI chat functionality"
echo "💡 Try asking questions in the chat bubble" 
import { MediaItem, CreateMediaItemRequest, UpdateMediaItemRequest, MediaSearchRequest, MediaShareRequest } from '../types/media';

const API_BASE_URL = '/api/media';

export const mediaApi = {
  // Get media items
  async getItems(params?: MediaSearchRequest): Promise<MediaItem[]> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/items?${queryParams}`);
    if (!response.ok) {
      throw new Error('Failed to fetch media items');
    }
    return response.json();
  },

  // Get single media item
  async getItem(id: string): Promise<MediaItem> {
    const response = await fetch(`${API_BASE_URL}/items/${id}`);
    if (!response.ok) {
      throw new Error('Failed to fetch media item');
    }
    return response.json();
  },

  // Create folder
  async createFolder(request: CreateMediaItemRequest): Promise<MediaItem> {
    const response = await fetch(`${API_BASE_URL}/folders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    if (!response.ok) {
      throw new Error('Failed to create folder');
    }
    return response.json();
  },

  // Create file record (for upload)
  async createFile(request: CreateMediaItemRequest): Promise<MediaItem> {
    const response = await fetch(`${API_BASE_URL}/files`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    if (!response.ok) {
      throw new Error('Failed to create file record');
    }
    return response.json();
  },

  // Upload file content
  async uploadFile(id: string, file: File): Promise<void> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('mime_type', file.type);

    const response = await fetch(`${API_BASE_URL}/items/${id}/upload`, {
      method: 'POST',
      body: formData,
    });
    if (!response.ok) {
      throw new Error('Failed to upload file');
    }
  },

  // Update media item
  async updateItem(id: string, request: UpdateMediaItemRequest): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/items/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    if (!response.ok) {
      throw new Error('Failed to update media item');
    }
  },

  // Delete media item
  async deleteItem(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/items/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error('Failed to delete media item');
    }
  },

  // Share media item
  async shareItem(id: string, request: MediaShareRequest): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/items/${id}/share`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    if (!response.ok) {
      throw new Error('Failed to share media item');
    }
  },

  // Get download URL
  getDownloadUrl(id: string): string {
    return `${API_BASE_URL}/items/${id}/download`;
  },

  // Get thumbnail URL
  getThumbnailUrl(id: string): string {
    return `${API_BASE_URL}/items/${id}/thumbnail`;
  },

  // Upload multiple files
  async uploadMultipleFiles(files: File[], parentId?: string): Promise<MediaItem[]> {
    const uploadPromises = files.map(async (file) => {
      // Create file record
      const fileRecord = await this.createFile({
        name: file.name,
        item_type: 'file',
        parent_id: parentId,
      });

      // Upload file content
      await this.uploadFile(fileRecord.id, file);

      return fileRecord;
    });

    return Promise.all(uploadPromises);
  },

  // Search media items
  async searchItems(query: string, filters?: Partial<MediaSearchRequest>): Promise<MediaItem[]> {
    return this.getItems({
      query,
      ...filters,
    });
  },

  // Get shared items
  async getSharedItems(): Promise<MediaItem[]> {
    return this.getItems({ is_shared: true });
  },

  // Get items by type
  async getItemsByType(type: 'file' | 'folder', parentId?: string): Promise<MediaItem[]> {
    return this.getItems({ item_type: type, parent_id: parentId });
  },
}; 
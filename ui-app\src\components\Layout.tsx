import { ReactNode, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  Database,
  Settings,
  Menu,
  X,
  ShoppingCart,
  Package,
  Layers,
  ChevronDown,
  ChevronRight,
  Users,
  Store,
  BarChart2,
  Image,
  <PERSON>ppW<PERSON>ow,
  <PERSON>lug,
  Bo<PERSON>
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import WorkspaceSwitcher from './WorkspaceSwitcher'
import PermissionGate from './PermissionGate'
import FontSizeDropdown from './FontSizeDropdown'
import ThemeToggle from './ThemeToggle'
import NotificationsDropdown from './NotificationsDropdown'
import GlobalSearch from './GlobalSearch'
import UserProfileDropdown from './UserProfileDropdown'
import { RESOURCES, ACTIONS } from '../types/auth'

interface LayoutProps {
  children: ReactNode
  collapseSidebar?: boolean
}

interface NavigationItem {
  name: string
  href?: string
  icon?: any
  permission?: { resource: string; action: string }
  type?: string
  children?: NavigationItem[]
}

const Layout = ({ children, collapseSidebar = false }: LayoutProps) => {
  const location = useLocation()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState<string[]>(['Products'])
  const [isHoveringCollapsedSidebar, setIsHoveringCollapsedSidebar] = useState(false)
  const { isAuthenticated, isLoading, login, error, clearError } = useAuth()
  const [loginForm, setLoginForm] = useState({ email: '', password: '' })
  const [isLoggingIn, setIsLoggingIn] = useState(false)

  // Check if we're on the Settings page that should auto-collapse the sidebar
  const isSettingsPage = () => {
    return location.pathname.startsWith('/settings')
  }

  // Auto-collapse sidebar only for Settings pages or if collapseSidebar is true
  const shouldCollapseSidebar = isSettingsPage() || collapseSidebar

  // Determine if sidebar should be expanded (either not collapsed, or collapsed but being hovered)
  const isSidebarExpanded = !shouldCollapseSidebar || isHoveringCollapsedSidebar

  // Handle login
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoggingIn(true)
    try {
      await login(loginForm.email, loginForm.password)
    } catch (err) {
      // Error is handled by the auth context
    } finally {
      setIsLoggingIn(false)
    }
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Show login form if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full space-y-8">
          <div>
            <div className="flex justify-center">
              <Database className="h-12 w-12 text-blue-600" />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Sign in to Nuclues
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Multi-space integration platform
            </p>
          </div>
          <form className="mt-8 space-y-6" onSubmit={handleLogin}>
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Authentication Error
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      {error}
                    </div>
                    <div className="mt-3">
                      <button
                        type="button"
                        onClick={clearError}
                        className="text-sm text-red-800 underline hover:text-red-900"
                      >
                        Dismiss
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={loginForm.email}
                  onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                  className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={loginForm.password}
                  onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                  className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoggingIn}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoggingIn ? 'Signing in...' : 'Sign in'}
              </button>
            </div>

            <div className="text-center">
              <div className="text-sm text-gray-600">
                Demo credentials:
              </div>
              <div className="text-xs text-gray-500 mt-1">
                <div><EMAIL> / password</div>
                <div><EMAIL> / password</div>
                <div><EMAIL> / password</div>
              </div>
            </div>
          </form>
        </div>
      </div>
    )
  }

  const navigation: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/',
      icon: Home,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
    },
    {
      type: 'separator',
      name: 'Ecommerce'
    },
    // 1. Orders
    {
      name: 'Orders',
      href: '/orders',
      icon: ShoppingCart,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
    },
    // 2. Products
    {
      name: 'Products',
      icon: Package,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ },
      children: [
        {
          name: 'All Products',
          href: '/products/all',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        },
        {
          name: 'Price Lists',
          href: '/products/price-lists',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        }
      ]
    },
    // 3. Customers
    {
      name: 'Customers',
      href: '/customers',
      icon: Users,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
    },
    // 4. Storefront
    {
      name: 'Storefront',
      href: '/storefront',
      icon: Store,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
    },
    // 5. Sales
    {
      name: 'Sales',
      href: '/sales',
      icon: BarChart2,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
    },
    // 6. PIM
    {
      name: 'PIM',
      icon: Layers,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ },
      children: [
        {
          name: 'Products',
          href: '/pim/products/all',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        },
        {
          name: 'Categories',
          href: '/categories',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        },
        {
          name: 'Brands',
          href: '/brands',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        },
        {
          name: 'Markup',
          href: '/pim/markup',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        },
        {
          name: 'Vendor Network',
          href: '/products/shared/vendors',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        },
        {
          name: 'Inbound Products',
          href: '/pim/inbound-products',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        },
        {
          name: 'Outbound Products',
          href: '/pim/outbound-products',
          permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
        },
      ]
    },
    // 7. Media
    {
      name: 'Media',
      href: '/media',
      icon: Image,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
    },
    // Rest as it is
    {
      name: 'NucleusIQ',
      href: '/nucleusiq',
      icon: Bot,
      permission: { resource: RESOURCES.DATA, action: ACTIONS.READ }
    },
    {
      name: 'Apps',
      icon: AppWindow,
      permission: { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ },
      children: [
        {
          name: 'All Apps',
          href: '/apps',
          permission: { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ }
        },
        {
          name: 'WhatsApp',
          href: '/apps/whatsapp',
          permission: { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ }
        }
      ]
    },
    {
      name: 'Integrations',
      icon: Plug,
      permission: { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ },
      children: [
        {
          name: 'Dashboard',
          href: '/integrations/dashboard',
          permission: { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ }
        },
        {
          name: 'Connections',
          href: '/integrations/connections',
          permission: { resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ }
        },
      ]
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      permission: { resource: RESOURCES.SETTINGS, action: ACTIONS.READ }
    },
  ]

  const isActiveRoute = (href?: string) => {
    if (!href) return false

    if (href === '/') {
      return location.pathname === '/'
    }

    // For Settings, check if any settings route is active
    if (href === '/settings') {
      return location.pathname.startsWith('/settings')
    }

    return location.pathname.startsWith(href)
  }

  const isMenuExpanded = (menuName: string) => {
    return expandedMenus.includes(menuName)
  }

  const toggleMenu = (menuName: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuName)
        ? prev.filter(name => name !== menuName)
        : [...prev, menuName]
    )
  }

  const hasActiveChild = (children?: NavigationItem[]) => {
    if (!children) return false
    return children.some(child => child.href && isActiveRoute(child.href))
  }

  // Unified color logic for sidebar icons and text
  const getMenuItemClasses = (isActive: boolean) =>
    isActive
      ? 'text-blue-600 font-semibold'
      : 'text-gray-700';
  const getMenuIconClasses = (isActive: boolean) =>
    isActive
      ? 'text-blue-600'
      : 'text-gray-400';

  return (
    <div className="min-h-screen bg-white flex">
      {/* Sidebar - Always fixed, visible, and collapsible */}
      <div
        className={`fixed top-0 left-0 h-screen z-50 bg-white border-r border-gray-200 transition-all duration-300 ease-in-out ${
          shouldCollapseSidebar
            ? isSidebarExpanded ? 'w-[260px]' : 'w-16'
            : 'w-[260px]'
        }`}
        style={{ width: shouldCollapseSidebar ? (isSidebarExpanded ? 260 : 64) : 260 }}
        onMouseEnter={() => shouldCollapseSidebar && setIsHoveringCollapsedSidebar(true)}
        onMouseLeave={() => shouldCollapseSidebar && setIsHoveringCollapsedSidebar(false)}
      >
        {/* Logo */}
        <div className={`flex items-center h-16 border-b border-gray-200 bg-white ${
          shouldCollapseSidebar && !isSidebarExpanded ? 'justify-center px-2' : 'justify-between px-6'
        }`}>
          <div className="flex items-center">
            <Database className="h-8 w-8 text-blue-600" />
            {(!shouldCollapseSidebar || isSidebarExpanded) && (
              <span className={`ml-2 text-xl font-bold text-gray-900 transition-opacity duration-200 ${
                shouldCollapseSidebar && isSidebarExpanded ? 'animate-in fade-in-0 duration-300' : ''
              }`}>
                Nuclues
              </span>
            )}
          </div>
          {(!shouldCollapseSidebar || isSidebarExpanded) && (
            <button
              onClick={() => setSidebarOpen(false)}
              className={`lg:hidden text-gray-500 hover:text-gray-700 transition-opacity duration-200 ${
                shouldCollapseSidebar && isSidebarExpanded ? 'animate-in fade-in-0 duration-300' : ''
              }`}
            >
              <X className="h-6 w-6" />
            </button>
          )}
        </div>

        {/* Navigation - scrollable */}
        <div className="flex-1 overflow-y-auto h-[calc(100vh-4rem)]">
          <nav className="mt-6 px-3">
            <div className="space-y-1">
              {navigation.map((item, index) => {
                // Handle separator items
                if (item.type === 'separator') {
                  return (
                    <div key={`separator-${index}`} className="pt-4 pb-2">
                      {(!shouldCollapseSidebar || isSidebarExpanded) && (
                        <div className={`px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider transition-opacity duration-200 ${
                          shouldCollapseSidebar && isSidebarExpanded ? 'animate-in fade-in-0 duration-300' : ''
                        }`}>
                          {item.name}
                        </div>
                      )}
                      {(shouldCollapseSidebar && !isSidebarExpanded) && (
                        <div className="border-t border-gray-200 mx-2"></div>
                      )}
                    </div>
                  )
                }

                // Handle navigation items with children (submenus)
                if (item.children && item.children.length > 0) {
                  const Icon = item.icon
                  const isExpanded = isMenuExpanded(item.name)
                  const hasActive = hasActiveChild(item.children)

                  return (
                    <PermissionGate
                      key={item.name}
                      resource={item.permission?.resource}
                      action={item.permission?.action}
                    >
                      <div>
                        {/* Parent menu item */}
                        <button
                          onClick={() => toggleMenu(item.name)}
                          className={`group flex items-center w-full text-sm font-medium rounded-lg transition-colors duration-200 px-3 py-2 ${
                            hasActive
                              ? 'text-gray-900'
                              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                          }`}
                          title={shouldCollapseSidebar && !isSidebarExpanded ? item.name : undefined}
                        >
                          {/* Icon container with fixed positioning */}
                          <div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
                            <Icon className={`h-5 w-5 ${getMenuIconClasses(hasActive)}`} />
                          </div>

                          {/* Text content with consistent spacing */}
                          {(!shouldCollapseSidebar || isSidebarExpanded) && (
                            <div className="flex items-center justify-between w-full ml-3 overflow-hidden min-w-0">
                              <span className={getMenuItemClasses(hasActive)}>
                                {item.name}
                              </span>
                              <div className="flex-shrink-0 ml-2">
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4 text-gray-400" />
                                ) : (
                                  <ChevronRight className="h-4 w-4 text-gray-400" />
                                )}
                              </div>
                            </div>
                          )}
                        </button>

                        {/* Submenu items */}
                        {isExpanded && (!shouldCollapseSidebar || isSidebarExpanded) && (
                          <div className="ml-8 mt-1 space-y-1">
                            {item.children.map((child) => {
                              // Handle nested submenu items (like Shared Products)
                              if (child.children && child.children.length > 0) {
                                const isChildExpanded = isMenuExpanded(child.name)
                                const hasChildActive = hasActiveChild(child.children)
                                const ChildIcon = child.icon

                                return (
                                  <PermissionGate
                                    key={child.name}
                                    resource={child.permission?.resource}
                                    action={child.permission?.action}
                                  >
                                    <div>
                                      <button
                                        onClick={() => toggleMenu(child.name)}
                                        className={`group flex items-center w-full text-sm font-medium rounded-lg transition-colors duration-200 px-3 py-2 ${
                                          hasChildActive
                                            ? 'text-gray-900 bg-gray-50'
                                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                                        }`}
                                      >
                                        {ChildIcon && (
                                          <div className="w-4 h-4 flex items-center justify-center flex-shrink-0 mr-3">
                                            <ChildIcon className={`h-4 w-4 ${getMenuIconClasses(hasChildActive)}`} />
                                          </div>
                                        )}
                                        <span className={getMenuItemClasses(hasChildActive)}>
                                          {child.name}
                                        </span>
                                        <div className="flex-shrink-0 ml-2">
                                          {isChildExpanded ? (
                                            <ChevronDown className="h-3 w-3 text-gray-400" />
                                          ) : (
                                            <ChevronRight className="h-3 w-3 text-gray-400" />
                                          )}
                                        </div>
                                      </button>

                                      {/* Nested submenu items */}
                                      {isChildExpanded && (
                                        <div className="ml-6 mt-1 space-y-1">
                                          {child.children.map((grandchild) => {
                                            if (!grandchild.href) return null

                                            const isGrandchildActive = isActiveRoute(grandchild.href)
                                            const GrandchildIcon = grandchild.icon

                                            return (
                                              <PermissionGate
                                                key={grandchild.name}
                                                resource={grandchild.permission?.resource}
                                                action={grandchild.permission?.action}
                                              >
                                                <Link
                                                  to={grandchild.href}
                                                  className={`group flex items-center text-sm font-medium rounded-lg transition-colors duration-200 px-3 py-2 ${
                                                    isGrandchildActive
                                                      ? 'text-gray-900 bg-gray-100'
                                                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                                                  }`}
                                                >
                                                  {GrandchildIcon && (
                                                    <div className="w-3 h-3 flex items-center justify-center flex-shrink-0 mr-3">
                                                      <GrandchildIcon className={`h-3 w-3 ${getMenuIconClasses(isGrandchildActive)}`} />
                                                    </div>
                                                  )}
                                                  <span className={getMenuItemClasses(isGrandchildActive)}>
                                                    {grandchild.name}
                                                  </span>
                                                </Link>
                                              </PermissionGate>
                                            )
                                          })}
                                        </div>
                                      )}
                                    </div>
                                  </PermissionGate>
                                )
                              }

                              // Handle regular submenu items
                              if (!child.href) return null

                              const isChildActive = isActiveRoute(child.href)

                              return (
                                <PermissionGate
                                  key={child.name}
                                  resource={child.permission?.resource}
                                  action={child.permission?.action}
                                >
                                  <Link
                                    to={child.href}
                                    className={`group flex items-center text-sm font-medium rounded-lg transition-colors duration-200 px-3 py-2 ${
                                      isChildActive
                                        ? 'text-gray-900 bg-gray-50'
                                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                                    }`}
                                  >
                                    <span className={getMenuItemClasses(isChildActive)}>
                                      {child.name}
                                    </span>
                                  </Link>
                                </PermissionGate>
                              )
                            })}
                          </div>
                        )}
                      </div>
                    </PermissionGate>
                  )
                }

                // Handle regular navigation items without children
                if (!item.icon || !item.href) return null

                const Icon = item.icon
                const isActive = isActiveRoute(item.href)

                return (
                  <PermissionGate
                    key={item.name}
                    resource={item.permission?.resource}
                    action={item.permission?.action}
                  >
                    <div>
                      <Link
                        to={item.href}
                        className={`group flex items-center text-sm font-medium rounded-lg transition-colors duration-200 px-3 py-2 ${
                          isActive
                            ? 'text-gray-900'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                        }`}
                        title={shouldCollapseSidebar && !isSidebarExpanded ? item.name : undefined}
                      >
                        {/* Icon container with fixed positioning */}
                        <div className="w-5 h-5 flex items-center justify-center flex-shrink-0">
                          <Icon className={`h-5 w-5 ${getMenuIconClasses(isActive)}`} />
                        </div>

                        {/* Text content with consistent spacing */}
                        {(!shouldCollapseSidebar || isSidebarExpanded) && (
                          <div className="flex items-center justify-between w-full ml-3 overflow-hidden min-w-0">
                            <span className={getMenuItemClasses(isActive)}>
                              {item.name}
                            </span>
                          </div>
                        )}
                      </Link>
                    </div>
                  </PermissionGate>
                )
              })}
            </div>
          </nav>
        </div>

        {/* Bottom section - Mobile Workspace Switcher */}
        {!shouldCollapseSidebar && (
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-100 lg:hidden">
            <WorkspaceSwitcher />
          </div>
        )}
      </div>

      {/* Main content area */}
      <div
        className="flex-1 flex flex-col min-h-screen"
        style={{
          paddingLeft: shouldCollapseSidebar ? (isSidebarExpanded ? 260 : 64) : 260
        }}
      >
        {/* Top header - fixed with left offset and width */}
        <header
          className="fixed top-0 z-40 bg-white shadow-sm border-b border-gray-200"
          style={{
            left: shouldCollapseSidebar ? (isSidebarExpanded ? 260 : 64) : 260,
            width: `calc(100vw - ${shouldCollapseSidebar ? (isSidebarExpanded ? 260 : 64) : 260}px)`,
            height: 64
          }}
        >
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="relative flex justify-between h-16">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden text-gray-500 hover:text-gray-700"
                >
                  <Menu className="h-6 w-6" />
                </button>

                {/* Workspace Switcher - Positioned to align with page content */}
                <div className={shouldCollapseSidebar ? 'block -ml-12' : 'hidden lg:block'}>
                  <WorkspaceSwitcher />
                </div>

                {/* Global Search */}
                <GlobalSearch />
              </div>

              <div className="flex items-center space-x-6">
                {/* Font Size Dropdown */}
                <FontSizeDropdown />

                {/* Theme Toggle */}
                <ThemeToggle />

                {/* Notifications */}
                <NotificationsDropdown />

                {/* User Profile */}
                <UserProfileDropdown />
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 bg-gray-50 overflow-x-hidden" style={{ paddingTop: 64 }}>
          <div className="p-6 max-w-full">
            {children}
          </div>
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}

export default Layout

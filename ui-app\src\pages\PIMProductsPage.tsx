import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Package, Plus, Search, Filter, Download, RefreshCw, Eye, Edit, Trash2, MoreHorizontal, ChevronDown, ExternalLink, Copy, Archive, X, Star } from 'lucide-react';
import SectionHeader from '../components/SectionHeader';
import ColumnSettings from '../components/ColumnSettings';
import CustomSelect from '../components/CustomSelect';
import { ProductListItem } from '../types/product';

const mockProducts: ProductListItem[] = [
  {
    id: '1',
    name: 'Wireless Bluetooth Headphones Pro',
    sku: 'WBH-PRO-001',
    category: 'Electronics',
    price: 199.99,
    inventory_level: 45,
    is_visible: true,
    is_featured: true,
    sync_status: 'synced',
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:30:00Z',
    brand: 'AudioTech'
  },
  {
    id: '2',
    name: 'Smart Fitness Watch Series 5',
    sku: 'SFW-S5-002',
    category: 'Electronics',
    price: 299.99,
    inventory_level: 23,
    is_visible: true,
    is_featured: false,
    sync_status: 'pending',
    created_at: '2024-01-14T15:45:00Z',
    updated_at: '2024-01-14T15:45:00Z',
    brand: 'FitTech'
  },
  {
    id: '3',
    name: 'Organic Cotton Premium T-Shirt',
    sku: 'OCT-PREM-003',
    category: 'Clothing',
    price: 39.99,
    inventory_level: 0,
    is_visible: false,
    is_featured: false,
    sync_status: 'error',
    created_at: '2024-01-13T09:20:00Z',
    updated_at: '2024-01-13T09:20:00Z',
    brand: 'EcoWear'
  },
  {
    id: '4',
    name: 'Stainless Steel Water Bottle 32oz',
    sku: 'SSWB-32-004',
    category: 'Home & Garden',
    price: 24.99,
    inventory_level: 156,
    is_visible: true,
    is_featured: true,
    sync_status: 'synced',
    created_at: '2024-01-12T14:10:00Z',
    updated_at: '2024-01-12T14:10:00Z',
    brand: 'HydroLife'
  },
  {
    id: '5',
    name: 'Wireless Charging Pad Fast Charge',
    sku: 'WCP-FC-005',
    category: 'Electronics',
    price: 49.99,
    inventory_level: 78,
    is_visible: true,
    is_featured: false,
    sync_status: 'not_synced',
    created_at: '2024-01-11T11:30:00Z',
    updated_at: '2024-01-11T11:30:00Z',
    brand: 'ChargeTech'
  }
];

const categories = ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Books'];
const brands = ['AudioTech', 'FitTech', 'EcoWear', 'HydroLife', 'ChargeTech'];

const PIMProductsPage = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [openActionMenu, setOpenActionMenu] = useState<string | null>(null);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    category: '',
    brand: '',
    is_visible: '',
    is_featured: '',
    sync_status: '',
    price_range: { min: '', max: '' },
    inventory_range: { min: '', max: '' },
    date_range: { start: '', end: '' }
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [sortBy, setSortBy] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC');
  const [visibleColumns, setVisibleColumns] = useState({
    image: true,
    name: true,
    sku: true,
    category: true,
    brand: true,
    price: true,
    inventory: true,
    status: true,
    sync_status: true,
    updated_at: true,
    actions: true
  });

  // Filtering logic
  const filteredProducts = useMemo(() => {
    let filtered = [...mockProducts];
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.sku?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.brand && product.brand.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }
    if (filters.category) {
      filtered = filtered.filter(product => product.category === filters.category);
    }
    if (filters.brand) {
      filtered = filtered.filter(product => product.brand === filters.brand);
    }
    if (filters.is_visible) {
      filtered = filtered.filter(product =>
        filters.is_visible === 'visible' ? product.is_visible : !product.is_visible
      );
    }
    if (filters.is_featured) {
      filtered = filtered.filter(product =>
        filters.is_featured === 'featured' ? product.is_featured : !product.is_featured
      );
    }
    if (filters.sync_status) {
      filtered = filtered.filter(product => product.sync_status === filters.sync_status);
    }
    if (filters.price_range.min) {
      filtered = filtered.filter(product => product.price >= parseFloat(filters.price_range.min));
    }
    if (filters.price_range.max) {
      filtered = filtered.filter(product => product.price <= parseFloat(filters.price_range.max));
    }
    if (filters.inventory_range.min) {
      filtered = filtered.filter(product => product.inventory_level >= parseInt(filters.inventory_range.min));
    }
    if (filters.inventory_range.max) {
      filtered = filtered.filter(product => product.inventory_level <= parseInt(filters.inventory_range.max));
    }
    // Sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof typeof a];
      let bValue: any = b[sortBy as keyof typeof b];
      if (sortBy === 'updated_at') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }
      if (sortOrder === 'ASC') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    return filtered;
  }, [searchQuery, filters, sortBy, sortOrder]);

  // Pagination
  const totalProducts = filteredProducts.length;
  const totalPages = Math.ceil(totalProducts / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(paginatedProducts.map(p => p.id));
    } else {
      setSelectedProducts([]);
    }
  };
  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts(prev => [...prev, productId]);
    } else {
      setSelectedProducts(prev => prev.filter(id => id !== productId));
    }
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      category: '',
      brand: '',
      is_visible: '',
      is_featured: '',
      sync_status: '',
      price_range: { min: '', max: '' },
      inventory_range: { min: '', max: '' },
      date_range: { start: '', end: '' }
    });
    setSearchQuery('');
  };

  // Check if any filters are active
  const hasActiveFilters = searchQuery || Object.values(filters).some(value =>
    typeof value === 'string' ? value : Object.values(value).some(v => v)
  );

  // Column definitions
  const columns = [
    { key: 'image', label: 'Image', sortable: false },
    { key: 'name', label: 'Product Name', sortable: true },
    { key: 'sku', label: 'SKU', sortable: true },
    { key: 'category', label: 'Category', sortable: true },
    { key: 'brand', label: 'Brand', sortable: true },
    { key: 'price', label: 'Price', sortable: true },
    { key: 'inventory_level', label: 'Inventory', sortable: true },
    { key: 'status', label: 'Status', sortable: true },
    { key: 'sync_status', label: 'Sync Status', sortable: true },
    { key: 'updated_at', label: 'Last Updated', sortable: true },
    { key: 'actions', label: 'Actions', sortable: false }
  ];

  const handleSort = (columnKey: string) => {
    if (sortBy === columnKey) {
      setSortOrder(sortOrder === 'ASC' ? 'DESC' : 'ASC');
    } else {
      setSortBy(columnKey);
      setSortOrder('ASC');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Action handlers
  const handleViewProduct = (productId: string) => {
    navigate(`/products/view/${productId}`);
  };
  const handleEditProduct = (productId: string) => {
    navigate(`/products/edit/${productId}`);
  };
  const handleDeleteProduct = (productId: string) => {
    // TODO: Implement delete confirmation modal
    console.log('Delete product:', productId);
  };
  const handleDuplicateProduct = (productId: string) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate product:', productId);
  };
  const handleArchiveProduct = (productId: string) => {
    // TODO: Implement archive functionality
    console.log('Archive product:', productId);
  };
  const toggleActionMenu = (productId: string) => {
    setOpenActionMenu(openActionMenu === productId ? null : productId);
  };
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openActionMenu && !(event.target as Element).closest('.action-menu')) {
        setOpenActionMenu(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [openActionMenu]);

  return (
    <div className="space-y-6">
      <SectionHeader
        icon={<Package className="h-7 w-7" />}
        title="Products"
        subtitle="Manage all your products in one place"
        actions={
          <button
            onClick={() => navigate('/products/create')}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Product
          </button>
        }
      />
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search products by name, SKU, or brand..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${
                showFilters || hasActiveFilters
                  ? 'border-blue-300 bg-blue-50 text-blue-700'
                  : 'border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Filter className="h-4 w-4 mr-2 inline" />
              Filter
              {hasActiveFilters && (
                <span className="ml-1 px-1.5 py-0.5 bg-blue-600 text-white text-xs rounded-full">
                  {Object.values(filters).filter(v => typeof v === 'string' ? v : Object.values(v).some(val => val)).length}
                </span>
              )}
            </button>
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <X className="h-4 w-4 mr-1 inline" />
                Clear
              </button>
            )}
          </div>
          <div className="flex items-center space-x-3">
            {selectedProducts.length > 0 && (
              <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
                <span className="text-sm text-blue-700 font-medium">
                  {selectedProducts.length} selected
                </span>
                <button className="text-sm text-blue-600 hover:text-blue-800">
                  Bulk Edit
                </button>
                <button className="text-sm text-red-600 hover:text-red-800">
                  Delete
                </button>
              </div>
            )}
            <ColumnSettings
              columns={columns}
              visibleColumns={visibleColumns}
              onColumnToggle={(columnKey, visible) =>
                setVisibleColumns(prev => ({ ...prev, [columnKey]: visible }))
              }
            />
          </div>
        </div>
        {showFilters && (
          <div className="border-t border-gray-200 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <CustomSelect
                  options={categories.map(cat => ({ value: cat, label: cat }))}
                  value={filters.category}
                  onChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                  placeholder="All categories"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                <CustomSelect
                  options={brands.map(brand => ({ value: brand, label: brand }))}
                  value={filters.brand}
                  onChange={(value) => setFilters(prev => ({ ...prev, brand: value }))}
                  placeholder="All brands"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Visibility</label>
                <CustomSelect
                  options={[
                    { value: '', label: 'All products' },
                    { value: 'visible', label: 'Visible' },
                    { value: 'hidden', label: 'Hidden' }
                  ]}
                  value={filters.is_visible}
                  onChange={(value) => setFilters(prev => ({ ...prev, is_visible: value }))}
                  placeholder="All products"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Featured</label>
                <CustomSelect
                  options={[
                    { value: '', label: 'All products' },
                    { value: 'featured', label: 'Featured' },
                    { value: 'not_featured', label: 'Not Featured' }
                  ]}
                  value={filters.is_featured}
                  onChange={(value) => setFilters(prev => ({ ...prev, is_featured: value }))}
                  placeholder="All products"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Sync Status</label>
                <CustomSelect
                  options={[
                    { value: '', label: 'All statuses' },
                    { value: 'synced', label: 'Synced' },
                    { value: 'pending', label: 'Pending' },
                    { value: 'error', label: 'Error' },
                    { value: 'not_synced', label: 'Not Synced' }
                  ]}
                  value={filters.sync_status}
                  onChange={(value) => setFilters(prev => ({ ...prev, sync_status: value }))}
                  placeholder="All statuses"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.price_range.min}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      price_range: { ...prev.price_range, min: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.price_range.max}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      price_range: { ...prev.price_range, max: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Inventory Range</label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.inventory_range.min}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      inventory_range: { ...prev.inventory_range, min: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.inventory_range.max}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      inventory_range: { ...prev.inventory_range, max: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Products Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mt-6">
        <div className="overflow-x-auto">
          <table className="w-full table-auto divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {/* Select All Checkbox */}
                <th className="w-12 px-3 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedProducts.length === paginatedProducts.length && paginatedProducts.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
                {columns.map((column) => {
                  if (!visibleColumns[column.key as keyof typeof visibleColumns]) return null;
                  return (
                    <th
                      key={column.key}
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => column.sortable && handleSort(column.key)}
                    >
                      <span className="truncate">{column.label}</span>
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedProducts.length === 0 ? (
                <tr>
                  <td colSpan={columns.length + 1} className="px-6 py-12 text-center">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p className="text-gray-600 mb-6">
                      {hasActiveFilters
                        ? 'Try adjusting your filters or search terms.'
                        : 'Get started by creating your first product.'}
                    </p>
                    {!hasActiveFilters && (
                      <button
                        onClick={() => navigate('/products/create')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Plus className="h-4 w-4 mr-2 inline" />
                        Create Product
                      </button>
                    )}
                  </td>
                </tr>
              ) : (
                paginatedProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50 transition-colors">
                    {/* Select Checkbox */}
                    <td className="w-12 px-3 py-3">
                      <input
                        type="checkbox"
                        checked={selectedProducts.includes(product.id)}
                        onChange={(e) => handleSelectProduct(product.id, e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    {/* Product Name */}
                    {visibleColumns.name && (
                      <td className="min-w-0 px-3 py-3">
                        <div className="min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">{product.name}</div>
                          <div className="text-xs text-gray-500 truncate">{product.brand}</div>
                        </div>
                      </td>
                    )}
                    {/* SKU */}
                    {visibleColumns.sku && (
                      <td className="w-20 px-3 py-3 text-xs text-gray-900 font-mono">
                        <div className="truncate">{product.sku}</div>
                      </td>
                    )}
                    {/* Category */}
                    {visibleColumns.category && (
                      <td className="w-20 px-3 py-3">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 truncate">
                          {product.category}
                        </span>
                      </td>
                    )}
                    {/* Brand */}
                    {visibleColumns.brand && (
                      <td className="w-20 px-3 py-3">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 truncate">
                          {product.brand}
                        </span>
                      </td>
                    )}
                    {/* Price */}
                    {visibleColumns.price && (
                      <td className="w-16 px-3 py-3 text-xs text-gray-900">
                        <div className="font-medium">{formatPrice(product.price)}</div>
                      </td>
                    )}
                    {/* Inventory */}
                    {visibleColumns.inventory && (
                      <td className="w-12 px-3 py-3 text-xs text-gray-900 text-center">
                        <span className="font-medium">{product.inventory_level}</span>
                      </td>
                    )}
                    {/* Status */}
                    {visibleColumns.status && (
                      <td className="w-20 px-3 py-3">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${product.is_visible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {product.is_visible ? 'Active' : 'Inactive'}
                        </span>
                        {product.is_featured && (
                          <Star className="h-3 w-3 text-yellow-500 fill-current ml-1" />
                        )}
                      </td>
                    )}
                    {/* Sync Status */}
                    {visibleColumns.sync_status && (
                      <td className="w-20 px-3 py-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${product.sync_status === 'synced' ? 'bg-green-100 text-green-800' : product.sync_status === 'pending' ? 'bg-yellow-100 text-yellow-800' : product.sync_status === 'error' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`}>
                          {product.sync_status.charAt(0).toUpperCase() + product.sync_status.slice(1)}
                        </span>
                      </td>
                    )}
                    {/* Last Updated */}
                    {visibleColumns.updated_at && (
                      <td className="w-24 px-3 py-3 text-xs text-gray-500">
                        <div className="truncate">{formatDate(product.updated_at)}</div>
                      </td>
                    )}
                    {/* Actions */}
                    {visibleColumns.actions && (
                      <td className="w-32 px-3 py-3 text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewProduct(product.id)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
                            title="View Product"
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </button>
                          <button
                            onClick={() => handleEditProduct(product.id)}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                            title="Edit Product"
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </button>
                          <div className="relative action-menu">
                            <button
                              onClick={() => toggleActionMenu(product.id)}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
                              title="More Actions"
                            >
                              <MoreHorizontal className="h-3 w-3 mr-1" />
                              More
                              <ChevronDown className="h-3 w-3 ml-1" />
                            </button>
                            {openActionMenu === product.id && (
                              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                                <div className="py-1">
                                  <button
                                    onClick={() => {
                                      handleDuplicateProduct(product.id);
                                      setOpenActionMenu(null);
                                    }}
                                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                  >
                                    <Copy className="h-4 w-4 mr-3 text-gray-400" />
                                    Duplicate
                                  </button>
                                  <button
                                    onClick={() => {
                                      handleArchiveProduct(product.id);
                                      setOpenActionMenu(null);
                                    }}
                                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                  >
                                    <Archive className="h-4 w-4 mr-3 text-gray-400" />
                                    Archive
                                  </button>
                                  <button
                                    onClick={() => {
                                      // TODO: Implement sync functionality
                                      setOpenActionMenu(null);
                                    }}
                                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                  >
                                    <RefreshCw className="h-4 w-4 mr-3 text-gray-400" />
                                    Sync Now
                                  </button>
                                  <button
                                    onClick={() => {
                                      // TODO: Implement external link functionality
                                      setOpenActionMenu(null);
                                    }}
                                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                  >
                                    <ExternalLink className="h-4 w-4 mr-3 text-gray-400" />
                                    View on Store
                                  </button>
                                  <div className="border-t border-gray-200 my-1"></div>
                                  <button
                                    onClick={() => {
                                      handleDeleteProduct(product.id);
                                      setOpenActionMenu(null);
                                    }}
                                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                  >
                                    <Trash2 className="h-4 w-4 mr-3" />
                                    Delete
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    )}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        {/* Pagination */}
        {totalProducts > 0 && (
          <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(endIndex, totalProducts)}</span> of{' '}
                  <span className="font-medium">{totalProducts}</span> results
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">Show:</span>
                  <CustomSelect
                    options={[
                      { value: '10', label: '10' },
                      { value: '20', label: '20' },
                      { value: '50', label: '50' },
                      { value: '100', label: '100' }
                    ]}
                    value={itemsPerPage.toString()}
                    onChange={(value) => {
                      setItemsPerPage(parseInt(value));
                      setCurrentPage(1);
                    }}
                    placeholder=""
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Previous
                </button>
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
                          currentPage === page
                            ? 'border-blue-500 bg-blue-50 text-blue-600'
                            : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}
                  {totalPages > 5 && (
                    <>
                      <span className="px-3 py-2 text-gray-500">...</span>
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        className={`px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
                          currentPage === totalPages
                            ? 'border-blue-500 bg-blue-50 text-blue-600'
                            : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {totalPages}
                      </button>
                    </>
                  )}
                </div>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PIMProductsPage; 
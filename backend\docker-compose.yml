version: "3.8"

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: pim_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - ENVIRONMENT=development
      - DATABASE_URL=**************************************/pim_db
    volumes:
      - .:/app
    depends_on:
      db:
        condition: service_healthy
    command: >
      sh -c "python setup_db.py &&
             uvicorn main:app --host 0.0.0.0 --port 8000 --reload"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:

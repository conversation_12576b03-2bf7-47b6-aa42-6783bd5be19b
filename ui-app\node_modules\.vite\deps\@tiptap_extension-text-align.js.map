{"version": 3, "sources": ["../../@tiptap/extension-text-align/src/text-align.ts", "../../@tiptap/extension-text-align/src/index.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\n\nexport interface TextAlignOptions {\n  /**\n   * The types where the text align attribute can be applied.\n   * @default []\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n\n  /**\n   * The alignments which are allowed.\n   * @default ['left', 'center', 'right', 'justify']\n   * @example ['left', 'right']\n   */\n  alignments: string[]\n\n  /**\n   * The default alignment.\n   * @default null\n   * @example 'center'\n   */\n  defaultAlignment: string | null\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textAlign: {\n      /**\n       * Set the text align attribute\n       * @param alignment The alignment\n       * @example editor.commands.setTextAlign('left')\n       */\n      setTextAlign: (alignment: string) => ReturnType\n      /**\n       * Unset the text align attribute\n       * @example editor.commands.unsetTextAlign()\n       */\n      unsetTextAlign: () => ReturnType\n      /**\n       * Toggle the text align attribute\n       * @param alignment The alignment\n       * @example editor.commands.toggleTextAlign('right')\n       */\n      toggleTextAlign: (alignment: string) => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to align text.\n * @see https://www.tiptap.dev/api/extensions/text-align\n */\nexport const TextAlign = Extension.create<TextAlignOptions>({\n  name: 'textAlign',\n\n  addOptions() {\n    return {\n      types: [],\n      alignments: ['left', 'center', 'right', 'justify'],\n      defaultAlignment: null,\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          textAlign: {\n            default: this.options.defaultAlignment,\n            parseHTML: element => {\n              const alignment = element.style.textAlign\n\n              return this.options.alignments.includes(alignment) ? alignment : this.options.defaultAlignment\n            },\n            renderHTML: attributes => {\n              if (!attributes.textAlign) {\n                return {}\n              }\n\n              return { style: `text-align: ${attributes.textAlign}` }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setTextAlign:\n        (alignment: string) =>\n        ({ commands }) => {\n          if (!this.options.alignments.includes(alignment)) {\n            return false\n          }\n\n          return this.options.types\n            .map(type => commands.updateAttributes(type, { textAlign: alignment }))\n            .every(response => response)\n        },\n\n      unsetTextAlign:\n        () =>\n        ({ commands }) => {\n          return this.options.types.map(type => commands.resetAttributes(type, 'textAlign')).every(response => response)\n        },\n\n      toggleTextAlign:\n        alignment =>\n        ({ editor, commands }) => {\n          if (!this.options.alignments.includes(alignment)) {\n            return false\n          }\n\n          if (editor.isActive({ textAlign: alignment })) {\n            return commands.unsetTextAlign()\n          }\n          return commands.setTextAlign(alignment)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-l': () => this.editor.commands.setTextAlign('left'),\n      'Mod-Shift-e': () => this.editor.commands.setTextAlign('center'),\n      'Mod-Shift-r': () => this.editor.commands.setTextAlign('right'),\n      'Mod-Shift-j': () => this.editor.commands.setTextAlign('justify'),\n    }\n  },\n})\n", "import { TextAlign } from './text-align.js'\n\nexport * from './text-align.js'\n\nexport default TextAlign\n"], "mappings": ";;;;;;AAqDO,IAAM,YAAY,UAAU,OAAyB;EAC1D,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO,CAAC;MACR,YAAY,CAAC,QAAQ,UAAU,SAAS,SAAS;MACjD,kBAAkB;IACpB;EACF;EAEA,sBAAsB;AACpB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,WAAW;YACT,SAAS,KAAK,QAAQ;YACtB,WAAW,CAAA,YAAW;AACpB,oBAAM,YAAY,QAAQ,MAAM;AAEhC,qBAAO,KAAK,QAAQ,WAAW,SAAS,SAAS,IAAI,YAAY,KAAK,QAAQ;YAChF;YACA,YAAY,CAAA,eAAc;AACxB,kBAAI,CAAC,WAAW,WAAW;AACzB,uBAAO,CAAC;cACV;AAEA,qBAAO,EAAE,OAAO,eAAe,WAAW,SAAS,GAAG;YACxD;UACF;QACF;MACF;IACF;EACF;EAEA,cAAc;AACZ,WAAO;MACL,cACE,CAAC,cACD,CAAC,EAAE,SAAS,MAAM;AAChB,YAAI,CAAC,KAAK,QAAQ,WAAW,SAAS,SAAS,GAAG;AAChD,iBAAO;QACT;AAEA,eAAO,KAAK,QAAQ,MACjB,IAAI,CAAA,SAAQ,SAAS,iBAAiB,MAAM,EAAE,WAAW,UAAU,CAAC,CAAC,EACrE,MAAM,CAAA,aAAY,QAAQ;MAC/B;MAEF,gBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,KAAK,QAAQ,MAAM,IAAI,CAAA,SAAQ,SAAS,gBAAgB,MAAM,WAAW,CAAC,EAAE,MAAM,CAAA,aAAY,QAAQ;MAC/G;MAEF,iBACE,CAAA,cACA,CAAC,EAAE,QAAQ,SAAS,MAAM;AACxB,YAAI,CAAC,KAAK,QAAQ,WAAW,SAAS,SAAS,GAAG;AAChD,iBAAO;QACT;AAEA,YAAI,OAAO,SAAS,EAAE,WAAW,UAAU,CAAC,GAAG;AAC7C,iBAAO,SAAS,eAAe;QACjC;AACA,eAAO,SAAS,aAAa,SAAS;MACxC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa,MAAM;MAC7D,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa,QAAQ;MAC/D,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa,OAAO;MAC9D,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa,SAAS;IAClE;EACF;AACF,CAAC;AChID,IAAO,gBAAQ;", "names": []}
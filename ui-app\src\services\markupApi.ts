import { delay } from '../utils/delay';

// Types
export interface MarkupRule {
  id: string;
  name: string;
  description?: string;
  markup_type: 'percentage' | 'fixed';
  markup_value: number;
  priority: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  targets: MarkupTarget[];
}

export interface MarkupTarget {
  id: string;
  target_type: 'product' | 'category' | 'brand' | 'supplier';
  target_id: string;
  target_name?: string;
  created_at: string;
}

export interface SupplierMarkupOverride {
  id: string;
  supplier_id: string;
  supplier_name?: string;
  product_id: string;
  product_name?: string;
  markup_type: 'percentage' | 'fixed';
  markup_value: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface MarkupCalculation {
  product_id: string;
  supplier_id: string;
  base_price: number;
  calculated_price: number;
  markup_amount: number;
  markup_percentage: number;
  applied_rules: AppliedRule[];
}

export interface AppliedRule {
  rule_type: string;
  rule_id?: string;
  rule_name?: string;
  target_type?: string;
  markup_type: string;
  markup_value: number;
}

export interface MarkupPreview {
  product_id: string;
  product_name: string;
  base_price: number;
  calculated_price: number;
  markup_amount: number;
  markup_percentage: number;
  applied_rules: AppliedRule[];
}

// Request Types
export interface CreateMarkupRuleRequest {
  name: string;
  description?: string;
  markup_type: 'percentage' | 'fixed';
  markup_value: number;
  priority?: number;
  targets: CreateMarkupTargetRequest[];
}

export interface CreateMarkupTargetRequest {
  target_type: 'product' | 'category' | 'brand' | 'supplier';
  target_id: string;
}

export interface UpdateMarkupRuleRequest {
  name?: string;
  description?: string;
  markup_type?: 'percentage' | 'fixed';
  markup_value?: number;
  priority?: number;
  is_active?: boolean;
  targets?: CreateMarkupTargetRequest[];
}

export interface CreateSupplierMarkupOverrideRequest {
  supplier_id: string;
  product_id: string;
  markup_type: 'percentage' | 'fixed';
  markup_value: number;
}

export interface UpdateSupplierMarkupOverrideRequest {
  markup_type?: 'percentage' | 'fixed';
  markup_value?: number;
  is_active?: boolean;
}

export interface MarkupPreviewRequest {
  product_ids: string[];
  supplier_id: string;
}

// Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface MarkupRuleListResponse {
  id: string;
  name: string;
  markup_type: string;
  markup_value: number;
  priority: number;
  is_active: boolean;
  target_count: number;
  created_at: string;
}

// Mock data for development
const mockMarkupRules: MarkupRule[] = [
  {
    id: 'rule_1',
    name: 'Premium Vendor Markup',
    description: '15% markup for premium vendors',
    markup_type: 'percentage',
    markup_value: 15.0,
    priority: 10,
    is_active: true,
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:30:00Z',
    targets: [
      {
        id: 'target_1',
        target_type: 'supplier',
        target_id: 'supplier_1',
        target_name: 'TechCorp Solutions',
        created_at: '2024-01-15T10:30:00Z'
      }
    ]
  },
  {
    id: 'rule_2',
    name: 'Electronics Category Markup',
    description: '10% markup for electronics category',
    markup_type: 'percentage',
    markup_value: 10.0,
    priority: 5,
    is_active: true,
    created_at: '2024-01-14T15:45:00Z',
    updated_at: '2024-01-14T15:45:00Z',
    targets: [
      {
        id: 'target_2',
        target_type: 'category',
        target_id: 'cat_1',
        target_name: 'Electronics',
        created_at: '2024-01-14T15:45:00Z'
      }
    ]
  },
  {
    id: 'rule_3',
    name: 'Fixed Markup for Brand X',
    description: '$5 fixed markup for Brand X products',
    markup_type: 'fixed',
    markup_value: 5.0,
    priority: 8,
    is_active: true,
    created_at: '2024-01-13T09:20:00Z',
    updated_at: '2024-01-13T09:20:00Z',
    targets: [
      {
        id: 'target_3',
        target_type: 'brand',
        target_id: 'brand_1',
        target_name: 'Brand X',
        created_at: '2024-01-13T09:20:00Z'
      }
    ]
  }
];

const mockSupplierOverrides: SupplierMarkupOverride[] = [
  {
    id: 'override_1',
    supplier_id: 'supplier_1',
    supplier_name: 'TechCorp Solutions',
    product_id: 'product_1',
    product_name: 'Wireless Headphones',
    markup_type: 'percentage',
    markup_value: 20.0,
    is_active: true,
    created_at: '2024-01-15T11:00:00Z',
    updated_at: '2024-01-15T11:00:00Z'
  },
  {
    id: 'override_2',
    supplier_id: 'supplier_2',
    supplier_name: 'HealthTech Inc',
    product_id: 'product_2',
    product_name: 'Fitness Tracker',
    markup_type: 'fixed',
    markup_value: 8.0,
    is_active: true,
    created_at: '2024-01-14T16:00:00Z',
    updated_at: '2024-01-14T16:00:00Z'
  }
];

export const markupApi = {
  // Markup Rules
  getMarkupRules: async (params?: { limit?: number; offset?: number }): Promise<ApiResponse<MarkupRuleListResponse[]>> => {
    await delay();
    
    const rules = mockMarkupRules.map(rule => ({
      id: rule.id,
      name: rule.name,
      markup_type: rule.markup_type,
      markup_value: rule.markup_value,
      priority: rule.priority,
      is_active: rule.is_active,
      target_count: rule.targets.length,
      created_at: rule.created_at
    }));

    return {
      success: true,
      data: rules
    };
  },

  getMarkupRule: async (id: string): Promise<ApiResponse<MarkupRule>> => {
    await delay();
    
    const rule = mockMarkupRules.find(r => r.id === id);
    if (!rule) {
      return {
        success: false,
        error: 'Markup rule not found'
      };
    }

    return {
      success: true,
      data: rule
    };
  },

  createMarkupRule: async (request: CreateMarkupRuleRequest): Promise<ApiResponse<MarkupRule>> => {
    await delay();
    
    const newRule: MarkupRule = {
      id: `rule_${Date.now()}`,
      name: request.name,
      description: request.description,
      markup_type: request.markup_type,
      markup_value: request.markup_value,
      priority: request.priority || 0,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      targets: request.targets.map((target, index) => ({
        id: `target_${Date.now()}_${index}`,
        target_type: target.target_type,
        target_id: target.target_id,
        target_name: `Target ${target.target_type}`,
        created_at: new Date().toISOString()
      }))
    };

    mockMarkupRules.push(newRule);

    return {
      success: true,
      data: newRule,
      message: 'Markup rule created successfully'
    };
  },

  updateMarkupRule: async (id: string, request: UpdateMarkupRuleRequest): Promise<ApiResponse<MarkupRule>> => {
    await delay();
    
    const ruleIndex = mockMarkupRules.findIndex(r => r.id === id);
    if (ruleIndex === -1) {
      return {
        success: false,
        error: 'Markup rule not found'
      };
    }

    const updatedRule: MarkupRule = {
      ...mockMarkupRules[ruleIndex],
      ...request,
      updated_at: new Date().toISOString(),
      targets: request.targets ? request.targets.map((target, index) => ({
        id: `target_${Date.now()}_${index}`,
        target_type: target.target_type,
        target_id: target.target_id,
        target_name: `Target ${target.target_type}`,
        created_at: new Date().toISOString()
      })) : mockMarkupRules[ruleIndex].targets
    };

    mockMarkupRules[ruleIndex] = updatedRule;

    return {
      success: true,
      data: updatedRule,
      message: 'Markup rule updated successfully'
    };
  },

  deleteMarkupRule: async (id: string): Promise<ApiResponse<void>> => {
    await delay();
    
    const ruleIndex = mockMarkupRules.findIndex(r => r.id === id);
    if (ruleIndex === -1) {
      return {
        success: false,
        error: 'Markup rule not found'
      };
    }

    mockMarkupRules.splice(ruleIndex, 1);

    return {
      success: true,
      message: 'Markup rule deleted successfully'
    };
  },

  // Supplier Markup Overrides
  getSupplierMarkupOverrides: async (params?: { supplier_id?: string; product_id?: string }): Promise<ApiResponse<SupplierMarkupOverride[]>> => {
    await delay();
    
    let overrides = [...mockSupplierOverrides];

    if (params?.supplier_id) {
      overrides = overrides.filter(o => o.supplier_id === params.supplier_id);
    }

    if (params?.product_id) {
      overrides = overrides.filter(o => o.product_id === params.product_id);
    }

    return {
      success: true,
      data: overrides
    };
  },

  createSupplierMarkupOverride: async (request: CreateSupplierMarkupOverrideRequest): Promise<ApiResponse<SupplierMarkupOverride>> => {
    await delay();
    
    const newOverride: SupplierMarkupOverride = {
      id: `override_${Date.now()}`,
      supplier_id: request.supplier_id,
      supplier_name: 'Supplier Name',
      product_id: request.product_id,
      product_name: 'Product Name',
      markup_type: request.markup_type,
      markup_value: request.markup_value,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    mockSupplierOverrides.push(newOverride);

    return {
      success: true,
      data: newOverride,
      message: 'Supplier markup override created successfully'
    };
  },

  updateSupplierMarkupOverride: async (id: string, request: UpdateSupplierMarkupOverrideRequest): Promise<ApiResponse<SupplierMarkupOverride>> => {
    await delay();
    
    const overrideIndex = mockSupplierOverrides.findIndex(o => o.id === id);
    if (overrideIndex === -1) {
      return {
        success: false,
        error: 'Supplier markup override not found'
      };
    }

    const updatedOverride = {
      ...mockSupplierOverrides[overrideIndex],
      ...request,
      updated_at: new Date().toISOString()
    };

    mockSupplierOverrides[overrideIndex] = updatedOverride;

    return {
      success: true,
      data: updatedOverride,
      message: 'Supplier markup override updated successfully'
    };
  },

  deleteSupplierMarkupOverride: async (id: string): Promise<ApiResponse<void>> => {
    await delay();
    
    const overrideIndex = mockSupplierOverrides.findIndex(o => o.id === id);
    if (overrideIndex === -1) {
      return {
        success: false,
        error: 'Supplier markup override not found'
      };
    }

    mockSupplierOverrides.splice(overrideIndex, 1);

    return {
      success: true,
      message: 'Supplier markup override deleted successfully'
    };
  },

  // Markup Calculations
  calculateProductMarkup: async (productId: string, supplierId: string): Promise<ApiResponse<MarkupCalculation>> => {
    await delay();
    
    const basePrice = 99.99; // Mock base price
    const markupPercentage = 15.0; // Mock markup
    const markupAmount = basePrice * (markupPercentage / 100);
    const calculatedPrice = basePrice + markupAmount;

    const calculation: MarkupCalculation = {
      product_id: productId,
      supplier_id: supplierId,
      base_price: basePrice,
      calculated_price: calculatedPrice,
      markup_amount: markupAmount,
      markup_percentage: markupPercentage,
      applied_rules: [
        {
          rule_type: 'rule',
          rule_id: 'rule_1',
          rule_name: 'Premium Supplier Markup',
          target_type: 'supplier',
          markup_type: 'percentage',
          markup_value: markupPercentage
        }
      ]
    };

    return {
      success: true,
      data: calculation
    };
  },

  previewMarkup: async (request: MarkupPreviewRequest): Promise<ApiResponse<MarkupPreview[]>> => {
    await delay();
    
    const previews: MarkupPreview[] = request.product_ids.map((productId, index) => {
      const basePrice = 99.99 + (index * 10); // Mock varying prices
      const markupPercentage = 15.0;
      const markupAmount = basePrice * (markupPercentage / 100);
      const calculatedPrice = basePrice + markupAmount;

      return {
        product_id: productId,
        product_name: `Product ${index + 1}`,
        base_price: basePrice,
        calculated_price: calculatedPrice,
        markup_amount: markupAmount,
        markup_percentage: markupPercentage,
        applied_rules: [
          {
            rule_type: 'rule',
            rule_id: 'rule_1',
            rule_name: 'Premium Vendor Markup',
            target_type: 'supplier',
            markup_type: 'percentage',
            markup_value: markupPercentage
          }
        ]
      };
    });

    return {
      success: true,
      data: previews
    };
  },

  refreshMarkupCalculations: async (): Promise<ApiResponse<void>> => {
    await delay();
    
    return {
      success: true,
      message: 'Markup calculations refreshed successfully'
    };
  }
}; 
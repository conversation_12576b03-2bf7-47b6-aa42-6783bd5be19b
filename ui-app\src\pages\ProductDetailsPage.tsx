import { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  ArrowLeft,
  Edit,
  Package,
  Eye,
  Star,
  Tag,
  DollarSign,
  BarChart3,
  Calendar,
  Globe,
  RefreshCw,
  ExternalLink,
  Copy,
  Archive,
  Trash2,
  MoreHorizontal,
  ChevronDown
} from 'lucide-react'
import { productApi } from '../services/productApi'
import SectionHeader from '../components/SectionHeader'

const ProductDetailsPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [openActionMenu, setOpenActionMenu] = useState(false)

  // Fetch product data
  const { data: product, isLoading, error } = useQuery(
    ['product', id],
    () => productApi.getProduct(id!),
    {
      enabled: !!id,
      staleTime: 30000
    }
  )

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getSyncStatusBadge = (status: string) => {
    const statusConfig = {
      synced: { color: 'bg-green-100 text-green-800', label: 'Synced' },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      error: { color: 'bg-red-100 text-red-800', label: 'Error' },
      not_synced: { color: 'bg-gray-100 text-gray-800', label: 'Not Synced' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.not_synced

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  const handleEdit = () => {
    navigate(`/products/edit/${id}`)
  }

  const handleDelete = () => {
    // TODO: Implement delete confirmation modal
    console.log('Delete product:', id)
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading product details...</span>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Product not found</h3>
            <p className="text-gray-600 mb-4">The product you're looking for doesn't exist or has been removed.</p>
            <button
              onClick={() => navigate('/products')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2 inline" />
              Back to Products
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/products')}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <SectionHeader
            icon={<Package className="h-7 w-7" />}
            title={product.name}
            subtitle={`Product ID: ${product.id} • SKU: ${product.sku || 'N/A'}`}
            actions={
              <>
                <button
                  onClick={handleEdit}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Product
                </button>

                {/* More Actions Dropdown */}
                <div className="relative">
                  <button
                    onClick={() => setOpenActionMenu(!openActionMenu)}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
                  >
                    <MoreHorizontal className="h-5 w-5" />
                  </button>

                  {openActionMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                      <div className="py-1">
                        <button
                          onClick={() => {
                            // TODO: Implement duplicate functionality
                            setOpenActionMenu(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        >
                          <Copy className="h-4 w-4 mr-3 text-gray-400" />
                          Duplicate
                        </button>
                        
                        <button
                          onClick={() => {
                            // TODO: Implement archive functionality
                            setOpenActionMenu(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        >
                          <Archive className="h-4 w-4 mr-3 text-gray-400" />
                          Archive
                        </button>
                        
                        <button
                          onClick={() => {
                            // TODO: Implement sync functionality
                            setOpenActionMenu(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        >
                          <RefreshCw className="h-4 w-4 mr-3 text-gray-400" />
                          Sync Now
                        </button>
                        
                        <button
                          onClick={() => {
                            // TODO: Implement external link functionality
                            setOpenActionMenu(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        >
                          <ExternalLink className="h-4 w-4 mr-3 text-gray-400" />
                          View on Store
                        </button>
                        
                        <div className="border-t border-gray-200 my-1"></div>
                        
                        <button
                          onClick={() => {
                            handleDelete()
                            setOpenActionMenu(false)
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                        >
                          <Trash2 className="h-4 w-4 mr-3" />
                          Delete
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </>
            }
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Left Column - Product Image and Basic Info */}
        <div className="lg:col-span-2 space-y-4">
          {/* Product Image */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center mb-3">
              <Package className="h-16 w-16 text-gray-400" />
            </div>
            <div className="grid grid-cols-6 gap-2">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                  <Package className="h-4 w-4 text-gray-300" />
                </div>
              ))}
            </div>
          </div>

          {/* Product Description */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Description</h3>
            <p className="text-gray-600">
              {product.description || 'No description available for this product.'}
            </p>
          </div>

          {/* Product Details */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Product Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Product Type</label>
                <p className="text-sm text-gray-900 capitalize">{product.product_type}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Condition</label>
                <p className="text-sm text-gray-900">{product.condition}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Weight</label>
                <p className="text-sm text-gray-900">{product.weight || 0} lbs</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Dimensions</label>
                <p className="text-sm text-gray-900">
                  {product.width || 0}" × {product.depth || 0}" × {product.height || 0}"
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">UPC</label>
                <p className="text-sm text-gray-900">{product.upc || 'N/A'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Warranty</label>
                <p className="text-sm text-gray-900">{product.warranty || 'N/A'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Stats and Info */}
        <div className="space-y-4">
          {/* Quick Stats */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-600">Price</span>
                </div>
                <span className="text-sm font-medium text-gray-900">{formatPrice(product.price)}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Package className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-600">Inventory</span>
                </div>
                <span className={`text-sm font-medium ${
                  product.inventory_level === 0
                    ? 'text-red-600'
                    : product.inventory_level < 10
                    ? 'text-yellow-600'
                    : 'text-green-600'
                }`}>
                  {product.inventory_level} in stock
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Eye className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-600">Views</span>
                </div>
                <span className="text-sm font-medium text-gray-900">{product.view_count}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <BarChart3 className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-600">Total Sold</span>
                </div>
                <span className="text-sm font-medium text-gray-900">{product.total_sold}</span>
              </div>
            </div>
          </div>

          {/* Status & Visibility */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Status & Visibility</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Visibility</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  product.is_visible
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {product.is_visible ? 'Visible' : 'Hidden'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Featured</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  product.is_featured
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {product.is_featured ? 'Featured' : 'Not Featured'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Sync Status</span>
                {getSyncStatusBadge(product.sync_status)}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Availability</span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  product.availability === 'available'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {product.availability}
                </span>
              </div>
            </div>
          </div>

          {/* Pricing Information */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Pricing</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Sale Price</span>
                <span className="text-sm font-medium text-gray-900">{formatPrice(product.price)}</span>
              </div>
              {product.cost_price && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Cost Price</span>
                  <span className="text-sm font-medium text-gray-900">{formatPrice(product.cost_price)}</span>
                </div>
              )}
              {product.retail_price && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Retail Price</span>
                  <span className="text-sm font-medium text-gray-900">{formatPrice(product.retail_price)}</span>
                </div>
              )}
              {product.sale_price && product.sale_price !== product.price && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Sale Price</span>
                  <span className="text-sm font-medium text-red-600">{formatPrice(product.sale_price)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Dates */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Dates</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Created</span>
                <span className="text-sm text-gray-900">{formatDate(product.created_at)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last Updated</span>
                <span className="text-sm text-gray-900">{formatDate(product.updated_at)}</span>
              </div>
              {product.last_sync_at && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Last Synced</span>
                  <span className="text-sm text-gray-900">{formatDate(product.last_sync_at)}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductDetailsPage 
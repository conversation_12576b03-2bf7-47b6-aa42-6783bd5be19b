import { useLocation, Link } from 'react-router-dom'
import { UserPlus, Users, Setting<PERSON>, Clock, Bell, Shield, Layers, Package } from 'lucide-react'
import RolesManagement from '../components/users/RolesManagement'
import MyOrganizationUsers from '../components/users/MyOrganizationUsers'
import GuestOrganizationUsers from '../components/users/GuestOrganizationUsers'
import SectionHeader from '../components/SectionHeader'

const settingsTabs = [
  { id: 'general', name: 'General', icon: Settings, href: '/settings/general' },
  { id: 'jobs', name: 'Jobs', icon: Clock, href: '/settings/jobs' },
  { id: 'users', name: 'User Management', icon: Users, href: '/settings/users/my-organization/' },
  { id: 'organization', name: 'Organization', icon: UserPlus, href: '/settings/organization' },
  { id: 'custom-data', name: 'Custom Data', icon: Layers, href: '/settings/custom-data' },
  { id: 'notifications', name: 'Notifications', icon: Bell, href: '/settings/notifications' },
  { id: 'components', name: 'Components', icon: Package, href: '/settings/components' },
]

const MyOrganizationPage = () => {
  const location = useLocation()

  // Determine active tab from URL
  const getActiveTab = () => {
    if (location.pathname.includes('/guest-organization')) return 'guest'
    if (location.pathname.includes('/roles') || location.pathname.includes('/permissions')) return 'roles'
    return 'users'
  }
  const activeTab = getActiveTab()

  // Determine active settings tab
  const getActiveSettingsTab = () => {
    if (location.pathname.includes('/settings/general')) return 'general'
    if (location.pathname.includes('/settings/jobs')) return 'jobs'
    if (location.pathname.includes('/settings/users')) return 'users'
    if (location.pathname.includes('/settings/custom-data')) return 'custom-data'
    if (location.pathname.includes('/settings/notifications')) return 'notifications'
    if (location.pathname.includes('/settings/components')) return 'components'
    return 'users' // default for user management pages
  }
  const activeSettingsTab = getActiveSettingsTab()

  return (
    <div className="-m-6 min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar (copied from SettingsPage) */}
        <div className="w-72 bg-white border-r border-gray-200 min-h-screen">
          <div className="p-6 border-b border-gray-100">
            <SectionHeader
              icon={<Settings className="h-7 w-7" />}
              title="Settings"
              subtitle="Manage your application preferences and system configuration"
            />
          </div>
          <nav className="p-4 space-y-1">
            {settingsTabs.map((tab) => {
              const Icon = tab.icon
              return (
                <Link
                  key={tab.id}
                  to={tab.href}
                  className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                    activeSettingsTab === tab.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-3 ${
                    activeSettingsTab === tab.id ? 'text-blue-600' : 'text-gray-400'
                  }`} />
                  {tab.name}
                </Link>
              )
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <div className="p-8">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <SectionHeader
                icon={<Users className="h-7 w-7" />}
                title="User Management"
                subtitle="Manage users, roles, and permissions for your organization"
              />
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="-mb-px flex space-x-8">
                <Link
                  to="/settings/users/my-organization/"
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === 'users'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4" />
                    <span>My Organization</span>
                  </div>
                </Link>
                <Link
                  to="/settings/users/my-organization/roles"
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === 'roles'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <span>Roles & Permissions</span>
                  </div>
                </Link>
              </nav>
            </div>

            {/* Tab Content */}
            <div className="space-y-6">
              {activeTab === 'users' && <MyOrganizationUsers />}
              {activeTab === 'roles' && <RolesManagement />}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MyOrganizationPage

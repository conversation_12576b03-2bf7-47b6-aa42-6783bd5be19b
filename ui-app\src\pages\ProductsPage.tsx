import { useState, useMemo, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  Package,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  ChevronDown,
  ExternalLink,
  Copy,
  Archive
} from 'lucide-react'
import { ProductQueryParams } from '../types/product'
import SectionHeader from '../components/SectionHeader'

const ProductsPage = () => {
  const navigate = useNavigate()

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedChannel, setSelectedChannel] = useState('all')
  const [showFilters, setShowFilters] = useState(false)
  const [openActionMenu, setOpenActionMenu] = useState<string | null>(null)

  // Column visibility
  const [visibleColumns] = useState({
    image: true,
    name: true,
    sku: true,
    category: true,
    price: true,
    inventory: true,
    status: true,
    sync_status: true,
    channel: true,
    updated_at: true,
    actions: true
  })

  // Connected channels (this would come from settings/integrations)
  const [connectedChannels] = useState([
    { id: 'bigcommerce', name: 'BigCommerce', isConnected: true, icon: '🛒' },
    { id: 'shopify', name: 'Shopify', isConnected: true, icon: '🛍️' },
    { id: 'amazon', name: 'Amazon', isConnected: true, icon: '📦' },
    { id: 'ebay', name: 'eBay', isConnected: false, icon: '🏪' }
  ])

  // Mock data for testing with channel information
  const mockProducts = [
    {
      id: '1',
      name: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      price: 99.99,
      inventory_level: 45,
      is_visible: true,
      is_featured: true,
      sync_status: 'synced',
      updated_at: '2023-06-22T14:30:00Z',
      channel: 'bigcommerce'
    },
    {
      id: '2',
      name: 'Smart Fitness Watch',
      sku: 'SFW-002',
      price: 199.99,
      inventory_level: 23,
      is_visible: true,
      is_featured: false,
      sync_status: 'pending',
      updated_at: '2023-06-22T12:15:00Z',
      channel: 'shopify'
    },
    {
      id: '3',
      name: 'Organic Cotton T-Shirt',
      sku: 'OCT-003',
      price: 29.99,
      inventory_level: 0,
      is_visible: false,
      is_featured: false,
      sync_status: 'error',
      updated_at: '2023-06-21T16:45:00Z',
      channel: 'amazon'
    },
    {
      id: '4',
      name: 'Premium Coffee Maker',
      sku: 'PCM-004',
      price: 149.99,
      inventory_level: 12,
      is_visible: true,
      is_featured: true,
      sync_status: 'synced',
      updated_at: '2023-06-22T10:20:00Z',
      channel: 'bigcommerce'
    },
    {
      id: '5',
      name: 'Wireless Charging Pad',
      sku: 'WCP-005',
      price: 39.99,
      inventory_level: 67,
      is_visible: true,
      is_featured: false,
      sync_status: 'synced',
      updated_at: '2023-06-22T09:45:00Z',
      channel: 'shopify'
    }
  ]

  // Filter products based on selected channel
  const filteredProducts = useMemo(() => {
    if (selectedChannel === 'all') {
      return mockProducts
    }
    return mockProducts.filter(product => product.channel === selectedChannel)
  }, [selectedChannel])

  // Query parameters
  const queryParams: ProductQueryParams = useMemo(() => ({
    search: searchQuery || undefined,
    channel: selectedChannel === 'all' ? undefined : selectedChannel
  }), [searchQuery, selectedChannel])

  // Mock query - in real app this would fetch from API
  const { data: productsData, isLoading, error } = useQuery(
    ['products', queryParams],
    () => Promise.resolve({
      products: filteredProducts,
      total: filteredProducts.length,
      page: 1,
      limit: 20,
      total_pages: 1
    }),
    {
      keepPreviousData: true,
      staleTime: 30000
    }
  )

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getSyncStatusBadge = (status: string) => {
    const statusConfig = {
      synced: { color: 'bg-green-100 text-green-800', label: 'Synced' },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      error: { color: 'bg-red-100 text-red-800', label: 'Error' },
      not_synced: { color: 'bg-gray-100 text-gray-800', label: 'Not Synced' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.not_synced

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  const getChannelBadge = (channel: string) => {
    const channelConfig = connectedChannels.find(ch => ch.id === channel)
    if (!channelConfig) return null

    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        <span className="mr-1">{channelConfig.icon}</span>
        {channelConfig.name}
      </span>
    )
  }

  // Action handlers
  const handleViewProduct = (productId: string) => {
    navigate(`/products/view/${productId}`)
  }

  const handleEditProduct = (productId: string) => {
    navigate(`/products/edit/${productId}`)
  }

  const handleDeleteProduct = (productId: string) => {
    // TODO: Implement delete confirmation modal
    console.log('Delete product:', productId)
  }

  const handleDuplicateProduct = (productId: string) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate product:', productId)
  }

  const handleArchiveProduct = (productId: string) => {
    // TODO: Implement archive functionality
    console.log('Archive product:', productId)
  }

  const toggleActionMenu = (productId: string) => {
    setOpenActionMenu(openActionMenu === productId ? null : productId)
  }

  // Close action menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openActionMenu && !(event.target as Element).closest('.action-menu')) {
        setOpenActionMenu(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [openActionMenu])

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<Package className="h-7 w-7" />} 
        title="Products"
        subtitle="Manage all your products in one place"
        actions={
          <button
            onClick={() => navigate('/products/create')}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Product
          </button>
        }
      />

      {/* Place the channel tabs and search/filter controls outside the white card */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setSelectedChannel('all')}
              className={`${
                selectedChannel === 'all'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              All Products
              <span className={`${
                selectedChannel === 'all'
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-gray-100 text-gray-900'
              } ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium`}>
                {mockProducts.length}
              </span>
            </button>
            {connectedChannels
              .filter(channel => channel.isConnected)
              .map((channel) => (
                <button
                  key={channel.id}
                  onClick={() => setSelectedChannel(channel.id)}
                  className={`${
                    selectedChannel === channel.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                >
                  <span>{channel.icon}</span>
                  <span>{channel.name}</span>
                  <span className={`${
                    selectedChannel === channel.id
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-100 text-gray-900'
                  } py-0.5 px-2.5 rounded-full text-xs font-medium`}>
                    {mockProducts.filter(product => product.channel === channel.id).length}
                  </span>
                </button>
              ))}
          </nav>
        </div>
      </div>
      <div className="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 w-full">
        <div className="flex flex-col sm:flex-row gap-2 flex-1">
          {/* Search Bar */}
          <div className="relative w-full sm:w-auto flex-1 min-w-[200px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input pl-10 w-full sm:w-80"
            />
          </div>
          {/* Filters Button */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors ${
              showFilters
                ? 'border-blue-500 text-blue-600'
                : 'border-gray-300 text-gray-700 hover:border-gray-400'
            }`}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>
        <div className="flex items-center gap-2">
          {/* Refresh, Download, etc. */}
          <button className="p-2 text-gray-400 hover:text-gray-600">
            <RefreshCw className="h-5 w-5" />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600">
            <Download className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Table card only contains the table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SKU
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Inventory
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sync Status
                </th>
                {visibleColumns.channel && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Channel
                  </th>
                )}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Updated
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={visibleColumns.channel ? 8 : 7} className="px-6 py-12 text-center">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-3 text-gray-600">Loading products...</span>
                    </div>
                  </td>
                </tr>
              ) : filteredProducts.length === 0 ? (
                <tr>
                  <td colSpan={visibleColumns.channel ? 8 : 7} className="px-6 py-12 text-center">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p className="text-gray-600 mb-4">
                      {selectedChannel === 'all' 
                        ? 'Get started by creating your first product'
                        : `No products found in ${connectedChannels.find(ch => ch.id === selectedChannel)?.name}`
                      }
                    </p>
                    <button
                      onClick={() => navigate('/products/create')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Plus className="h-4 w-4 mr-2 inline" />
                      Create Product
                    </button>
                  </td>
                </tr>
              ) : (
                filteredProducts.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                            <Package className="h-5 w-5 text-gray-500" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <button
                            onClick={() => handleViewProduct(product.id)}
                            className="text-sm font-medium text-blue-600 hover:text-blue-900 hover:underline transition-colors text-left"
                          >
                            {product.name}
                          </button>
                          <div className="text-sm text-gray-500">
                            {product.is_visible ? 'Visible' : 'Hidden'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {product.sku || '-'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {formatPrice(product.price)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        product.inventory_level > 10
                          ? 'bg-green-100 text-green-800'
                          : product.inventory_level > 0
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {product.inventory_level} in stock
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      {getSyncStatusBadge(product.sync_status)}
                    </td>
                    {visibleColumns.channel && (
                      <td className="px-6 py-4">
                        {product.channel && getChannelBadge(product.channel)}
                      </td>
                    )}
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {formatDate(product.updated_at)}
                    </td>
                    <td className="px-6 py-4 text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        {/* Primary Actions - Icons Only */}
                        <button
                          onClick={() => handleViewProduct(product.id)}
                          className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-md transition-colors"
                          title="View Product"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        
                        <button
                          onClick={() => handleEditProduct(product.id)}
                          className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
                          title="Edit Product"
                        >
                          <Edit className="h-4 w-4" />
                        </button>

                        {/* More Actions Dropdown */}
                        <div className="relative action-menu">
                          <button
                            onClick={() => toggleActionMenu(product.id)}
                            className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
                            title="More Actions"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </button>

                          {/* Dropdown Menu */}
                          {openActionMenu === product.id && (
                            <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                              <div className="py-1">
                                <button
                                  onClick={() => {
                                    handleDuplicateProduct(product.id)
                                    setOpenActionMenu(null)
                                  }}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                >
                                  <Copy className="h-4 w-4 mr-3 text-gray-400" />
                                  Duplicate
                                </button>
                                
                                <button
                                  onClick={() => {
                                    handleArchiveProduct(product.id)
                                    setOpenActionMenu(null)
                                  }}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                >
                                  <Archive className="h-4 w-4 mr-3 text-gray-400" />
                                  Archive
                                </button>
                                
                                <button
                                  onClick={() => {
                                    // TODO: Implement sync functionality
                                    setOpenActionMenu(null)
                                  }}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                >
                                  <RefreshCw className="h-4 w-4 mr-3 text-gray-400" />
                                  Sync Now
                                </button>
                                
                                <button
                                  onClick={() => {
                                    // TODO: Implement external link functionality
                                    setOpenActionMenu(null)
                                  }}
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                >
                                  <ExternalLink className="h-4 w-4 mr-3 text-gray-400" />
                                  View on Store
                                </button>
                                
                                <div className="border-t border-gray-200 my-1"></div>
                                
                                <button
                                  onClick={() => {
                                    handleDeleteProduct(product.id)
                                    setOpenActionMenu(null)
                                  }}
                                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                >
                                  <Trash2 className="h-4 w-4 mr-3" />
                                  Delete
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default ProductsPage

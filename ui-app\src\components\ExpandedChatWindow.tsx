import React, { useState, useRef, useEffect } from 'react';
import { 
  MessageCircle, 
  X, 
  Plus, 
  Trash2, 
  Edit3, 
  Send,
  ChevronDown,
  ChevronUp,
  MoreVertical
} from 'lucide-react';
import { useChat, ChatConversation } from '../contexts/ChatContext';
import StructuredChatResponse from './StructuredChatResponse';

const ExpandedChatWindow: React.FC = () => {
  const { 
    isChatExpanded,
    collapseChat,
    conversations,
    currentConversation,
    switchConversation,
    createNewConversation,
    deleteConversation,
    renameConversation,
    sendMessage,
    isLoading
  } = useChat();

  const [inputValue, setInputValue] = useState('');
  const [isRenaming, setIsRenaming] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState('');
  const [showConversations, setShowConversations] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentConversation?.messages]);

  if (!isChatExpanded) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;
    
    await sendMessage(inputValue);
    setInputValue('');
  };

  const handleRename = (conversationId: string) => {
    if (renameValue.trim()) {
      renameConversation(conversationId, renameValue.trim());
    }
    setIsRenaming(null);
    setRenameValue('');
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return `${days} days ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl h-[80vh] flex">
        {/* Sidebar */}
        <div className="w-80 border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Chat History</h2>
              <button
                onClick={collapseChat}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* New Chat Button */}
          <div className="p-4">
            <button
              onClick={() => createNewConversation()}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>New Chat</span>
            </button>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4">
              <button
                onClick={() => setShowConversations(!showConversations)}
                className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                <span>Conversations ({conversations.length})</span>
                {showConversations ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </button>
            </div>
            
            {showConversations && (
              <div className="space-y-1 px-4 pb-4">
                {conversations.map((conversation) => (
                  <ConversationItem
                    key={conversation.id}
                    conversation={conversation}
                    isActive={currentConversation?.id === conversation.id}
                    onSelect={() => switchConversation(conversation.id)}
                    onDelete={() => deleteConversation(conversation.id)}
                    onRename={(name) => renameConversation(conversation.id, name)}
                    isRenaming={isRenaming === conversation.id}
                    onStartRename={() => {
                      setIsRenaming(conversation.id);
                      setRenameValue(conversation.name);
                    }}
                    onCancelRename={() => setIsRenaming(null)}
                    onConfirmRename={() => handleRename(conversation.id)}
                    renameValue={renameValue}
                    onRenameChange={setRenameValue}
                    formatDate={formatDate}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Header */}
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <MessageCircle className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-gray-900">
                  {currentConversation?.name || 'New Conversation'}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                {currentConversation && (
                  <span className="text-sm text-gray-500">
                    {currentConversation.messages.length} messages
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {currentConversation?.messages.length === 0 ? (
              <div className="text-center text-gray-500 mt-8">
                <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">Welcome to Nuclues Assistant</h3>
                <p className="text-sm">Ask me anything about your platform, products, orders, or settings!</p>
              </div>
            ) : (
              currentConversation?.messages.map((message) => (
                <MessageBubble key={message.id} message={message} />
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="p-4 border-t border-gray-200">
            <form onSubmit={handleSubmit} className="flex space-x-3">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoading}
              />
              <button
                type="submit"
                disabled={!inputValue.trim() || isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                <Send className="h-4 w-4" />
                <span>Send</span>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

// Conversation Item Component
interface ConversationItemProps {
  conversation: ChatConversation;
  isActive: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onRename: (name: string) => void;
  isRenaming: boolean;
  onStartRename: () => void;
  onCancelRename: () => void;
  onConfirmRename: () => void;
  renameValue: string;
  onRenameChange: (value: string) => void;
  formatDate: (date: Date) => string;
}

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  isActive,
  onSelect,
  onDelete,
  onRename,
  isRenaming,
  onStartRename,
  onCancelRename,
  onConfirmRename,
  renameValue,
  onRenameChange,
  formatDate
}) => {
  const [showMenu, setShowMenu] = useState(false);

  if (isRenaming) {
    return (
      <div className="p-2">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={renameValue}
            onChange={(e) => onRenameChange(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && onConfirmRename()}
            onKeyDown={(e) => e.key === 'Escape' && onCancelRename()}
            className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            autoFocus
          />
          <button
            onClick={onConfirmRename}
            className="text-xs text-blue-600 hover:text-blue-700"
          >
            Save
          </button>
          <button
            onClick={onCancelRename}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative group">
      <button
        onClick={onSelect}
        className={`w-full text-left p-2 rounded-lg transition-colors ${
          isActive
            ? 'bg-blue-50 text-blue-900 border border-blue-200'
            : 'hover:bg-gray-50 text-gray-700'
        }`}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium truncate">{conversation.name}</div>
            <div className="text-xs text-gray-500">{formatDate(conversation.updatedAt)}</div>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowMenu(!showMenu);
            }}
            className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-gray-600 transition-opacity"
          >
            <MoreVertical className="h-4 w-4" />
          </button>
        </div>
      </button>

      {showMenu && (
        <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onStartRename();
              setShowMenu(false);
            }}
            className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
          >
            <Edit3 className="h-3 w-3" />
            <span>Rename</span>
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
              setShowMenu(false);
            }}
            className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
          >
            <Trash2 className="h-3 w-3" />
            <span>Delete</span>
          </button>
        </div>
      )}
    </div>
  );
};

// Message Bubble Component
interface MessageBubbleProps {
  message: {
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    structuredData?: {
      type: 'customer' | 'product' | 'order' | 'revenue' | 'list';
      title: string;
      subtitle?: string;
      data: any;
      actions?: Array<{
        label: string;
        action: string;
        icon?: string;
      }>;
    };
  };
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  return (
    <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
      <div
        className={`max-w-2xl px-4 py-3 rounded-lg ${
          message.role === 'user'
            ? 'bg-blue-600 text-white'
            : 'bg-gray-100 text-gray-900'
        }`}
      >
        <div className="text-sm whitespace-pre-wrap">{message.content}</div>
        
        {/* Render structured data if available */}
        {message.structuredData && (
          <div className="mt-3">
            <StructuredChatResponse data={message.structuredData} />
          </div>
        )}
        
        <div className={`text-xs mt-1 ${
          message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
        }`}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      </div>
    </div>
  );
};

export default ExpandedChatWindow; 
import React from 'react'
import { useQuery } from 'react-query'
import {
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  Home,
  DollarSign,
  ShoppingCart,
  Package,
  Activity,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'
import SectionHeader from '../components/SectionHeader'

const HomePage = () => {
  // Mock health check instead of real API call
  const { } = useQuery(
    'health',
    () => Promise.resolve({ status: 'ok', message: 'Mock health check successful' }),
    {
      refetchInterval: 30000,
      retry: false,
      refetchOnWindowFocus: false
    }
  )

  // Mock dashboard data
  const dashboardData = {
    metrics: [
      {
        title: 'Total Revenue',
        value: '$45,231.89',
        change: '+12.5%',
        trend: 'up',
        period: 'vs last month'
      },
      {
        title: 'Orders',
        value: '356',
        change: '+8.2%',
        trend: 'up',
        period: 'vs last month'
      },
      {
        title: 'Conversion Rate',
        value: '3.2%',
        change: '+1.1%',
        trend: 'up',
        period: 'vs last month'
      },
      {
        title: 'Products',
        value: '1,423',
        change: '-2.3%',
        trend: 'down',
        period: 'vs last month'
      }
    ],
    recentOrders: [
      { id: 'ORD-1001', customer: '<PERSON> Doe', date: '2023-06-17', total: '$570.50', status: 'Completed' },
      { id: 'ORD-1002', customer: 'Jane Smith', date: '2023-06-21', total: '$125.00', status: 'Processing' },
      { id: 'ORD-1003', customer: 'Bob Johnson', date: '2023-06-22', total: '$89.99', status: 'Shipped' },
    ]
  }



  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
    switch (status) {
      case 'Completed':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'Processing':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'Shipped':
        return `${baseClasses} bg-blue-100 text-blue-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  return (
    <div className="space-y-6">
      <SectionHeader
        icon={<Home className="h-7 w-7" />}
        title="Dashboard"
        subtitle="Welcome back, here's an overview of your business."
      />

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardData.metrics.map((metric, index) => {
          const icons = [DollarSign, ShoppingCart, Activity, Package]
          const Icon = icons[index]

          return (
            <div key={metric.title} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-2">{metric.value}</p>
                </div>
                <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                  <Icon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                {metric.trend === 'up' ? (
                  <ArrowUpRight className="h-4 w-4 text-green-500" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 text-red-500" />
                )}
                <span className={`text-sm font-medium ml-1 ${
                  metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {metric.change}
                </span>
                <span className="text-sm text-gray-500 ml-1">{metric.period}</span>
              </div>
            </div>
          )
        })}
      </div>

      {/* Charts and Tables Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Overview Chart */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Sales Overview</h3>
            <div className="flex space-x-2">
              <button className="px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">Week</button>
              <button className="px-3 py-1 text-sm font-medium text-gray-500 hover:text-gray-700">Month</button>
              <button className="px-3 py-1 text-sm font-medium text-gray-500 hover:text-gray-700">Year</button>
            </div>
          </div>

          {/* Simple chart placeholder */}
          <div className="h-64 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 text-blue-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Chart visualization</p>
              <p className="text-xs text-gray-500">Sales trending upward</p>
            </div>
          </div>
        </div>

        {/* Integration Status */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Integration Status</h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">Shopify</p>
                  <p className="text-xs text-gray-500">Last sync: 2 minutes ago</p>
                </div>
              </div>
              <span className="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">
                Connected
              </span>
            </div>

            <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">BigCommerce</p>
                  <p className="text-xs text-gray-500">Sync paused</p>
                </div>
              </div>
              <span className="text-xs font-medium text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full">
                Paused
              </span>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center">
                  <XCircle className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">WooCommerce</p>
                  <p className="text-xs text-gray-500">Not connected</p>
                </div>
              </div>
              <span className="text-xs font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
                Available
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Orders Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Recent Orders</h3>
            <div className="flex space-x-2">
              <button className="px-3 py-1 text-sm font-medium text-gray-500 hover:text-gray-700">Recent Orders</button>
              <button className="px-3 py-1 text-sm font-medium text-gray-500 hover:text-gray-700">Low Stock Items</button>
              <button className="px-3 py-1 text-sm font-medium text-gray-500 hover:text-gray-700">Performance</button>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {dashboardData.recentOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {order.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {order.customer}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {order.date}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {order.total}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getStatusBadge(order.status)}>
                      {order.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default HomePage

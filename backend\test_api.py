#!/usr/bin/env python3
"""
Simple API test script for PIM API

This script tests the basic functionality of the PIM API endpoints.
"""

import requests
import json
import sys
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            print(f"   Database: {data['database']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_create_product():
    """Test creating a product"""
    print("\n📦 Testing product creation...")
    
    product_data = {
        "name": "Test Product",
        "product_type": "physical",
        "sku": f"TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "description": "This is a test product created by the test script",
        "price": 29.99,
        "cost_price": 15.00,
        "inventory_level": 100,
        "is_visible": True,
        "is_featured": False,
        "weight": 1.5,
        "categories": [{"id": "1", "name": "Test Category"}],
        "custom_fields": [{"name": "test_field", "value": "test_value"}]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/products",
            json=product_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            product = response.json()
            print(f"✅ Product created successfully!")
            print(f"   ID: {product['id']}")
            print(f"   Name: {product['name']}")
            print(f"   SKU: {product['sku']}")
            return product['id']
        else:
            print(f"❌ Product creation failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Product creation error: {e}")
        return None

def test_get_products():
    """Test getting products list"""
    print("\n📋 Testing products list...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/products?limit=5")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Products list retrieved successfully!")
            print(f"   Total products: {data['total']}")
            print(f"   Page: {data['page']}")
            print(f"   Products on this page: {len(data['products'])}")
            return True
        else:
            print(f"❌ Products list failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Products list error: {e}")
        return False

def test_get_product(product_id):
    """Test getting a specific product"""
    if not product_id:
        print("\n⏭️  Skipping product details test (no product ID)")
        return False
        
    print(f"\n🔍 Testing product details for ID: {product_id}")
    
    try:
        response = requests.get(f"{BASE_URL}/api/products/{product_id}")
        
        if response.status_code == 200:
            product = response.json()
            print(f"✅ Product details retrieved successfully!")
            print(f"   Name: {product['name']}")
            print(f"   Price: ${product['price']}")
            print(f"   View count: {product['view_count']}")
            return True
        else:
            print(f"❌ Product details failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Product details error: {e}")
        return False

def test_update_product(product_id):
    """Test updating a product"""
    if not product_id:
        print("\n⏭️  Skipping product update test (no product ID)")
        return False
        
    print(f"\n✏️  Testing product update for ID: {product_id}")
    
    update_data = {
        "name": "Updated Test Product",
        "price": 39.99,
        "is_featured": True
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/api/products/{product_id}",
            json=update_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            product = response.json()
            print(f"✅ Product updated successfully!")
            print(f"   New name: {product['name']}")
            print(f"   New price: ${product['price']}")
            print(f"   Featured: {product['is_featured']}")
            return True
        else:
            print(f"❌ Product update failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Product update error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting PIM API Tests...")
    print(f"   Base URL: {BASE_URL}")
    print("=" * 50)
    
    # Test health endpoint
    if not test_health():
        print("\n❌ API is not healthy. Please check if the server is running.")
        sys.exit(1)
    
    # Test product creation
    product_id = test_create_product()
    
    # Test products list
    test_get_products()
    
    # Test product details
    test_get_product(product_id)
    
    # Test product update
    test_update_product(product_id)
    
    print("\n" + "=" * 50)
    print("🎉 API tests completed!")
    print("\n📖 Next steps:")
    print("1. Visit API docs: http://localhost:8000/docs")
    print("2. Test with frontend: http://localhost:5173")

if __name__ == "__main__":
    main()

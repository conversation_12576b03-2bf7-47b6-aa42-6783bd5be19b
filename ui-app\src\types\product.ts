// Product management types

export interface Product {
  id: string
  name: string
  product_type: string // physical, digital
  sku?: string
  description?: string
  weight?: number
  width?: number
  depth?: number
  height?: number
  price: number
  cost_price?: number
  retail_price?: number
  sale_price?: number
  map_price?: number
  tax_class_id?: number
  product_tax_code?: string
  brand_id?: number
  brand_name?: string
  inventory_level: number
  inventory_warning_level?: number
  inventory_tracking: string // none, simple, variant
  fixed_cost_shipping_price?: number
  is_free_shipping: boolean
  is_visible: boolean
  is_featured: boolean
  warranty?: string
  bin_picking_number?: string
  layout_file?: string
  upc?: string
  search_keywords?: string
  availability_description?: string
  availability: string // available, disabled, preorder
  gift_wrapping_options_type: string // any, none, list
  sort_order?: number
  condition: string // New, Used, Refurbished
  is_condition_shown: boolean
  order_quantity_minimum?: number
  order_quantity_maximum?: number
  page_title?: string
  meta_description?: string
  view_count: number
  preorder_release_date?: string
  preorder_message?: string
  is_preorder_only: boolean
  is_price_hidden: boolean
  price_hidden_label?: string
  open_graph_type: string // product, article, website
  open_graph_title?: string
  open_graph_description?: string
  open_graph_use_meta_description: boolean
  open_graph_use_product_name: boolean
  open_graph_use_image: boolean
  gtin?: string
  mpn?: string
  date_last_imported?: string
  reviews_rating_sum?: number
  reviews_count?: number
  total_sold: number
  bigcommerce_id?: number
  sync_status: string // synced, pending, error, not_synced
  last_sync_at?: string
  created_at: string
  updated_at: string
  channel?: string // Channel identifier (bigcommerce, shopify, amazon, etc.)
  categories: ProductCategory[]
  images: ProductImage[]
  videos: ProductVideo[]
  variants: ProductVariant[]
  custom_fields: ProductCustomField[]
  bulk_pricing_rules: BulkPricingRule[]
}

export interface ProductListItem {
  id: string
  name: string
  sku?: string
  price: number
  inventory_level: number
  is_visible: boolean
  is_featured: boolean
  sync_status: string
  last_sync_at?: string
  created_at: string
  updated_at: string
  channel?: string // Channel identifier (bigcommerce, shopify, amazon, etc.)
  category?: string // Added for UI filtering and mock data
  brand?: string // Added for UI filtering and mock data
}

export interface ProductImage {
  id: string
  image_url: string
  is_thumbnail: boolean
  sort_order: number
  description?: string
  created_at: string
}

export interface ProductVideo {
  id: string
  title: string
  description?: string
  sort_order: number
  video_type: string // youtube, vimeo, mp4
  video_id: string
  length?: string
  created_at: string
}

export interface ProductVariant {
  id: string
  sku?: string
  cost_price?: number
  price?: number
  sale_price?: number
  retail_price?: number
  map_price?: number
  weight?: number
  width?: number
  height?: number
  depth?: number
  is_free_shipping: boolean
  fixed_cost_shipping_price?: number
  purchasing_disabled: boolean
  purchasing_disabled_message?: string
  image_url?: string
  upc?: string
  inventory_level: number
  inventory_warning_level?: number
  bin_picking_number?: string
  mpn?: string
  gtin?: string
  calculated_price?: number
  calculated_weight?: number
  created_at: string
  option_values: VariantOptionValue[]
}

export interface ProductCategory {
  id: string
  name: string
  description?: string
  parent_id?: string
  sort_order: number
  is_visible: boolean
  created_at: string
}

export interface ProductCustomField {
  id: string
  name: string
  value: string
  created_at: string
}

export interface BulkPricingRule {
  id: string
  quantity_min: number
  quantity_max?: number
  rule_type: string // price, percent, fixed
  amount: number
  created_at: string
}

export interface VariantOptionValue {
  id: string
  option_id: number
  option_display_name: string
  label: string
  created_at: string
}

export interface CreateProductRequest {
  name: string
  product_type: string
  sku?: string
  description?: string
  weight?: number
  width?: number
  depth?: number
  height?: number
  price: number
  cost_price?: number
  retail_price?: number
  sale_price?: number
  map_price?: number
  tax_class_id?: number
  product_tax_code?: string
  brand_id?: number
  brand_name?: string
  inventory_level: number
  inventory_warning_level?: number
  inventory_tracking: string
  fixed_cost_shipping_price?: number
  is_free_shipping: boolean
  is_visible: boolean
  is_featured: boolean
  warranty?: string
  bin_picking_number?: string
  layout_file?: string
  upc?: string
  search_keywords?: string
  availability_description?: string
  availability: string
  gift_wrapping_options_type: string
  sort_order?: number
  condition: string
  is_condition_shown: boolean
  order_quantity_minimum?: number
  order_quantity_maximum?: number
  page_title?: string
  meta_description?: string
  preorder_release_date?: string
  preorder_message?: string
  is_preorder_only: boolean
  is_price_hidden: boolean
  price_hidden_label?: string
  open_graph_type: string
  open_graph_title?: string
  open_graph_description?: string
  open_graph_use_meta_description: boolean
  open_graph_use_product_name: boolean
  open_graph_use_image: boolean
  gtin?: string
  mpn?: string
  categories?: string[]
  images?: CreateProductImageRequest[]
  videos?: CreateProductVideoRequest[]
  variants?: CreateProductVariantRequest[]
  custom_fields?: CreateCustomFieldRequest[]
  bulk_pricing_rules?: CreateBulkPricingRuleRequest[]
  status?: 'active' | 'inactive' | 'draft'
}

export interface CreateProductImageRequest {
  image_url: string
  is_thumbnail: boolean
  sort_order: number
  description?: string
}

export interface CreateProductVideoRequest {
  title: string
  description?: string
  sort_order: number
  video_type: string
  video_id: string
  length?: string
}

export interface CreateProductVariantRequest {
  sku?: string
  cost_price?: number
  price?: number
  sale_price?: number
  retail_price?: number
  map_price?: number
  weight?: number
  width?: number
  height?: number
  depth?: number
  is_free_shipping: boolean
  fixed_cost_shipping_price?: number
  purchasing_disabled: boolean
  purchasing_disabled_message?: string
  image_url?: string
  upc?: string
  inventory_level: number
  inventory_warning_level?: number
  bin_picking_number?: string
  mpn?: string
  gtin?: string
  option_values?: CreateVariantOptionValueRequest[]
}

export interface CreateVariantOptionValueRequest {
  option_id: number
  option_display_name: string
  label: string
}

export interface CreateCustomFieldRequest {
  name: string
  value: string
}

export interface CreateBulkPricingRuleRequest {
  quantity_min: number
  quantity_max?: number
  rule_type: string // price, percent, fixed
  amount: number
  type?: 'fixed' | 'percentage' // UI only: for rule entry, not persisted to backend
}

export interface UpdateProductRequest {
  name?: string
  product_type?: string
  sku?: string
  description?: string
  weight?: number
  width?: number
  depth?: number
  height?: number
  price?: number
  cost_price?: number
  retail_price?: number
  sale_price?: number
  map_price?: number
  tax_class_id?: number
  product_tax_code?: string
  brand_id?: number
  brand_name?: string
  inventory_level?: number
  inventory_warning_level?: number
  inventory_tracking?: string
  fixed_cost_shipping_price?: number
  is_free_shipping?: boolean
  is_visible?: boolean
  is_featured?: boolean
  warranty?: string
  bin_picking_number?: string
  layout_file?: string
  upc?: string
  search_keywords?: string
  availability_description?: string
  availability?: string
  gift_wrapping_options_type?: string
  sort_order?: number
  condition?: string
  is_condition_shown?: boolean
  order_quantity_minimum?: number
  order_quantity_maximum?: number
  page_title?: string
  meta_description?: string
  preorder_release_date?: string
  preorder_message?: string
  is_preorder_only?: boolean
  is_price_hidden?: boolean
  price_hidden_label?: string
  open_graph_type?: string
  open_graph_title?: string
  open_graph_description?: string
  open_graph_use_meta_description?: boolean
  open_graph_use_product_name?: boolean
  open_graph_use_image?: boolean
  gtin?: string
  mpn?: string
  status?: 'active' | 'inactive' | 'draft'
}

export interface ProductQueryParams {
  page?: number
  limit?: number
  search?: string
  category?: string
  is_visible?: boolean
  is_featured?: boolean
  sync_status?: string
  channel?: string // Filter by channel (bigcommerce, shopify, amazon, etc.)
  sort_by?: string
  sort_order?: string
}

export interface ProductListResult {
  products: ProductListItem[]
  total: number
  page: number
  limit: number
  total_pages: number
}

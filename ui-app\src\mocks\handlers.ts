import { http, HttpResponse } from 'msw';
import { v4 as uuidv4 } from 'uuid';
import { MediaItem, CreateMediaItemRequest, UpdateMediaItemRequest, MediaShareRequest } from '../types/media';

// Preloaded mock data: 3-level folder structure and files
const folder1 = uuidv4();
const folder2 = uuidv4();
const folder3 = uuidv4();
const folder4 = uuidv4();
const folder5 = uuidv4();
const folder6 = uuidv4();
const file1 = uuidv4();
const file2 = uuidv4();
const file3 = uuidv4();
const file4 = uuidv4();
const file5 = uuidv4();

let mediaItems: MediaItem[] = [
  {
    id: 'root',
    name: 'My Drive',
    item_type: 'folder',
    parent_id: null,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: undefined,
    mime_type: undefined,
    thumbnail_url: undefined,
    download_url: undefined,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  // Level 1 folders
  {
    id: folder1,
    name: 'Projects',
    item_type: 'folder',
    parent_id: 'root',
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: undefined,
    mime_type: undefined,
    thumbnail_url: undefined,
    download_url: undefined,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  {
    id: folder2,
    name: 'Designs',
    item_type: 'folder',
    parent_id: 'root',
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: undefined,
    mime_type: undefined,
    thumbnail_url: undefined,
    download_url: undefined,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  // Level 2 folders
  {
    id: folder3,
    name: '2024',
    item_type: 'folder',
    parent_id: folder1,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: undefined,
    mime_type: undefined,
    thumbnail_url: undefined,
    download_url: undefined,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  {
    id: folder4,
    name: '2023',
    item_type: 'folder',
    parent_id: folder1,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: undefined,
    mime_type: undefined,
    thumbnail_url: undefined,
    download_url: undefined,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  // Level 3 folders
  {
    id: folder5,
    name: 'Q1',
    item_type: 'folder',
    parent_id: folder3,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: undefined,
    mime_type: undefined,
    thumbnail_url: undefined,
    download_url: undefined,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  {
    id: folder6,
    name: 'Q2',
    item_type: 'folder',
    parent_id: folder3,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: undefined,
    mime_type: undefined,
    thumbnail_url: undefined,
    download_url: undefined,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  // Files in various folders
  {
    id: file1,
    name: 'ProjectPlan.pdf',
    item_type: 'file',
    parent_id: folder1,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: 102400,
    mime_type: 'application/pdf',
    thumbnail_url: undefined,
    download_url: `/api/media/items/${file1}/download`,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  {
    id: file2,
    name: 'Wireframe.png',
    item_type: 'file',
    parent_id: folder2,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: 204800,
    mime_type: 'image/png',
    thumbnail_url: undefined,
    download_url: `/api/media/items/${file2}/download`,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  {
    id: file3,
    name: 'Q1_Report.docx',
    item_type: 'file',
    parent_id: folder5,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: 51200,
    mime_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    thumbnail_url: undefined,
    download_url: `/api/media/items/${file3}/download`,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  {
    id: file4,
    name: 'Q2_Design.sketch',
    item_type: 'file',
    parent_id: folder6,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: 256000,
    mime_type: 'application/octet-stream',
    thumbnail_url: undefined,
    download_url: `/api/media/items/${file4}/download`,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
  {
    id: file5,
    name: '2023_Summary.xlsx',
    item_type: 'file',
    parent_id: folder4,
    created_by: 'mock-user',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    size: 128000,
    mime_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    thumbnail_url: undefined,
    download_url: `/api/media/items/${file5}/download`,
    is_shared: false,
    shared_with: [],
    permissions: [],
    metadata: {},
    children: [],
  },
];

export const handlers = [
  // Get media items
  http.get('/api/media/items', ({ request }) => {
    const url = new URL(request.url);
    const parent_id = url.searchParams.get('parent_id');
    const item_type = url.searchParams.get('item_type');
    let items = mediaItems;
    if (parent_id !== null) {
      items = items.filter(item => (parent_id ? item.parent_id === parent_id : !item.parent_id));
    }
    if (item_type) {
      items = items.filter(item => item.item_type === item_type);
    }
    return HttpResponse.json(items);
  }),

  // Create folder
  http.post('/api/media/folders', async ({ request }) => {
    const body = (await request.json()) as CreateMediaItemRequest;
    const newFolder: MediaItem = {
      id: uuidv4(),
      name: body.name,
      item_type: 'folder',
      parent_id: body.parent_id,
      created_by: 'mock-user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      size: undefined,
      mime_type: undefined,
      thumbnail_url: undefined,
      download_url: undefined,
      is_shared: false,
      shared_with: [],
      permissions: [],
      metadata: {},
      children: [],
    };
    mediaItems.push(newFolder);
    return HttpResponse.json(newFolder);
  }),

  // Create file (metadata)
  http.post('/api/media/files', async ({ request }) => {
    const body = (await request.json()) as CreateMediaItemRequest;
    const newFile: MediaItem = {
      id: uuidv4(),
      name: body.name,
      item_type: 'file',
      parent_id: body.parent_id,
      created_by: 'mock-user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      size: 12345,
      mime_type: 'application/octet-stream',
      thumbnail_url: undefined,
      download_url: `/api/media/items/${uuidv4()}/download`,
      is_shared: false,
      shared_with: [],
      permissions: [],
      metadata: {},
      children: [],
    };
    mediaItems.push(newFile);
    return HttpResponse.json(newFile);
  }),

  // Delete item
  http.delete('/api/media/items/:id', ({ params }) => {
    const { id } = params;
    mediaItems = mediaItems.filter(item => item.id !== id);
    return HttpResponse.json({ message: 'Deleted' });
  }),

  // Update item
  http.put('/api/media/items/:id', async ({ params, request }) => {
    const { id } = params;
    const body = (await request.json()) as UpdateMediaItemRequest;
    mediaItems = mediaItems.map(item =>
      item.id === id ? { ...item, ...body, updated_at: new Date().toISOString() } : item
    );
    return HttpResponse.json({ message: 'Updated' });
  }),

  // Share item
  http.post('/api/media/items/:id/share', () => {
    // No-op for mock
    return HttpResponse.json({ message: 'Shared' });
  }),

  // Download file (mock)
  http.get('/api/media/items/:id/download', () => {
    return new HttpResponse('Mock file content', { status: 200 });
  }),
]; 
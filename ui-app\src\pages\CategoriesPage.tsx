import React, { useState, useEffect, useMemo } from 'react'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Eye, 
  Folder,
  ChevronDown,
  ChevronUp,
  Download,
  RefreshCw,
  Archive,
  Copy,
  ExternalLink,
  AlertTriangle,
  CheckCircle,
  X,
  Package,
  BarChart2
} from 'lucide-react'
import { toast } from 'react-hot-toast'
import SectionHeader from '../components/SectionHeader'

interface Category {
  id: string
  name: string
  description: string
  parentCategory?: string
  productCount: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  slug?: string
  imageUrl?: string
  sortOrder?: number
}

interface SortConfig {
  key: keyof Category
  direction: 'asc' | 'desc'
}

const CategoriesPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  const [levelFilter, setLevelFilter] = useState<'all' | 'root' | 'child'>('all')
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'name', direction: 'asc' })
  const [openActionMenu, setOpenActionMenu] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  // Mock data for demonstration
  useEffect(() => {
    const mockCategories: Category[] = [
      {
        id: '1',
        name: 'Electronics',
        description: 'Electronic devices and accessories including smartphones, laptops, and smart home devices',
        productCount: 245,
        status: 'active',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
        slug: 'electronics',
        sortOrder: 1
      },
      {
        id: '2',
        name: 'Smartphones',
        description: 'Mobile phones and smartphone accessories',
        parentCategory: '1',
        productCount: 89,
        status: 'active',
        createdAt: '2024-01-16',
        updatedAt: '2024-01-21',
        slug: 'electronics-smartphones',
        sortOrder: 2
      },
      {
        id: '3',
        name: 'Laptops',
        description: 'Portable computers and laptop accessories',
        parentCategory: '1',
        productCount: 67,
        status: 'active',
        createdAt: '2024-01-17',
        updatedAt: '2024-01-22',
        slug: 'electronics-laptops',
        sortOrder: 3
      },
      {
        id: '4',
        name: 'Clothing & Fashion',
        description: 'Apparel and fashion items for men, women, and children',
        productCount: 189,
        status: 'active',
        createdAt: '2024-01-10',
        updatedAt: '2024-01-18',
        slug: 'clothing-fashion',
        sortOrder: 4
      },
      {
        id: '5',
        name: 'Men\'s Clothing',
        description: 'Clothing specifically for men',
        parentCategory: '4',
        productCount: 78,
        status: 'active',
        createdAt: '2024-01-11',
        updatedAt: '2024-01-19',
        slug: 'clothing-fashion-mens',
        sortOrder: 5
      },
      {
        id: '6',
        name: 'Women\'s Clothing',
        description: 'Clothing specifically for women',
        parentCategory: '4',
        productCount: 92,
        status: 'active',
        createdAt: '2024-01-12',
        updatedAt: '2024-01-20',
        slug: 'clothing-fashion-womens',
        sortOrder: 6
      },
      {
        id: '7',
        name: 'Home & Garden',
        description: 'Home improvement and garden supplies for DIY enthusiasts',
        productCount: 156,
        status: 'active',
        createdAt: '2024-01-05',
        updatedAt: '2024-01-15',
        slug: 'home-garden',
        sortOrder: 7
      },
      {
        id: '8',
        name: 'Kitchen & Dining',
        description: 'Kitchen appliances and dining accessories',
        parentCategory: '7',
        productCount: 45,
        status: 'active',
        createdAt: '2024-01-06',
        updatedAt: '2024-01-16',
        slug: 'home-garden-kitchen',
        sortOrder: 8
      },
      {
        id: '9',
        name: 'Sports & Outdoors',
        description: 'Sports equipment and outdoor gear for active lifestyles',
        productCount: 98,
        status: 'active',
        createdAt: '2024-01-12',
        updatedAt: '2024-01-16',
        slug: 'sports-outdoors',
        sortOrder: 9
      },
      {
        id: '10',
        name: 'Books & Media',
        description: 'Books, movies, music, and digital media content',
        productCount: 203,
        status: 'active',
        createdAt: '2024-01-08',
        updatedAt: '2024-01-14',
        slug: 'books-media',
        sortOrder: 10
      },
      {
        id: '11',
        name: 'Health & Beauty',
        description: 'Health products, beauty supplies, and personal care items',
        productCount: 134,
        status: 'inactive',
        createdAt: '2024-01-03',
        updatedAt: '2024-01-12',
        slug: 'health-beauty',
        sortOrder: 11
      },
      {
        id: '12',
        name: 'Automotive',
        description: 'Car parts, accessories, and automotive maintenance products',
        productCount: 87,
        status: 'active',
        createdAt: '2024-01-20',
        updatedAt: '2024-01-22',
        slug: 'automotive',
        sortOrder: 12
      },
      {
        id: '13',
        name: 'Toys & Games',
        description: 'Children toys, board games, and entertainment products',
        productCount: 112,
        status: 'active',
        createdAt: '2024-01-18',
        updatedAt: '2024-01-21',
        slug: 'toys-games',
        sortOrder: 13
      }
    ]
    
    setTimeout(() => {
      setCategories(mockCategories)
      setLoading(false)
    }, 1000)
  }, [])

  // Build hierarchical structure
  const buildHierarchy = (categories: Category[]) => {
    const categoryMap = new Map<string, Category>()
    const rootCategories: Category[] = []
    const childCategories: Category[] = []

    // Create a map for quick lookup
    categories.forEach(cat => {
      categoryMap.set(cat.id, cat)
      if (cat.parentCategory) {
        childCategories.push(cat)
      } else {
        rootCategories.push(cat)
      }
    })

    // Sort root categories
    rootCategories.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))

    return { categoryMap, rootCategories, childCategories }
  }

  // Get category path (breadcrumb)
  const getCategoryPath = (category: Category, categoryMap: Map<string, Category>): string[] => {
    const path: string[] = [category.name]
    let currentCategory = category

    while (currentCategory.parentCategory && categoryMap.has(currentCategory.parentCategory)) {
      currentCategory = categoryMap.get(currentCategory.parentCategory)!
      path.unshift(currentCategory.name)
    }

    return path
  }

  // Get category level (depth)
  const getCategoryLevel = (category: Category, categoryMap: Map<string, Category>): number => {
    let level = 0
    let currentCategory = category

    while (currentCategory.parentCategory && categoryMap.has(currentCategory.parentCategory)) {
      level++
      currentCategory = categoryMap.get(currentCategory.parentCategory)!
    }

    return level
  }

  // Get all children of a category
  const getCategoryChildren = (categoryId: string): Category[] => {
    return categories.filter(cat => cat.parentCategory === categoryId)
  }

  // Check if category has children
  const hasChildren = (categoryId: string): boolean => {
    return categories.some(cat => cat.parentCategory === categoryId)
  }

  // Filter and sort categories with hierarchy
  const filteredAndSortedCategories = useMemo(() => {
    const { categoryMap, rootCategories, childCategories } = buildHierarchy(categories)
    
    let filtered = categories.filter(category => {
      const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          category.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === 'all' || category.status === statusFilter
      
      // Level filter
      let matchesLevel = true
      if (levelFilter !== 'all') {
        const { categoryMap } = buildHierarchy(categories)
        const level = getCategoryLevel(category, categoryMap)
        if (levelFilter === 'root') {
          matchesLevel = level === 0
        } else if (levelFilter === 'child') {
          matchesLevel = level > 0
        }
      }
      
      return matchesSearch && matchesStatus && matchesLevel
    })

    // Sort categories hierarchically
    filtered.sort((a, b) => {
      // First sort by hierarchy level
      const aLevel = getCategoryLevel(a, categoryMap)
      const bLevel = getCategoryLevel(b, categoryMap)
      
      if (aLevel !== bLevel) {
        return aLevel - bLevel
      }
      
      // Then sort by the selected sort config
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue)
        return sortConfig.direction === 'asc' ? comparison : -comparison
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue
      }
      
      return 0
    })

    return filtered
  }, [categories, searchTerm, statusFilter, levelFilter, sortConfig])

  // Handle sorting
  const handleSort = (key: keyof Category) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }))
  }

  // Get sort icon
  const getSortIcon = (key: keyof Category) => {
    if (sortConfig.key !== key) {
      return <ChevronDown className="h-4 w-4 text-gray-400" />
    }
    return sortConfig.direction === 'asc' 
      ? <ChevronUp className="h-4 w-4 text-blue-600" />
      : <ChevronDown className="h-4 w-4 text-blue-600" />
  }

  const handleSelectAll = () => {
    if (selectedCategories.length === filteredAndSortedCategories.length) {
      setSelectedCategories([])
    } else {
      setSelectedCategories(filteredAndSortedCategories.map(cat => cat.id))
    }
  }

  const handleSelectCategory = (categoryId: string) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    )
  }

  const handleDeleteCategories = async () => {
    if (selectedCategories.length === 0) {
      toast.error('Please select categories to delete')
      return
    }
    
    setIsDeleting(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setCategories(prev => prev.filter(cat => !selectedCategories.includes(cat.id)))
      setSelectedCategories([])
      setShowDeleteConfirm(false)
      toast.success(`${selectedCategories.length} categories deleted successfully`)
    } catch (error) {
      toast.error('Failed to delete categories')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleBulkAction = (action: string) => {
    if (selectedCategories.length === 0) {
      toast.error('Please select categories first')
      return
    }

    switch (action) {
      case 'activate':
        setCategories(prev => prev.map(cat => 
          selectedCategories.includes(cat.id) ? { ...cat, status: 'active' as const } : cat
        ))
        toast.success(`${selectedCategories.length} categories activated`)
        break
      case 'deactivate':
        setCategories(prev => prev.map(cat => 
          selectedCategories.includes(cat.id) ? { ...cat, status: 'inactive' as const } : cat
        ))
        toast.success(`${selectedCategories.length} categories deactivated`)
        break
      case 'archive':
        toast.success(`${selectedCategories.length} categories archived`)
        break
      default:
        break
    }
    setSelectedCategories([])
  }

  const toggleActionMenu = (categoryId: string) => {
    setOpenActionMenu(openActionMenu === categoryId ? null : categoryId)
  }

  // Close action menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openActionMenu && !(event.target as Element).closest('.action-menu')) {
        setOpenActionMenu(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [openActionMenu])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading categories...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<Folder className="h-7 w-7" />}
        title="Categories"
        subtitle="Manage your product categories and organize your catalog hierarchically"
        actions={
          <div className="flex items-center space-x-3">
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </button>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              Add Category
            </button>
          </div>
        }
      />

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Folder className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-600">Total Categories</div>
              <div className="text-2xl font-bold text-gray-900">{categories.length}</div>
              <div className="text-xs text-gray-500">
                {(() => {
                  const { rootCategories, childCategories } = buildHierarchy(categories)
                  return `${rootCategories.length} root, ${childCategories.length} subcategories`
                })()}
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-600">Active Categories</div>
              <div className="text-2xl font-bold text-green-600">
                {categories.filter(cat => cat.status === 'active').length}
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Package className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-600">Total Products</div>
              <div className="text-2xl font-bold text-purple-600">
                {categories.reduce((sum, cat) => sum + cat.productCount, 0).toLocaleString()}
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BarChart2 className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-600">Avg Products/Category</div>
              <div className="text-2xl font-bold text-orange-600">
                {Math.round(categories.reduce((sum, cat) => sum + cat.productCount, 0) / categories.length)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search categories by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium transition-colors ${
                showFilters 
                  ? 'border-blue-500 text-blue-700 bg-blue-50' 
                  : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
              }`}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
            {selectedCategories.length > 0 && (
              <>
                <button
                  onClick={() => handleBulkAction('activate')}
                  className="inline-flex items-center px-3 py-2 border border-green-300 rounded-md text-sm font-medium text-green-700 bg-white hover:bg-green-50 transition-colors"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Activate ({selectedCategories.length})
                </button>
                <button
                  onClick={() => handleBulkAction('deactivate')}
                  className="inline-flex items-center px-3 py-2 border border-yellow-300 rounded-md text-sm font-medium text-yellow-700 bg-white hover:bg-yellow-50 transition-colors"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Deactivate ({selectedCategories.length})
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-colors"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete ({selectedCategories.length})
                </button>
              </>
            )}
          </div>
        </div>

                 {/* Advanced Filters */}
         {showFilters && (
           <div className="mt-4 pt-4 border-t border-gray-200">
             <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                 <select
                   value={statusFilter}
                   onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 >
                   <option value="all">All Status</option>
                   <option value="active">Active</option>
                   <option value="inactive">Inactive</option>
                 </select>
               </div>
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">Level</label>
                 <select
                   value={levelFilter}
                   onChange={(e) => setLevelFilter(e.target.value as 'all' | 'root' | 'child')}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 >
                   <option value="all">All Levels</option>
                   <option value="root">Root Categories</option>
                   <option value="child">Subcategories</option>
                 </select>
               </div>
               <div>
                 <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                 <select
                   value={`${sortConfig.key}-${sortConfig.direction}`}
                   onChange={(e) => {
                     const [key, direction] = e.target.value.split('-')
                     setSortConfig({ key: key as keyof Category, direction: direction as 'asc' | 'desc' })
                   }}
                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                 >
                   <option value="name-asc">Name (A-Z)</option>
                   <option value="name-desc">Name (Z-A)</option>
                   <option value="productCount-desc">Most Products</option>
                   <option value="productCount-asc">Least Products</option>
                   <option value="updatedAt-desc">Recently Updated</option>
                   <option value="updatedAt-asc">Oldest Updated</option>
                 </select>
               </div>
               <div className="flex items-end">
                 <button
                   onClick={() => {
                     setSearchTerm('')
                     setStatusFilter('all')
                     setLevelFilter('all')
                     setSortConfig({ key: 'name', direction: 'asc' })
                   }}
                   className="w-full px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors"
                 >
                   Clear Filters
                 </button>
               </div>
             </div>
           </div>
         )}
      </div>

      {/* Categories Table */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedCategories.length === filteredAndSortedCategories.length && filteredAndSortedCategories.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Category</span>
                    {getSortIcon('name')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('productCount')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Products</span>
                    {getSortIcon('productCount')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Status</span>
                    {getSortIcon('status')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('updatedAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</span>
                    {getSortIcon('updatedAt')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Level
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
                             {filteredAndSortedCategories.length === 0 ? (
                 <tr>
                   <td colSpan={7} className="px-6 py-12 text-center">
                    <div className="text-gray-500">
                      <Folder className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-lg font-medium">No categories found</p>
                      <p className="text-sm">Try adjusting your search or filters</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredAndSortedCategories.map((category) => (
                  <tr key={category.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(category.id)}
                        onChange={() => handleSelectCategory(category.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                                         <td className="px-6 py-4 whitespace-nowrap">
                       <div className="flex items-center">
                         <div className="flex-shrink-0 h-10 w-10">
                           <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                             <Folder className="h-5 w-5 text-blue-600" />
                           </div>
                         </div>
                         <div className="ml-4 flex-1">
                           <div className="flex items-center space-x-2">
                             {/* Indentation for hierarchy */}
                             {(() => {
                               const { categoryMap } = buildHierarchy(categories)
                               const level = getCategoryLevel(category, categoryMap)
                               return Array.from({ length: level }, (_, i) => (
                                 <div key={i} className="w-4 h-px bg-gray-300"></div>
                               ))
                             })()}
                             
                             {/* Category name with hierarchy indicator */}
                             <div className="flex items-center space-x-2">
                               <span className="text-sm font-medium text-gray-900">{category.name}</span>
                               {category.parentCategory && (
                                 <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                   Child
                                 </span>
                               )}
                               {hasChildren(category.id) && (
                                 <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-600">
                                   Parent
                                 </span>
                               )}
                             </div>
                           </div>
                           
                           {/* Category path/breadcrumb */}
                           <div className="text-sm text-gray-500 max-w-xs truncate">
                             {(() => {
                               const { categoryMap } = buildHierarchy(categories)
                               const path = getCategoryPath(category, categoryMap)
                               if (path.length > 1) {
                                 return path.slice(0, -1).join(' > ') + ' > ' + category.name
                               }
                               return category.description
                             })()}
                           </div>
                           
                           {category.slug && (
                             <div className="text-xs text-gray-400">/{category.slug}</div>
                           )}
                         </div>
                       </div>
                     </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {category.productCount.toLocaleString()} products
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        category.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {category.status === 'active' ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <AlertTriangle className="h-3 w-3 mr-1" />
                        )}
                        {category.status}
                      </span>
                    </td>
                                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                       {formatDate(category.updatedAt)}
                     </td>
                     <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                       {(() => {
                         const { categoryMap } = buildHierarchy(categories)
                         const level = getCategoryLevel(category, categoryMap)
                         return (
                           <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                             level === 0 
                               ? 'bg-blue-100 text-blue-800' 
                               : level === 1 
                               ? 'bg-green-100 text-green-800'
                               : 'bg-purple-100 text-purple-800'
                           }`}>
                             {level === 0 ? 'Root' : level === 1 ? 'Sub' : `Level ${level}`}
                           </span>
                         )
                       })()}
                     </td>
                     <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button 
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors"
                          title="View Category"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button 
                          className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50 transition-colors"
                          title="Edit Category"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <div className="relative action-menu">
                          <button
                            onClick={() => toggleActionMenu(category.id)}
                            className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50 transition-colors"
                            title="More Actions"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </button>
                          {openActionMenu === category.id && (
                            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                              <div className="py-1">
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                  <Copy className="h-4 w-4 mr-2" />
                                  Duplicate
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                  <Archive className="h-4 w-4 mr-2" />
                                  Archive
                                </button>
                                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                  <ExternalLink className="h-4 w-4 mr-2" />
                                  View Products
                                </button>
                                <hr className="my-1" />
                                <button className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 text-center mt-4">Delete Categories</h3>
              <p className="text-sm text-gray-500 text-center mt-2">
                Are you sure you want to delete {selectedCategories.length} selected category{selectedCategories.length > 1 ? 'ies' : 'y'}? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isDeleting}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteCategories}
                  disabled={isDeleting}
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50 flex items-center"
                >
                  {isDeleting && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>}
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create Category Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Create New Category</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category Name *</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Enter category name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    rows={3}
                    placeholder="Enter category description"
                  />
                </div>
                                 <div>
                   <label className="block text-sm font-medium text-gray-700 mb-1">Parent Category</label>
                   <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                     <option value="">No parent category (Root level)</option>
                     {(() => {
                       const { categoryMap } = buildHierarchy(categories)
                       return categories
                         .filter(cat => cat.status === 'active') // Only show active categories as parents
                         .map(cat => {
                           const level = getCategoryLevel(cat, categoryMap)
                           const indent = '—'.repeat(level)
                           return (
                             <option key={cat.id} value={cat.id}>
                               {indent} {cat.name} {hasChildren(cat.id) ? '(has children)' : ''}
                             </option>
                           )
                         })
                     })()}
                   </select>
                   <p className="text-xs text-gray-500 mt-1">
                     Select a parent category to create a subcategory, or leave empty for a root category
                   </p>
                 </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    Create Category
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CategoriesPage 
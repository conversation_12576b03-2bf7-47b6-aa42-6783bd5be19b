import { useState } from 'react'
import { 
  Eye, 
  EyeOff, 
  Settings as SettingsIcon, 
  Info, 
  Package, 
  Users, 
  ShoppingCart,
  Building,
  Tag,
  FileText,
  DollarSign,
  Calendar,
  Hash,
  Type,
  Globe
} from 'lucide-react'
import toast from 'react-hot-toast'

interface SystemField {
  id: string
  name: string
  key: string
  description: string
  resourceType: string
  fieldType: string
  isRequired: boolean
  isVisible: boolean
  isEditable: boolean
  icon: React.ReactNode
}

const SystemFieldsTab: React.FC = () => {
  const [systemFields, setSystemFields] = useState<SystemField[]>([
    // Product Fields
    {
      id: 'product_name',
      name: 'Product Name',
      key: 'name',
      description: 'The name of the product',
      resourceType: 'products',
      fieldType: 'text',
      isRequired: true,
      isVisible: true,
      isEditable: false, // Required fields cannot be hidden
      icon: <Package className="h-4 w-4" />
    },
    {
      id: 'product_type',
      name: 'Product Type',
      key: 'type',
      description: 'The type/category of the product',
      resourceType: 'products',
      fieldType: 'select',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <Tag className="h-4 w-4" />
    },
    {
      id: 'product_sku',
      name: 'SKU',
      key: 'sku',
      description: 'Stock Keeping Unit identifier',
      resourceType: 'products',
      fieldType: 'text',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <Hash className="h-4 w-4" />
    },
    {
      id: 'product_description',
      name: 'Description',
      key: 'description',
      description: 'Product description and details',
      resourceType: 'products',
      fieldType: 'textarea',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <FileText className="h-4 w-4" />
    },
    {
      id: 'product_price',
      name: 'Price',
      key: 'price',
      description: 'Product selling price',
      resourceType: 'products',
      fieldType: 'number',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <DollarSign className="h-4 w-4" />
    },
    {
      id: 'product_categories',
      name: 'Categories',
      key: 'categories',
      description: 'Product categories and tags',
      resourceType: 'products',
      fieldType: 'multi_select',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <Tag className="h-4 w-4" />
    },
    // Customer Fields
    {
      id: 'customer_name',
      name: 'Customer Name',
      key: 'name',
      description: 'Full name of the customer',
      resourceType: 'customers',
      fieldType: 'text',
      isRequired: true,
      isVisible: true,
      isEditable: false,
      icon: <Users className="h-4 w-4" />
    },
    {
      id: 'customer_email',
      name: 'Email',
      key: 'email',
      description: 'Customer email address',
      resourceType: 'customers',
      fieldType: 'email',
      isRequired: true,
      isVisible: true,
      isEditable: false,
      icon: <Globe className="h-4 w-4" />
    },
    {
      id: 'customer_phone',
      name: 'Phone',
      key: 'phone',
      description: 'Customer phone number',
      resourceType: 'customers',
      fieldType: 'text',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <Type className="h-4 w-4" />
    },
    {
      id: 'customer_company',
      name: 'Company',
      key: 'company',
      description: 'Customer company name',
      resourceType: 'customers',
      fieldType: 'text',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <Building className="h-4 w-4" />
    },
    // Order Fields
    {
      id: 'order_number',
      name: 'Order Number',
      key: 'order_number',
      description: 'Unique order identifier',
      resourceType: 'orders',
      fieldType: 'text',
      isRequired: true,
      isVisible: true,
      isEditable: false,
      icon: <ShoppingCart className="h-4 w-4" />
    },
    {
      id: 'order_date',
      name: 'Order Date',
      key: 'order_date',
      description: 'Date when the order was placed',
      resourceType: 'orders',
      fieldType: 'date',
      isRequired: true,
      isVisible: true,
      isEditable: false,
      icon: <Calendar className="h-4 w-4" />
    },
    {
      id: 'order_status',
      name: 'Order Status',
      key: 'status',
      description: 'Current status of the order',
      resourceType: 'orders',
      fieldType: 'select',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <Tag className="h-4 w-4" />
    },
    {
      id: 'order_total',
      name: 'Order Total',
      key: 'total',
      description: 'Total amount of the order',
      resourceType: 'orders',
      fieldType: 'number',
      isRequired: false,
      isVisible: true,
      isEditable: true,
      icon: <DollarSign className="h-4 w-4" />
    }
  ])

  const [selectedResourceType, setSelectedResourceType] = useState('products')

  const resourceTypes = [
    { value: 'products', label: 'Products', icon: Package },
    { value: 'customers', label: 'Customers', icon: Users },
    { value: 'orders', label: 'Orders', icon: ShoppingCart },
  ]

  const toggleFieldVisibility = (fieldId: string) => {
    setSystemFields(prev => prev.map(field => 
      field.id === fieldId 
        ? { ...field, isVisible: !field.isVisible }
        : field
    ))
    
    const field = systemFields.find(f => f.id === fieldId)
    if (field) {
      toast.success(`${field.name} ${field.isVisible ? 'hidden' : 'shown'} in forms`)
    }
  }

  const filteredFields = systemFields.filter(field => field.resourceType === selectedResourceType)

  const getFieldTypeDisplay = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'text': 'Text',
      'number': 'Number',
      'email': 'Email',
      'textarea': 'Textarea',
      'select': 'Select',
      'multi_select': 'Multi Select',
      'date': 'Date',
      'boolean': 'Boolean'
    }
    return typeMap[type] || type
  }

  const getFieldTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      'text': 'bg-blue-100 text-blue-800',
      'number': 'bg-green-100 text-green-800',
      'email': 'bg-pink-100 text-pink-800',
      'textarea': 'bg-blue-100 text-blue-800',
      'select': 'bg-purple-100 text-purple-800',
      'multi_select': 'bg-purple-100 text-purple-800',
      'date': 'bg-orange-100 text-orange-800',
      'boolean': 'bg-gray-100 text-gray-800'
    }
    return colorMap[type] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="space-y-6">
      {/* Info Banner */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-blue-900">System Fields Management</h3>
            <p className="text-sm text-blue-700 mt-1">
              Control the visibility of built-in system fields in your forms. Required fields cannot be hidden but their order can be customized.
            </p>
          </div>
        </div>
      </div>

      {/* Resource Type Selector */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        {resourceTypes.map((type) => {
          const Icon = type.icon
          return (
            <button
              key={type.value}
              onClick={() => setSelectedResourceType(type.value)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedResourceType === type.value
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{type.label}</span>
            </button>
          )
        })}
      </div>

      {/* System Fields Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            {resourceTypes.find(t => t.value === selectedResourceType)?.label} Fields
          </h3>
          <p className="text-sm text-gray-500 mt-1">
            Manage the visibility and settings of system fields for {selectedResourceType}
          </p>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Field
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Required
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Visibility
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredFields.map((field) => (
                <tr key={field.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 bg-gray-100 rounded-lg flex items-center justify-center">
                          {field.icon}
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{field.name}</div>
                        <div className="text-sm text-gray-500">{field.description}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getFieldTypeColor(field.fieldType)}`}>
                      {getFieldTypeDisplay(field.fieldType)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      field.isRequired
                        ? 'bg-red-100 text-red-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {field.isRequired ? 'Required' : 'Optional'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {field.isVisible ? (
                        <Eye className="h-4 w-4 text-green-500 mr-2" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-gray-400 mr-2" />
                      )}
                      <span className={`text-sm ${field.isVisible ? 'text-green-700' : 'text-gray-500'}`}>
                        {field.isVisible ? 'Visible' : 'Hidden'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => toggleFieldVisibility(field.id)}
                        disabled={!field.isEditable}
                        className={`p-1 rounded transition-colors ${
                          field.isEditable
                            ? 'text-blue-600 hover:text-blue-900 hover:bg-blue-50'
                            : 'text-gray-300 cursor-not-allowed'
                        }`}
                        title={field.isEditable ? (field.isVisible ? 'Hide field' : 'Show field') : 'Required field cannot be hidden'}
                      >
                        {field.isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                      <button
                        className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                        title="Field Settings"
                      >
                        <SettingsIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default SystemFieldsTab

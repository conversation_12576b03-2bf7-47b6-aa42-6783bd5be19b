#!/usr/bin/env python3
"""
PostgreSQL database creation script for PIM API

This script creates the PostgreSQL database if it doesn't exist.
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import sys

def create_database():
    """Create the PostgreSQL database"""
    
    # Database connection parameters
    DB_HOST = "localhost"
    DB_PORT = "5432"
    DB_USER = "postgres"
    DB_PASSWORD = "password"  # Change this to your PostgreSQL password
    DB_NAME = "pim_db"
    
    print("🔧 Creating PostgreSQL database...")
    
    try:
        # Connect to PostgreSQL server (not to a specific database)
        print(f"📡 Connecting to PostgreSQL server at {DB_HOST}:{DB_PORT}...")
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database="postgres"  # Connect to default postgres database
        )
        
        # Set autocommit mode
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{DB_NAME}'")
        exists = cursor.fetchone()
        
        if exists:
            print(f"✅ Database '{DB_NAME}' already exists!")
        else:
            # Create the database
            cursor.execute(f"CREATE DATABASE {DB_NAME}")
            print(f"✅ Database '{DB_NAME}' created successfully!")
        
        cursor.close()
        conn.close()
        
        return True
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL Error: {e}")
        print("\n💡 Make sure:")
        print("1. PostgreSQL is running")
        print("2. Username and password are correct")
        print("3. You have permission to create databases")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function"""
    print("🚀 PostgreSQL Database Setup for PIM API")
    print("=" * 50)
    
    if create_database():
        print("\n🎉 Database setup completed successfully!")
        print("\n📖 Next steps:")
        print("1. Run: python setup_db.py")
        print("2. Run: python main.py")
        print("3. Visit: http://localhost:8000/docs")
    else:
        print("\n❌ Database setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()

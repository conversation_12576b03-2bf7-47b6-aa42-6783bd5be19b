import { useState, useEffect, useRef } from 'react'
import { X, Check, Settings } from 'lucide-react'

interface Column {
  key: string
  label: string
  visible?: boolean
  sticky?: boolean
}

interface ColumnSelectorProps {
  columns: Column[]
  selectedColumns: string[]
  onSelectionChange: (selectedColumns: string[]) => void
  isOpen: boolean
  onClose: () => void
}

const ColumnSelector = ({
  columns,
  selectedColumns,
  onSelectionChange,
  isOpen,
  onClose
}: ColumnSelectorProps) => {
  const modalRef = useRef<HTMLDivElement>(null)
  const [localSelection, setLocalSelection] = useState<string[]>(selectedColumns)

  useEffect(() => {
    setLocalSelection(selectedColumns)
  }, [selectedColumns])

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  const handleColumnToggle = (columnKey: string) => {
    setLocalSelection(prev => 
      prev.includes(columnKey)
        ? prev.filter(key => key !== columnKey)
        : [...prev, columnKey]
    )
  }

  const handleSelectAll = () => {
    setLocalSelection(columns.map(col => col.key))
  }

  const handleDeselectAll = () => {
    setLocalSelection([])
  }

  const handleApply = () => {
    onSelectionChange(localSelection)
    onClose()
  }

  const handleCancel = () => {
    setLocalSelection(selectedColumns)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />

        {/* Modal */}
        <div
          ref={modalRef}
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
        >
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Settings className="h-5 w-5 text-gray-400 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Select Columns</h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="bg-white px-6 py-4">
            {/* Action buttons */}
            <div className="flex space-x-2 mb-4">
              <button
                onClick={handleSelectAll}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
              >
                Select All
              </button>
              <button
                onClick={handleDeselectAll}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                Deselect All
              </button>
            </div>

            {/* Column list */}
            <div className="max-h-96 overflow-y-auto">
              <div className="space-y-2">
                {columns.map((column) => {
                  const isSelected = localSelection.includes(column.key)
                  
                  return (
                    <label
                      key={column.key}
                      className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors"
                    >
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleColumnToggle(column.key)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {column.label}
                          </div>
                          {column.sticky && (
                            <div className="text-xs text-blue-600 font-medium">
                              Sticky Column
                            </div>
                          )}
                        </div>
                      </div>
                      {isSelected && (
                        <Check className="h-4 w-4 text-green-600 ml-auto" />
                      )}
                    </label>
                  )
                })}
              </div>
            </div>

            {/* Selection summary */}
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                <span className="font-medium">{localSelection.length}</span> of{' '}
                <span className="font-medium">{columns.length}</span> columns selected
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              onClick={handleApply}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Apply Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ColumnSelector

import { useState, useEffect } from 'react'
import { X, Plus, Trash2, Globe, Key, Settings, TestTube, CheckCircle, XCircle } from 'lucide-react'
import toast from 'react-hot-toast'

interface WebhookIntegration {
  id?: string
  integration_name: string
  webhook_url: string
  webhook_method: string
  webhook_headers: { [key: string]: string }
  field_mapping: { [key: string]: string }
  is_active: boolean
  last_sync_at?: string
  sync_status?: string
  error_message?: string
}

interface WebhookConfigModalProps {
  isOpen: boolean
  onClose: () => void
  fieldId: string
  fieldLabel: string
  existingIntegrations?: WebhookIntegration[]
  onSave: (integrations: WebhookIntegration[]) => void
}

const WebhookConfigModal: React.FC<WebhookConfigModalProps> = ({
  isOpen,
  onClose,
  fieldId,
  fieldLabel,
  existingIntegrations = [],
  onSave
}) => {
  const [integrations, setIntegrations] = useState<WebhookIntegration[]>(existingIntegrations)
  const [activeTab, setActiveTab] = useState(0)
  const [testing, setTesting] = useState<{ [key: number]: boolean }>({})

  useEffect(() => {
    if (isOpen) {
      setIntegrations(existingIntegrations.length > 0 ? existingIntegrations : [createNewIntegration()])
      setActiveTab(0)
    }
  }, [isOpen, existingIntegrations])

  const createNewIntegration = (): WebhookIntegration => ({
    integration_name: '',
    webhook_url: '',
    webhook_method: 'POST',
    webhook_headers: {
      'Content-Type': 'application/json'
    },
    field_mapping: {},
    is_active: true
  })

  const addIntegration = () => {
    const newIntegration = createNewIntegration()
    setIntegrations([...integrations, newIntegration])
    setActiveTab(integrations.length)
  }

  const removeIntegration = (index: number) => {
    if (integrations.length === 1) {
      toast.error('At least one integration is required')
      return
    }
    
    const newIntegrations = integrations.filter((_, i) => i !== index)
    setIntegrations(newIntegrations)
    
    if (activeTab >= newIntegrations.length) {
      setActiveTab(newIntegrations.length - 1)
    }
  }

  const updateIntegration = (index: number, field: keyof WebhookIntegration, value: any) => {
    const newIntegrations = [...integrations]
    newIntegrations[index] = {
      ...newIntegrations[index],
      [field]: value
    }
    setIntegrations(newIntegrations)
  }

  const updateHeader = (index: number, headerKey: string, headerValue: string) => {
    const newIntegrations = [...integrations]
    newIntegrations[index] = {
      ...newIntegrations[index],
      webhook_headers: {
        ...newIntegrations[index].webhook_headers,
        [headerKey]: headerValue
      }
    }
    setIntegrations(newIntegrations)
  }

  const removeHeader = (index: number, headerKey: string) => {
    const newIntegrations = [...integrations]
    const headers = { ...newIntegrations[index].webhook_headers }
    delete headers[headerKey]
    newIntegrations[index] = {
      ...newIntegrations[index],
      webhook_headers: headers
    }
    setIntegrations(newIntegrations)
  }

  const addHeader = (index: number) => {
    const newIntegrations = [...integrations]
    newIntegrations[index] = {
      ...newIntegrations[index],
      webhook_headers: {
        ...newIntegrations[index].webhook_headers,
        '': ''
      }
    }
    setIntegrations(newIntegrations)
  }

  const testWebhook = async (index: number) => {
    const integration = integrations[index]
    
    if (!integration.webhook_url) {
      toast.error('Webhook URL is required')
      return
    }

    setTesting(prev => ({ ...prev, [index]: true }))

    try {
      // Simulate webhook test
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // TODO: Replace with actual webhook test
      // const response = await fetch('/api/custom-fields/test-webhook', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     webhook_url: integration.webhook_url,
      //     webhook_method: integration.webhook_method,
      //     webhook_headers: integration.webhook_headers,
      //     test_data: { [fieldLabel]: 'test_value' }
      //   })
      // })

      toast.success('Webhook test successful!')
      
      // Update integration status
      updateIntegration(index, 'sync_status', 'success')
      updateIntegration(index, 'last_sync_at', new Date().toISOString())
      updateIntegration(index, 'error_message', '')
      
    } catch (error) {
      toast.error('Webhook test failed')
      updateIntegration(index, 'sync_status', 'error')
      updateIntegration(index, 'error_message', 'Test failed: Connection timeout')
    } finally {
      setTesting(prev => ({ ...prev, [index]: false }))
    }
  }

  const handleSave = () => {
    // Validate integrations
    for (let i = 0; i < integrations.length; i++) {
      const integration = integrations[i]
      
      if (!integration.integration_name.trim()) {
        toast.error(`Integration ${i + 1}: Name is required`)
        setActiveTab(i)
        return
      }
      
      if (!integration.webhook_url.trim()) {
        toast.error(`Integration ${i + 1}: Webhook URL is required`)
        setActiveTab(i)
        return
      }
      
      try {
        new URL(integration.webhook_url)
      } catch {
        toast.error(`Integration ${i + 1}: Invalid webhook URL`)
        setActiveTab(i)
        return
      }
    }

    onSave(integrations)
    toast.success('Webhook configuration saved successfully')
    onClose()
  }

  if (!isOpen) return null

  const currentIntegration = integrations[activeTab]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Globe className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Webhook Configuration</h2>
              <p className="text-sm text-gray-500">Configure third-party integrations for "{fieldLabel}"</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Integration Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex items-center justify-between px-6 py-3">
            <div className="flex space-x-1">
              {integrations.map((integration, index) => (
                <button
                  key={index}
                  onClick={() => setActiveTab(index)}
                  className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === index
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {integration.integration_name || `Integration ${index + 1}`}
                  {integration.sync_status === 'success' && (
                    <CheckCircle className="inline h-3 w-3 ml-1 text-green-500" />
                  )}
                  {integration.sync_status === 'error' && (
                    <XCircle className="inline h-3 w-3 ml-1 text-red-500" />
                  )}
                </button>
              ))}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={addIntegration}
                className="btn btn-secondary btn-sm flex items-center"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Integration
              </button>
              {integrations.length > 1 && (
                <button
                  onClick={() => removeIntegration(activeTab)}
                  className="btn btn-secondary btn-sm text-red-600 hover:text-red-700 flex items-center"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Remove
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Integration Configuration */}
        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          {currentIntegration && (
            <div className="space-y-6">
              {/* Basic Configuration */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Configuration</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Integration Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={currentIntegration.integration_name}
                      onChange={(e) => updateIntegration(activeTab, 'integration_name', e.target.value)}
                      placeholder="e.g., Shopify, BigCommerce, Custom API"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      HTTP Method
                    </label>
                    <select
                      value={currentIntegration.webhook_method}
                      onChange={(e) => updateIntegration(activeTab, 'webhook_method', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="POST">POST</option>
                      <option value="PUT">PUT</option>
                      <option value="PATCH">PATCH</option>
                    </select>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Webhook URL <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="url"
                    value={currentIntegration.webhook_url}
                    onChange={(e) => updateIntegration(activeTab, 'webhook_url', e.target.value)}
                    placeholder="https://api.example.com/webhooks/custom-fields"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="mt-4 flex items-center justify-between">
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={currentIntegration.is_active}
                        onChange={(e) => updateIntegration(activeTab, 'is_active', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      <span className="ml-3 text-sm font-medium text-gray-700">
                        Active
                      </span>
                    </label>
                  </div>
                  
                  <button
                    onClick={() => testWebhook(activeTab)}
                    disabled={testing[activeTab] || !currentIntegration.webhook_url}
                    className="btn btn-secondary flex items-center"
                  >
                    <TestTube className="h-4 w-4 mr-2" />
                    {testing[activeTab] ? 'Testing...' : 'Test Webhook'}
                  </button>
                </div>
              </div>

              {/* Headers Configuration */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">HTTP Headers</h3>
                  <button
                    onClick={() => addHeader(activeTab)}
                    className="btn btn-secondary btn-sm flex items-center"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Header
                  </button>
                </div>
                
                <div className="space-y-3">
                  {Object.entries(currentIntegration.webhook_headers).map(([key, value], index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <input
                        type="text"
                        value={key}
                        onChange={(e) => {
                          const oldKey = key
                          const newKey = e.target.value
                          const headers = { ...currentIntegration.webhook_headers }
                          if (oldKey !== newKey) {
                            delete headers[oldKey]
                            headers[newKey] = value
                            updateIntegration(activeTab, 'webhook_headers', headers)
                          }
                        }}
                        placeholder="Header name"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => updateHeader(activeTab, key, e.target.value)}
                        placeholder="Header value"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <button
                        onClick={() => removeHeader(activeTab, key)}
                        className="text-red-500 hover:text-red-700 p-2"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Status Information */}
              {currentIntegration.last_sync_at && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Sync Status</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {currentIntegration.sync_status === 'success' ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500" />
                        )}
                        <span className="text-sm font-medium">
                          {currentIntegration.sync_status === 'success' ? 'Last sync successful' : 'Last sync failed'}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {new Date(currentIntegration.last_sync_at).toLocaleString()}
                      </span>
                    </div>
                    {currentIntegration.error_message && (
                      <p className="text-sm text-red-600 mt-2">{currentIntegration.error_message}</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="btn btn-secondary"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="btn btn-primary flex items-center"
          >
            <Settings className="h-4 w-4 mr-2" />
            Save Configuration
          </button>
        </div>
      </div>
    </div>
  )
}

export default WebhookConfigModal

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  Building, 
  Globe, 
  Zap, 
  Mail, 
  CreditCard, 
  Database,
  Eye,
  EyeOff,
  AlertCircle,
  Info,
  XCircle
} from 'lucide-react';
import SectionHeader from '../../../components/SectionHeader';

interface Platform {
  id: string;
  name: string;
  icon: string; // now icon is a string name, not a ReactNode
  description: string;
  authTypes: string[];
  color: string;
}

interface ConnectionForm {
  name: string;
  platform: string;
  authType: string;
  credentials: Record<string, string>;
  connectionConfig: Record<string, any>;
}

const iconMap: Record<string, React.ReactNode> = {
  salesforce: <Building className="w-8 h-8" />,
  zoho: <Globe className="w-8 h-8" />,
  hubspot: <Zap className="w-8 h-8" />,
  stripe: <CreditCard className="w-8 h-8" />,
  mailchimp: <Mail className="w-8 h-8" />,
  generic: <Database className="w-8 h-8" />,
  custom: <Database className="w-8 h-8" />
};

const CreateConnection: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Initialize currentStep from URL path
  const getCurrentStepFromUrl = () => {
    const pathParts = location.pathname.split('/');
    const stepPart = pathParts[pathParts.length - 1];
    const stepNumber = parseInt(stepPart.replace('step', ''));
    const validStep = Math.max(1, Math.min(4, stepNumber || 1));
    return validStep;
  };
  
  const [currentStep, setCurrentStep] = useState(getCurrentStepFromUrl());
  const [formData, setFormData] = useState<ConnectionForm>({
    name: '',
    platform: '',
    authType: '',
    credentials: {},
    connectionConfig: {}
  });
  const [loading, setLoading] = useState(false);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [platformsLoading, setPlatformsLoading] = useState(true);
  const [customName, setCustomName] = useState('');
  const [testingConnection, setTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // Update currentStep when URL changes
  useEffect(() => {
    const newStep = getCurrentStepFromUrl();
    setCurrentStep(newStep);
  }, [location.pathname]);

  useEffect(() => {
    // Simulate fetching from API
    setPlatformsLoading(true);
    setTimeout(() => {
      setPlatforms([
        {
          id: 'salesforce',
          name: 'Salesforce',
          icon: 'salesforce',
          description: 'CRM and sales automation platform',
          authTypes: ['OAuth2'],
          color: 'bg-blue-500'
        },
        {
          id: 'zoho',
          name: 'Zoho CRM',
          icon: 'zoho',
          description: 'Customer relationship management',
          authTypes: ['API Key', 'OAuth2'],
          color: 'bg-orange-500'
        },
        {
          id: 'hubspot',
          name: 'HubSpot',
          icon: 'hubspot',
          description: 'Marketing and CRM platform',
          authTypes: ['API Key', 'OAuth2'],
          color: 'bg-orange-400'
        },
        {
          id: 'stripe',
          name: 'Stripe',
          icon: 'stripe',
          description: 'Payment processing platform',
          authTypes: ['API Key', 'Webhook'],
          color: 'bg-purple-500'
        },
        {
          id: 'mailchimp',
          name: 'Mailchimp',
          icon: 'mailchimp',
          description: 'Email marketing platform',
          authTypes: ['API Key'],
          color: 'bg-yellow-500'
        },
        {
          id: 'generic',
          name: 'Generic REST API',
          icon: 'generic',
          description: 'Custom REST API integration',
          authTypes: ['API Key', 'Basic Auth', 'Bearer Token'],
          color: 'bg-gray-500'
        },
        {
          id: 'custom',
          name: 'Custom Connector',
          icon: 'custom',
          description: 'Connect to any REST API or service',
          authTypes: ['API Key', 'OAuth2', 'Basic Auth', 'Bearer Token'],
          color: 'bg-gray-400'
        }
      ]);
      setPlatformsLoading(false);
    }, 700);
  }, []);

  const getPlatformFields = (platform: string, authType: string) => {
    if (platform === 'salesforce' && authType === 'OAuth2') {
      return [
        { name: 'client_id', label: 'Client ID', type: 'text', required: true },
        { name: 'client_secret', label: 'Client Secret', type: 'password', required: true },
        { name: 'instance_url', label: 'Instance URL', type: 'text', required: true, placeholder: 'https://na1.salesforce.com' },
        { name: 'api_version', label: 'API Version', type: 'text', required: false, placeholder: '58.0', defaultValue: '58.0' }
      ];
    }
    if (platform === 'zoho' && authType === 'API Key') {
      return [
        { name: 'api_key', label: 'API Key', type: 'password', required: true },
        { name: 'base_url', label: 'Base URL', type: 'text', required: false, placeholder: 'https://www.zohoapis.com/crm/v3', defaultValue: 'https://www.zohoapis.com/crm/v3' },
        { name: 'timeout', label: 'Timeout (seconds)', type: 'number', required: false, defaultValue: 30 }
      ];
    }
    if (platform === 'zoho' && authType === 'OAuth2') {
      return [
        { name: 'client_id', label: 'Client ID', type: 'text', required: true },
        { name: 'client_secret', label: 'Client Secret', type: 'password', required: true },
        { name: 'redirect_uri', label: 'Redirect URI', type: 'text', required: true },
        { name: 'scope', label: 'Scope', type: 'text', required: true, placeholder: 'ZohoCRM.modules.ALL' }
      ];
    }
    if (platform === 'hubspot' && authType === 'API Key') {
      return [
        { name: 'api_key', label: 'API Key', type: 'password', required: true },
        { name: 'base_url', label: 'Base URL', type: 'text', required: false, placeholder: 'https://api.hubapi.com', defaultValue: 'https://api.hubapi.com' },
        { name: 'timeout', label: 'Timeout (seconds)', type: 'number', required: false, defaultValue: 30 }
      ];
    }
    if (platform === 'hubspot' && authType === 'OAuth2') {
      return [
        { name: 'client_id', label: 'Client ID', type: 'text', required: true },
        { name: 'client_secret', label: 'Client Secret', type: 'password', required: true },
        { name: 'redirect_uri', label: 'Redirect URI', type: 'text', required: true },
        { name: 'scope', label: 'Scope', type: 'text', required: true, placeholder: 'contacts' }
      ];
    }
    if (platform === 'stripe' && authType === 'API Key') {
      return [
        { name: 'api_key', label: 'Secret Key', type: 'password', required: true },
        { name: 'timeout', label: 'Timeout (seconds)', type: 'number', required: false, defaultValue: 30 }
      ];
    }
    if (platform === 'stripe' && authType === 'Webhook') {
      return [
        { name: 'webhook_secret', label: 'Webhook Secret', type: 'password', required: true },
        { name: 'timeout', label: 'Timeout (seconds)', type: 'number', required: false, defaultValue: 30 }
      ];
    }
    if (platform === 'mailchimp' && authType === 'API Key') {
      return [
        { name: 'api_key', label: 'API Key', type: 'password', required: true },
        { name: 'server_prefix', label: 'Server Prefix', type: 'text', required: true, placeholder: 'us1' },
        { name: 'timeout', label: 'Timeout (seconds)', type: 'number', required: false, defaultValue: 30 }
      ];
    }
    if (platform === 'generic' && authType === 'API Key') {
      return [
        { name: 'base_url', label: 'Base URL', type: 'text', required: true, placeholder: 'https://api.example.com' },
        { name: 'api_key', label: 'API Key', type: 'password', required: true },
        { name: 'timeout', label: 'Timeout (seconds)', type: 'number', required: false, defaultValue: 30 }
      ];
    }
    if (platform === 'generic' && authType === 'Basic Auth') {
      return [
        { name: 'base_url', label: 'Base URL', type: 'text', required: true, placeholder: 'https://api.example.com' },
        { name: 'username', label: 'Username', type: 'text', required: true },
        { name: 'password', label: 'Password', type: 'password', required: true },
        { name: 'timeout', label: 'Timeout (seconds)', type: 'number', required: false, defaultValue: 30 }
      ];
    }
    if (platform === 'generic' && authType === 'Bearer Token') {
      return [
        { name: 'base_url', label: 'Base URL', type: 'text', required: true, placeholder: 'https://api.example.com' },
        { name: 'token', label: 'Bearer Token', type: 'password', required: true },
        { name: 'timeout', label: 'Timeout (seconds)', type: 'number', required: false, defaultValue: 30 }
      ];
    }
    if (platform === 'custom') {
      if (authType === 'API Key') {
        return [
          { name: 'base_url', label: 'Base URL', type: 'text', required: true, placeholder: 'https://api.example.com' },
          { name: 'api_key', label: 'API Key', type: 'password', required: true }
        ];
      }
      if (authType === 'OAuth2') {
        return [
          { name: 'base_url', label: 'Base URL', type: 'text', required: true, placeholder: 'https://api.example.com' },
          { name: 'client_id', label: 'Client ID', type: 'text', required: true },
          { name: 'client_secret', label: 'Client Secret', type: 'password', required: true },
          { name: 'redirect_uri', label: 'Redirect URI', type: 'text', required: true },
          { name: 'scope', label: 'Scope', type: 'text', required: false }
        ];
      }
      if (authType === 'Basic Auth') {
        return [
          { name: 'base_url', label: 'Base URL', type: 'text', required: true, placeholder: 'https://api.example.com' },
          { name: 'username', label: 'Username', type: 'text', required: true },
          { name: 'password', label: 'Password', type: 'password', required: true }
        ];
      }
      if (authType === 'Bearer Token') {
        return [
          { name: 'base_url', label: 'Base URL', type: 'text', required: true, placeholder: 'https://api.example.com' },
          { name: 'token', label: 'Bearer Token', type: 'password', required: true }
        ];
      }
      return [];
    }
    return [];
  };

  const navigateToStep = (stepNumber: number) => {
    const validStep = Math.max(1, Math.min(4, stepNumber));
    navigate(`/integrations/connections/create/step${validStep}`);
  };

  const handlePlatformSelect = (platform: Platform) => {
    setFormData({
      ...formData,
      platform: platform.id,
      authType: '' // reset authType
    });
    // Don't auto-navigate, let user click Next
  };

  const handleAuthTypeSelect = (authType: string) => {
    setFormData({
      ...formData,
      authType,
      credentials: {}
    });
    navigateToStep(3);
  };

  const handleCredentialChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      credentials: {
        ...formData.credentials,
        [field]: value
      }
    });
  };

  const handleConfigChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      connectionConfig: {
        ...formData.connectionConfig,
        [field]: value
      }
    });
  };

  const testConnection = async () => {
    setTestingConnection(true);
    setTestResult(null);
    
    try {
      // Get the fields for current platform and auth type
      const fields = getPlatformFields(formData.platform, formData.authType);
      
      // Check if all required fields are filled
      const hasAllRequiredFields = fields.every(field => {
        if (field.required) {
          const value = formData.credentials[field.name] || formData.connectionConfig[field.name];
          return value && value.trim() !== '';
        }
        return true;
      });
      
      if (!hasAllRequiredFields || formData.name.trim() === '') {
        setTestResult({ 
          success: false, 
          message: 'Please fill in all required fields before testing.' 
        });
        return;
      }
      
      // Prepare the test request payload
      const testPayload = {
        platform: formData.platform === 'custom' ? customName : formData.platform,
        auth_type: formData.authType,
        credentials: formData.credentials,
        connection_config: formData.connectionConfig
      };
      
      // Make real API call to test connection
      const response = await fetch('/api/integrations/connections/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testPayload)
      });
      
      const result = await response.json();
      
      if (response.ok) {
        setTestResult({ 
          success: result.success, 
          message: result.message 
        });
      } else {
        setTestResult({ 
          success: false, 
          message: result.message || 'Connection test failed. Please try again.' 
        });
      }
      
    } catch (error) {
      console.error('Connection test error:', error);
      setTestResult({ 
        success: false, 
        message: 'Connection test failed due to a network error. Please check your connection and try again.' 
      });
    } finally {
      setTestingConnection(false);
    }
  };

  const canProceedToNextStep = () => {
    if (currentStep === 1) {
      const canProceed = formData.platform !== '' && (formData.platform !== 'custom' || customName.trim() !== '');
      return canProceed;
    }
    if (currentStep === 2) {
      const canProceed = formData.authType !== '';
      return canProceed;
    }
    if (currentStep === 3) {
      const fields = getPlatformFields(formData.platform, formData.authType);
      const hasAllRequiredFields = fields.every(field => {
        if (field.required) {
          const value = formData.credentials[field.name] || formData.connectionConfig[field.name];
          return value && value.trim() !== '';
        }
        return true;
      });
      const canProceed = formData.name.trim() !== '' && hasAllRequiredFields;
      return canProceed;
    }
    return true;
  };

  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate to success page or connections list
      navigate('/integrations/connections');
    } catch (error) {
      console.error('Error creating connection:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Choose Platform</h2>
        <p className="text-gray-600 mt-2">Select a preset or create a custom connector</p>
      </div>
      {platformsLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
          <span className="text-gray-500">Loading platforms...</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {platforms.map((platform) => (
            <button
              key={platform.id}
              onClick={() => handlePlatformSelect(platform)}
              className={`p-6 border rounded-lg transition-all text-left ${formData.platform === platform.id ? 'border-blue-500 ring-2 ring-blue-200 shadow-md' : 'border-gray-200 hover:border-blue-300 hover:shadow-md'}`}
            >
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-lg ${platform.color} text-white`}>
                  {iconMap[platform.icon] || <Database className="w-8 h-8" />}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{platform.name}</h3>
                  <p className="text-sm text-gray-600">{platform.description}</p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {platform.authTypes.map((authType) => (
                      <span key={authType} className="px-2 py-1 bg-gray-100 text-xs rounded text-gray-600">
                        {authType}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
      {/* Custom connector name field only */}
      {formData.platform === 'custom' && (
        <div className="mt-6 space-y-4 bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Connector Name *</label>
            <input
              type="text"
              value={customName}
              onChange={e => setCustomName(e.target.value)}
              placeholder="e.g., Basecamp"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
            <p className="text-sm text-gray-500 mt-1">This will be the display name for your custom connector</p>
          </div>
        </div>
      )}
    </div>
  );

  const renderStep2 = () => {
    const selectedPlatform = platforms.find(p => p.id === formData.platform);
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Authentication Method</h2>
          <p className="text-gray-600 mt-2">Choose how to authenticate with {selectedPlatform?.name}</p>
        </div>
        <div className="space-y-4">
          {selectedPlatform?.authTypes.map((authType) => (
            <button
              key={authType}
              onClick={() => handleAuthTypeSelect(authType)}
              className={`w-full p-4 border rounded-lg transition-all text-left flex items-center justify-between ${formData.authType === authType ? 'border-blue-500 ring-2 ring-blue-200 shadow-md' : 'border-gray-200 hover:border-blue-300 hover:shadow-md'}`}
            >
              <div>
                <h3 className="font-semibold text-gray-900">{authType}</h3>
                <p className="text-sm text-gray-600">
                  {authType === 'OAuth2' && 'Secure OAuth2 authentication flow'}
                  {authType === 'API Key' && 'Simple API key authentication'}
                  {authType === 'Basic Auth' && 'Username and password authentication'}
                  {authType === 'Bearer Token' && 'Bearer token authentication'}
                  {authType === 'Webhook' && 'Webhook-based authentication'}
                </p>
              </div>
              <ArrowRight className="w-5 h-5 text-gray-400" />
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderStep3 = () => {
    const selectedPlatform = platforms.find(p => p.id === formData.platform);
    const fields = getPlatformFields(formData.platform, formData.authType);
    
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Connection Details</h2>
          <p className="text-gray-600 mt-2">Configure your {selectedPlatform?.name} connection</p>
        </div>
        
        {/* Show custom connector name if applicable */}
        {formData.platform === 'custom' && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start">
              <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-2" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Custom Connector:</p>
                <div className="mt-2 space-y-1">
                  <p><strong>Name:</strong> {customName}</p>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div className="space-y-4">
          {/* Connection Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Connection Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., My Custom API"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
          
          {/* Platform-specific fields */}
          {fields.map((field) => (
            <div key={field.name}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {field.label} {field.required && '*'}
              </label>
              {field.type === 'password' ? (
                <div className="relative">
                  <input
                    type="password"
                    value={formData.credentials[field.name] || ''}
                    onChange={(e) => handleCredentialChange(field.name, e.target.value)}
                    placeholder={field.placeholder}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required={field.required}
                  />
                </div>
              ) : field.type === 'number' ? (
                <input
                  type="number"
                  value={formData.connectionConfig[field.name] || field.defaultValue || ''}
                  onChange={(e) => handleConfigChange(field.name, parseInt(e.target.value))}
                  placeholder={field.placeholder}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required={field.required}
                />
              ) : (
                <input
                  type="text"
                  value={formData.credentials[field.name] || formData.connectionConfig[field.name] || field.defaultValue || ''}
                  onChange={(e) => {
                    if (field.name === 'api_version' || field.name === 'timeout' || field.name === 'base_url') {
                      handleConfigChange(field.name, e.target.value);
                    } else {
                      handleCredentialChange(field.name, e.target.value);
                    }
                  }}
                  placeholder={field.placeholder}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required={field.required}
                />
              )}
            </div>
          ))}
        </div>
        
        {/* Test Connection Button */}
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={testConnection}
            disabled={testingConnection || !canProceedToNextStep()}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed mr-4"
          >
            {testingConnection ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Testing...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Test Connection
              </>
            )}
          </button>
          
          {testResult && (
            <div className={`mt-4 p-4 rounded-lg ${
              testResult.success 
                ? 'bg-green-50 border border-green-200 text-green-800' 
                : 'bg-red-50 border border-red-200 text-red-800'
            }`}>
              <div className="flex items-center">
                {testResult.success ? (
                  <CheckCircle className="w-5 h-5 mr-2" />
                ) : (
                  <XCircle className="w-5 h-5 mr-2" />
                )}
                <span className="text-sm font-medium">{testResult.message}</span>
              </div>
            </div>
          )}
        </div>
        
        {/* Platform-specific help */}
        {formData.platform === 'salesforce' && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start">
              <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-2" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Salesforce Setup Instructions:</p>
                <ol className="mt-2 list-decimal list-inside space-y-1">
                  <li>Create a Connected App in Salesforce Setup</li>
                  <li>Enable OAuth Settings and add callback URL</li>
                  <li>Copy the Consumer Key and Consumer Secret</li>
                  <li>Set the appropriate OAuth scopes</li>
                </ol>
              </div>
            </div>
          </div>
        )}
        
        {formData.platform === 'zoho' && (
          <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-start">
              <Info className="w-5 h-5 text-orange-600 mt-0.5 mr-2" />
              <div className="text-sm text-orange-800">
                <p className="font-medium">Zoho CRM Setup Instructions:</p>
                <ol className="mt-2 list-decimal list-inside space-y-1">
                  <li>Go to Setup → Developer Space → APIs</li>
                  <li>Generate a Self-Client API Key</li>
                  <li>Copy the API Key and add it above</li>
                  <li>Ensure the API key has appropriate permissions</li>
                </ol>
              </div>
            </div>
          </div>
        )}
        
        {formData.platform === 'custom' && (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex items-start">
              <Info className="w-5 h-5 text-gray-600 mt-0.5 mr-2" />
              <div className="text-sm text-gray-700">
                <p className="font-medium">Custom Connector Setup:</p>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  <li>Enter the base URL of your API endpoint</li>
                  <li>Provide the required authentication credentials</li>
                  <li>Test the connection to verify your settings</li>
                  <li>After successful test, proceed to create the connection</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderStep4 = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Test Connection</h2>
        <p className="text-gray-600 mt-2">Verify your connection settings</p>
      </div>
      
      <div className="p-6 bg-gray-50 rounded-lg">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Connection Name:</span>
            <span className="text-sm text-gray-900">{formData.name}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Platform:</span>
            <span className="text-sm text-gray-900">
              {platforms.find(p => p.id === formData.platform)?.name}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Authentication:</span>
            <span className="text-sm text-gray-900">{formData.authType}</span>
          </div>
        </div>
      </div>
      
      <div className="flex items-center justify-center">
        <button
          onClick={handleSubmit}
          disabled={loading}
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Testing Connection...
            </>
          ) : (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              Test & Create Connection
            </>
          )}
        </button>
      </div>
    </div>
  );

  const steps = [
    { id: 1, title: 'Choose Platform' },
    { id: 2, title: 'Authentication' },
    { id: 3, title: 'Configuration' },
    { id: 4, title: 'Test & Create' }
  ];

  const renderNavigationButtons = () => (
    <div className="flex justify-between mt-8">
      {currentStep > 1 && currentStep < 4 && (
        <button
          onClick={() => navigateToStep(currentStep - 1)}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Previous
        </button>
      )}
      {currentStep === 1 && (
        <button
          onClick={() => canProceedToNextStep() && navigateToStep(2)}
          disabled={!canProceedToNextStep()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
          <ArrowRight className="w-4 h-4 ml-2" />
        </button>
      )}
      {currentStep === 2 && (
        <button
          onClick={() => canProceedToNextStep() && navigateToStep(3)}
          disabled={!canProceedToNextStep()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
          <ArrowRight className="w-4 h-4 ml-2" />
        </button>
      )}
      {currentStep === 3 && canProceedToNextStep() && (
        <button
          onClick={() => navigateToStep(4)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
        >
          Next
          <ArrowRight className="w-4 h-4 ml-2" />
        </button>
      )}
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/integrations/connections')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <SectionHeader
            icon={<Database className="h-7 w-7" />}
            title="Create Connection"
            subtitle="Set up a new data source connection"
          />
        </div>
      </div>

      {/* Step Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <button
                onClick={() => navigateToStep(step.id)}
                disabled={currentStep < step.id}
                className={`flex items-center justify-center w-8 h-8 rounded-full transition-all ${
                  currentStep >= step.id 
                    ? 'bg-blue-600 text-white hover:bg-blue-700' 
                    : 'bg-gray-200 text-gray-600'
                } ${currentStep >= step.id ? 'cursor-pointer' : 'cursor-not-allowed'}`}
              >
                {currentStep > step.id ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-medium">{step.id}</span>
                )}
              </button>
              <button
                onClick={() => navigateToStep(step.id)}
                disabled={currentStep < step.id}
                className={`ml-2 text-sm font-medium transition-colors ${
                  currentStep >= step.id 
                    ? 'text-blue-600 hover:text-blue-700 cursor-pointer' 
                    : 'text-gray-500 cursor-not-allowed'
                }`}
              >
                {step.title}
              </button>
              {index < steps.length - 1 && (
                <div className={`w-16 h-0.5 mx-4 ${
                  currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="bg-white rounded-lg shadow p-8">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
        {currentStep === 4 && renderStep4()}
      </div>

      {/* Navigation */}
      {renderNavigationButtons()}
    </div>
  );
};

export default CreateConnection; 
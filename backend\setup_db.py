#!/usr/bin/env python3
"""
Database setup script for PIM API

This script creates the database tables and runs initial migrations.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.db import create_tables, check_db_health
from models.product import Product

def main():
    """Main setup function"""
    print("🚀 Setting up PIM API Database...")
    
    # Check database connection
    print("📡 Checking database connection...")
    if not check_db_health():
        print("❌ Database connection failed!")
        print("Please ensure PostgreSQL is running and DATABASE_URL is correct.")
        sys.exit(1)
    
    print("✅ Database connection successful!")
    
    # Create tables
    print("📋 Creating database tables...")
    try:
        create_tables()
        print("✅ Database tables created successfully!")
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        sys.exit(1)
    
    print("🎉 Database setup completed successfully!")
    print("\n📖 Next steps:")
    print("1. Run the API: python main.py")
    print("2. Visit API docs: http://localhost:8000/docs")
    print("3. Test health endpoint: http://localhost:8000/api/health")

if __name__ == "__main__":
    main()

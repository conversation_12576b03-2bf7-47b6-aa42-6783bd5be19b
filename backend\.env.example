# Server Configuration
HOST=0.0.0.0
PORT=8000

# Environment
ENVIRONMENT=development

# Database Configuration (SQLite for development)
DATABASE_URL=sqlite:///./pim_db.sqlite
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pim_db
DB_USER=postgres
DB_PASSWORD=password

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# API Configuration
API_PREFIX=/api
API_VERSION=v1

# Logging
LOG_LEVEL=info

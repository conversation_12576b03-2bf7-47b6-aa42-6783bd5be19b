# Server Configuration
HOST=0.0.0.0
PORT=8000

# Environment
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/pim_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pim_db
DB_USER=postgres
DB_PASSWORD=password

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# API Configuration
API_PREFIX=/api
API_VERSION=v1

# Logging
LOG_LEVEL=info

import React from 'react'
import { useParams, Navigate } from 'react-router-dom'
import { User } from 'lucide-react'
import SectionHeader from '../components/SectionHeader'

const AdminShopAsCustomerPage: React.FC = () => {
  const { customerId } = useParams<{ customerId: string }>()

  if (!customerId) {
    return <Navigate to="/customers" replace />
  }

  return (
    <div className="space-y-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <SectionHeader
            icon={<User className="h-7 w-7" />}
            title="Shop as Customer"
            subtitle="This feature allows administrators to shop as a specific customer to test their experience."
          />
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Customer ID: {customerId}
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    This feature is currently under development. It will allow administrators to:
                  </p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>View the storefront as this specific customer</li>
                    <li>See customer-specific pricing and products</li>
                    <li>Test the customer's shopping experience</li>
                    <li>Debug customer-specific issues</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 flex space-x-3">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled
            >
              Start Shopping Session
            </button>
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              onClick={() => window.history.back()}
            >
              Back to Customer Details
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminShopAsCustomerPage

# Component Naming Conventions & Usage Guide

This document provides a comprehensive guide to all UI components used throughout the application, including their naming conventions, usage patterns, and code examples.

## Table of Contents

1. [Buttons](#buttons)
2. [Form Elements](#form-elements)
3. [Dropdowns](#dropdowns)
4. [Layout Components](#layout-components)
5. [Tables](#tables)
6. [Status Badges](#status-badges)
7. [Modals](#modals)
8. [Navigation](#navigation)
9. [Loading States](#loading-states)
10. [Alerts](#alerts)

---

## Buttons

### Primary Button
**Naming Convention:** `btn btn-primary`
**Usage:** Main action buttons (Create, Save, Submit, etc.)
**Example:**
```tsx
<button className="btn btn-primary flex items-center">
  <Plus className="h-4 w-4 mr-2" />
  Create New
</button>
```

### Secondary Button
**Naming Convention:** `btn btn-secondary`
**Usage:** Secondary actions (Cancel, Edit, etc.)
**Example:**
```tsx
<button className="btn btn-secondary flex items-center">
  <Edit className="h-4 w-4 mr-2" />
  Edit
</button>
```

### Danger Button
**Naming Convention:** `btn bg-red-600 text-white hover:bg-red-700`
**Usage:** Destructive actions (Delete, Remove, etc.)
**Example:**
```tsx
<button className="btn bg-red-600 text-white hover:bg-red-700 flex items-center">
  <Trash2 className="h-4 w-4 mr-2" />
  Delete
</button>
```

### Icon Button
**Naming Convention:** `p-1.5 text-{color}-600 hover:text-{color}-900 hover:bg-{color}-50 rounded-md`
**Usage:** Actions with only icons (View, Edit, Delete in tables)
**Example:**
```tsx
<button className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-md transition-colors">
  <Eye className="h-4 w-4" />
</button>
```

---

## Form Elements

### Text Input
**Naming Convention:** `input`
**Usage:** Standard text input fields
**Example:**
```tsx
<input
  type="text"
  placeholder="Enter text..."
  className="input"
/>
```

### Search Input
**Naming Convention:** `search-input pl-10`
**Usage:** Search fields with icon
**Example:**
```tsx
<div className="relative">
  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
  <input
    type="text"
    placeholder="Search..."
    className="search-input pl-10 w-80"
  />
</div>
```

### Textarea
**Naming Convention:** `input` (with rows attribute)
**Usage:** Multi-line text input
**Example:**
```tsx
<textarea
  placeholder="Enter description..."
  rows={3}
  className="input"
/>
```

---

## Dropdowns

### Single Select Dropdown
**Component:** `CustomSelect`
**Usage:** Dropdown for single selection
**Example:**
```tsx
<CustomSelect
  options={selectOptions}
  value={singleSelectValue}
  onChange={setSingleSelectValue}
  placeholder="Select an option..."
  className="w-64"
/>
```

### Multi Select Dropdown
**Component:** `MultiSelectDropdown`
**Usage:** Dropdown for multiple selections
**Example:**
```tsx
<MultiSelectDropdown
  options={multiSelectOptions}
  value={selectedOptions}
  onChange={setSelectedOptions}
  placeholder="Select technologies..."
  className="w-64"
/>
```

---

## Layout Components

### Standard Card
**Naming Convention:** `card`
**Usage:** Basic card container with padding and shadow
**Example:**
```tsx
<div className="card">
  <h3 className="text-lg font-medium text-gray-900 mb-2">Card Title</h3>
  <p className="text-gray-600">This is a standard card with content.</p>
</div>
```

### Interactive Card
**Naming Convention:** `card hover:shadow-md transition-all duration-200`
**Usage:** Cards with hover effects
**Example:**
```tsx
<div className="card hover:shadow-md transition-all duration-200 cursor-pointer">
  <div className="flex items-center justify-between">
    <div>
      <h3 className="text-lg font-medium text-gray-900">Interactive Card</h3>
      <p className="text-gray-600">Hover to see effects</p>
    </div>
    <ChevronDown className="h-5 w-5 text-gray-400" />
  </div>
</div>
```

---

## Tables

### Data Table
**Naming Convention:** Standard table with action buttons
**Usage:** Data display with actions
**Example:**
```tsx
<div className="overflow-hidden border border-gray-200 rounded-lg">
  <table className="min-w-full divide-y divide-gray-200">
    <thead className="bg-gray-50">
      <tr>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Name
        </th>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Status
        </th>
        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
          Actions
        </th>
      </tr>
    </thead>
    <tbody className="bg-white divide-y divide-gray-200">
      <tr>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          Sample Item
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
            Active
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center space-x-2 justify-end">
            <button className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-md">
              <Eye className="h-4 w-4" />
            </button>
            <button className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
              <Edit className="h-4 w-4" />
            </button>
            <button className="p-1.5 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md">
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
```

---

## Status Badges

### Status Badge
**Naming Convention:** `inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-{color}-100 text-{color}-800`
**Usage:** Status indicators with colored background
**Example:**
```tsx
<span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
  Active
</span>
```

**Color Variants:**
- Success: `bg-green-100 text-green-800`
- Warning: `bg-yellow-100 text-yellow-800`
- Error: `bg-red-100 text-red-800`
- Neutral: `bg-gray-100 text-gray-800`

---

## Modals

### Modal Overlay
**Naming Convention:** `fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50`
**Usage:** Modal background overlay
**Example:**
```tsx
<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-lg font-medium text-gray-900">Modal Title</h3>
      <button className="text-gray-400 hover:text-gray-600">
        <X className="h-5 w-5" />
      </button>
    </div>
    <p className="text-gray-600 mb-4">This is a modal content example.</p>
    <div className="flex justify-end space-x-3">
      <button className="btn btn-secondary">Cancel</button>
      <button className="btn btn-primary">Confirm</button>
    </div>
  </div>
</div>
```

---

## Navigation

### Tab Navigation
**Naming Convention:** `flex space-x-8 border-b border-gray-200`
**Usage:** Horizontal tab navigation
**Example:**
```tsx
<div className="border-b border-gray-200">
  <nav className="flex space-x-8">
    <button className="py-2 px-1 border-b-2 border-blue-500 text-sm font-medium text-blue-600">
      Active Tab
    </button>
    <button className="py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
      Inactive Tab
    </button>
  </nav>
</div>
```

---

## Loading States

### Loading Spinner
**Naming Convention:** `animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600`
**Usage:** Animated loading spinner
**Example:**
```tsx
<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
```

---

## Alerts

### Success Alert
**Naming Convention:** `bg-green-50 border border-green-200 rounded-md p-4`
**Usage:** Success message alert
**Example:**
```tsx
<div className="bg-green-50 border border-green-200 rounded-md p-4">
  <div className="flex">
    <Check className="h-5 w-5 text-green-400" />
    <div className="ml-3">
      <h3 className="text-sm font-medium text-green-800">Success</h3>
      <div className="mt-2 text-sm text-green-700">
        Operation completed successfully.
      </div>
    </div>
  </div>
</div>
```

### Error Alert
**Naming Convention:** `bg-red-50 border border-red-200 rounded-md p-4`
**Usage:** Error message alert
**Example:**
```tsx
<div className="bg-red-50 border border-red-200 rounded-md p-4">
  <div className="flex">
    <X className="h-5 w-5 text-red-400" />
    <div className="ml-3">
      <h3 className="text-sm font-medium text-red-800">Error</h3>
      <div className="mt-2 text-sm text-red-700">
        Something went wrong. Please try again.
      </div>
    </div>
  </div>
</div>
```

---

## How to Use This Guide

When you need to implement a component in your code:

1. **Identify the component type** from the categories above
2. **Use the naming convention** provided for the component
3. **Copy the example code** and adapt it to your needs
4. **Follow the usage patterns** shown in the examples

### Example Request to AI Assistant:

When asking for component implementation, use phrases like:
- "Use the `btn btn-primary` pattern for the submit button"
- "Implement a data table using the standard table pattern with action buttons"
- "Create a modal using the modal overlay pattern"
- "Add a status badge with the green success variant"

### Component Categories Reference:

- **Buttons:** `btn`, `btn-primary`, `btn-secondary`, icon buttons
- **Forms:** `input`, `search-input`, textarea
- **Dropdowns:** `CustomSelect`, `MultiSelectDropdown`
- **Layout:** `card`, interactive cards
- **Tables:** Standard data table pattern
- **Badges:** Status badges with color variants
- **Modals:** Modal overlay pattern
- **Navigation:** Tab navigation
- **Loading:** Loading spinner
- **Alerts:** Success and error alerts

This naming convention ensures consistency across the application and makes it easy to maintain and extend the UI components. 
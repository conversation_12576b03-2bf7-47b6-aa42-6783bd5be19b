// Mock data for multi-tenant authentication system
import {
  User,
  Organization,
  Workspace,
  Role,
  Permission,
  UserMembership,
  Invitation,
  ActivityLog,

  RESOURCES,
  ACTIONS,
  SCOPES
} from '../types/auth'

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'user_1',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: 'Doe',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
    isActive: true,
    lastLoginAt: '2023-06-22T14:30:00Z',
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2023-06-22T14:30:00Z',
    preferences: {
      theme: 'light',
      timezone: 'America/New_York',
      language: 'en',
      notifications: {
        email: true,
        push: true,
        slack: false
      }
    }
  },
  {
    id: 'user_2',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
    isActive: true,
    lastLoginAt: '2023-06-22T09:15:00Z',
    createdAt: '2023-02-01T14:20:00Z',
    updatedAt: '2023-06-22T09:15:00Z',
    preferences: {
      theme: 'dark',
      timezone: 'America/Los_Angeles',
      language: 'en',
      notifications: {
        email: true,
        push: false,
        slack: true
      }
    }
  },
  {
    id: 'user_3',
    email: '<EMAIL>',
    firstName: 'Mike',
    lastName: 'Wilson',
    isActive: true,
    createdAt: '2023-03-10T11:30:00Z',
    updatedAt: '2023-06-20T16:45:00Z',
    preferences: {
      theme: 'light',
      timezone: 'Europe/London',
      language: 'en',
      notifications: {
        email: true,
        push: true,
        slack: false
      }
    }
  }
]

// Mock Organizations
export const mockOrganizations: Organization[] = [
  {
    id: 'org_1',
    name: 'Acme Corporation',
    slug: 'acme-corp',
    description: 'Leading e-commerce solutions provider',
    logo: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=64&h=64&fit=crop',
    website: 'https://acmecorp.com',
    industry: 'E-commerce',
    size: 'large',
    plan: 'enterprise',
    isActive: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-06-01T12:00:00Z',
    billing: {
      customerId: 'cus_acme123',
      subscriptionId: 'sub_acme456',
      currentPeriodStart: '2023-06-01T00:00:00Z',
      currentPeriodEnd: '2023-07-01T00:00:00Z',
      status: 'active'
    },
    limits: {
      maxWorkspaces: 50,
      maxUsers: 500,
      maxIntegrations: 100,
      maxJobs: 1000,
      dataRetentionDays: 365
    },
    settings: {
      allowUserInvites: true,
      requireTwoFactor: true,
      allowedDomains: ['acmecorp.com'],
      sessionTimeoutMinutes: 480
    }
  },
  {
    id: 'org_2',
    name: 'TechStart Inc',
    slug: 'techstart',
    description: 'Innovative startup in the tech space',
    industry: 'Technology',
    size: 'startup',
    plan: 'professional',
    isActive: true,
    createdAt: '2023-03-01T00:00:00Z',
    updatedAt: '2023-06-15T10:30:00Z',
    billing: {
      currentPeriodStart: '2023-06-01T00:00:00Z',
      currentPeriodEnd: '2023-07-01T00:00:00Z',
      status: 'active'
    },
    limits: {
      maxWorkspaces: 10,
      maxUsers: 50,
      maxIntegrations: 20,
      maxJobs: 100,
      dataRetentionDays: 90
    },
    settings: {
      allowUserInvites: true,
      requireTwoFactor: false,
      allowedDomains: ['techstart.io'],
      sessionTimeoutMinutes: 240
    }
  }
]

// Mock Workspaces
export const mockWorkspaces: Workspace[] = [
  {
    id: 'workspace_1',
    organizationId: 'org_1',
    name: 'North America',
    slug: 'north-america',
    description: 'North American operations and stores',
    color: '#3B82F6',
    icon: '🇺🇸',
    isActive: true,
    createdAt: '2023-01-15T00:00:00Z',
    updatedAt: '2023-06-01T12:00:00Z',
    settings: {
      timezone: 'America/New_York',
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
      allowGuestAccess: false,
      defaultJobSchedule: '0 */6 * * *'
    },
    limits: {
      maxIntegrations: 20,
      maxJobs: 100,
      maxApiCalls: 100000
    },
    usage: {
      integrationCount: 3,
      jobCount: 8,
      apiCallsThisMonth: 45230,
      dataPointsThisMonth: 125000,
      lastActivityAt: '2023-06-22T14:30:00Z'
    }
  },
  {
    id: 'workspace_2',
    organizationId: 'org_1',
    name: 'Europe',
    slug: 'europe',
    description: 'European operations and stores',
    color: '#10B981',
    icon: '🇪🇺',
    isActive: true,
    createdAt: '2023-02-01T00:00:00Z',
    updatedAt: '2023-06-10T08:15:00Z',
    settings: {
      timezone: 'Europe/London',
      currency: 'EUR',
      dateFormat: 'DD/MM/YYYY',
      allowGuestAccess: false,
      defaultJobSchedule: '0 */4 * * *'
    },
    limits: {
      maxIntegrations: 15,
      maxJobs: 75,
      maxApiCalls: 75000
    },
    usage: {
      integrationCount: 2,
      jobCount: 5,
      apiCallsThisMonth: 28450,
      dataPointsThisMonth: 89000,
      lastActivityAt: '2023-06-22T09:15:00Z'
    }
  },
  {
    id: 'workspace_3',
    organizationId: 'org_2',
    name: 'Main Store',
    slug: 'main-store',
    description: 'Primary e-commerce operations',
    color: '#8B5CF6',
    icon: '🏪',
    isActive: true,
    createdAt: '2023-03-10T00:00:00Z',
    updatedAt: '2023-06-20T16:45:00Z',
    settings: {
      timezone: 'Europe/London',
      currency: 'GBP',
      dateFormat: 'DD/MM/YYYY',
      allowGuestAccess: true,
      defaultJobSchedule: '0 */2 * * *'
    },
    limits: {
      maxIntegrations: 10,
      maxJobs: 50,
      maxApiCalls: 50000
    },
    usage: {
      integrationCount: 1,
      jobCount: 3,
      apiCallsThisMonth: 12340,
      dataPointsThisMonth: 34500,
      lastActivityAt: '2023-06-20T16:45:00Z'
    }
  }
]

// Mock Permissions
export const mockPermissions: Permission[] = [
  // Integration permissions
  { id: 'perm_1', resource: RESOURCES.INTEGRATIONS, action: ACTIONS.CREATE, scope: SCOPES.WORKSPACE },
  { id: 'perm_2', resource: RESOURCES.INTEGRATIONS, action: ACTIONS.READ, scope: SCOPES.WORKSPACE },
  { id: 'perm_3', resource: RESOURCES.INTEGRATIONS, action: ACTIONS.UPDATE, scope: SCOPES.WORKSPACE },
  { id: 'perm_4', resource: RESOURCES.INTEGRATIONS, action: ACTIONS.DELETE, scope: SCOPES.WORKSPACE },
  { id: 'perm_5', resource: RESOURCES.INTEGRATIONS, action: ACTIONS.CONFIGURE, scope: SCOPES.WORKSPACE },

  // Job permissions
  { id: 'perm_6', resource: RESOURCES.JOBS, action: ACTIONS.CREATE, scope: SCOPES.WORKSPACE },
  { id: 'perm_7', resource: RESOURCES.JOBS, action: ACTIONS.READ, scope: SCOPES.WORKSPACE },
  { id: 'perm_8', resource: RESOURCES.JOBS, action: ACTIONS.UPDATE, scope: SCOPES.WORKSPACE },
  { id: 'perm_9', resource: RESOURCES.JOBS, action: ACTIONS.DELETE, scope: SCOPES.WORKSPACE },
  { id: 'perm_10', resource: RESOURCES.JOBS, action: ACTIONS.EXECUTE, scope: SCOPES.WORKSPACE },

  // User permissions
  { id: 'perm_11', resource: RESOURCES.USERS, action: ACTIONS.READ, scope: SCOPES.WORKSPACE },
  { id: 'perm_12', resource: RESOURCES.USERS, action: ACTIONS.INVITE, scope: SCOPES.WORKSPACE },
  { id: 'perm_13', resource: RESOURCES.USERS, action: ACTIONS.UPDATE, scope: SCOPES.WORKSPACE },
  { id: 'perm_14', resource: RESOURCES.USERS, action: ACTIONS.DELETE, scope: SCOPES.WORKSPACE },

  // Data permissions
  { id: 'perm_15', resource: RESOURCES.DATA, action: ACTIONS.READ, scope: SCOPES.WORKSPACE },
  { id: 'perm_16', resource: RESOURCES.DATA, action: ACTIONS.EXPORT, scope: SCOPES.WORKSPACE },

  // Settings permissions
  { id: 'perm_17', resource: RESOURCES.SETTINGS, action: ACTIONS.READ, scope: SCOPES.WORKSPACE },
  { id: 'perm_18', resource: RESOURCES.SETTINGS, action: ACTIONS.UPDATE, scope: SCOPES.WORKSPACE },

  // Billing permissions
  { id: 'perm_19', resource: RESOURCES.BILLING, action: ACTIONS.READ, scope: SCOPES.ORGANIZATION },
  { id: 'perm_20', resource: RESOURCES.BILLING, action: ACTIONS.UPDATE, scope: SCOPES.ORGANIZATION },
]

// Mock Roles
export const mockRoles: Role[] = [
  {
    id: 'role_workspace_owner',
    name: 'Workspace Owner',
    description: 'Full control over workspace and all its resources',
    level: 'workspace',
    isSystem: true,
    isCustom: false,
    permissions: [
      'perm_1', 'perm_2', 'perm_3', 'perm_4', 'perm_5', // All integration permissions
      'perm_6', 'perm_7', 'perm_8', 'perm_9', 'perm_10', // All job permissions
      'perm_11', 'perm_12', 'perm_13', 'perm_14', // All user permissions
      'perm_15', 'perm_16', // All data permissions
      'perm_17', 'perm_18' // All settings permissions
    ].map(id => mockPermissions.find(p => p.id === id)!),
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    metadata: {
      color: '#DC2626',
      icon: '👑',
      category: 'admin'
    }
  },
  {
    id: 'role_integration_manager',
    name: 'Integration Manager',
    description: 'Manage integrations and sync jobs',
    level: 'workspace',
    isSystem: true,
    isCustom: false,
    permissions: [
      'perm_1', 'perm_2', 'perm_3', 'perm_4', 'perm_5', // All integration permissions
      'perm_6', 'perm_7', 'perm_8', 'perm_9', 'perm_10', // All job permissions
      'perm_11', // Read users
      'perm_15', 'perm_16', // All data permissions
      'perm_17' // Read settings
    ].map(id => mockPermissions.find(p => p.id === id)!),
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    metadata: {
      color: '#2563EB',
      icon: '🔧',
      category: 'manager'
    }
  },
  {
    id: 'role_developer',
    name: 'Developer',
    description: 'Create and modify integrations and jobs',
    level: 'workspace',
    isSystem: true,
    isCustom: false,
    permissions: [
      'perm_1', 'perm_2', 'perm_3', 'perm_5', // Create, read, update, configure integrations
      'perm_6', 'perm_7', 'perm_8', 'perm_10', // Create, read, update, execute jobs
      'perm_15', // Read data
      'perm_17' // Read settings
    ].map(id => mockPermissions.find(p => p.id === id)!),
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    metadata: {
      color: '#059669',
      icon: '💻',
      category: 'developer'
    }
  },
  {
    id: 'role_analyst',
    name: 'Data Analyst',
    description: 'View and analyze data from integrations',
    level: 'workspace',
    isSystem: true,
    isCustom: false,
    permissions: [
      'perm_2', // Read integrations
      'perm_7', // Read jobs
      'perm_15', 'perm_16', // Read and export data
      'perm_17' // Read settings
    ].map(id => mockPermissions.find(p => p.id === id)!),
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    metadata: {
      color: '#7C3AED',
      icon: '📊',
      category: 'analyst'
    }
  },
  {
    id: 'role_viewer',
    name: 'Viewer',
    description: 'Read-only access to workspace resources',
    level: 'workspace',
    isSystem: true,
    isCustom: false,
    permissions: [
      'perm_2', // Read integrations
      'perm_7', // Read jobs
      'perm_15', // Read data
      'perm_17' // Read settings
    ].map(id => mockPermissions.find(p => p.id === id)!),
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    metadata: {
      color: '#6B7280',
      icon: '👁️',
      category: 'viewer'
    }
  }
]

// Mock User Memberships
export const mockUserMemberships: UserMembership[] = [
  {
    id: 'membership_1',
    userId: 'user_1',
    organizationId: 'org_1',
    workspaceId: 'workspace_1',
    roleId: 'role_workspace_owner',
    status: 'active',
    joinedAt: '2023-01-15T10:00:00Z',
    additionalPermissions: [],
    restrictions: {}
  },
  {
    id: 'membership_2',
    userId: 'user_1',
    organizationId: 'org_1',
    workspaceId: 'workspace_2',
    roleId: 'role_integration_manager',
    status: 'active',
    joinedAt: '2023-02-01T14:20:00Z',
    additionalPermissions: [],
    restrictions: {}
  },
  {
    id: 'membership_3',
    userId: 'user_2',
    organizationId: 'org_1',
    workspaceId: 'workspace_1',
    roleId: 'role_developer',
    status: 'active',
    joinedAt: '2023-02-01T14:20:00Z',
    additionalPermissions: [],
    restrictions: {}
  },
  {
    id: 'membership_4',
    userId: 'user_3',
    organizationId: 'org_2',
    workspaceId: 'workspace_3',
    roleId: 'role_workspace_owner',
    status: 'active',
    joinedAt: '2023-03-10T11:30:00Z',
    additionalPermissions: [],
    restrictions: {}
  }
]

// Mock Invitations
export const mockInvitations: Invitation[] = [
  {
    id: 'invite_1',
    email: '<EMAIL>',
    organizationId: 'org_1',
    workspaceId: 'workspace_1',
    roleId: 'role_analyst',
    invitedBy: 'user_1',
    invitedAt: '2023-06-20T10:00:00Z',
    expiresAt: '2023-06-27T10:00:00Z',
    status: 'pending',
    message: 'Welcome to the North America workspace! You\'ll be working with our data analysis team.',
    metadata: {
      source: 'manual',
      remindersSent: 1,
      lastReminderAt: '2023-06-22T10:00:00Z'
    }
  }
]

// Mock Activity Logs
export const mockActivityLogs: ActivityLog[] = [
  {
    id: 'log_1',
    userId: 'user_1',
    organizationId: 'org_1',
    workspaceId: 'workspace_1',
    action: 'integration.connected',
    resource: 'integrations',
    resourceId: 'shopify',
    details: {
      integrationName: 'Shopify',
      storeUrl: 'acme-store.myshopify.com'
    },
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    timestamp: '2023-06-22T14:30:00Z',
    riskLevel: 'low',
    flagged: false
  },
  {
    id: 'log_2',
    userId: 'user_2',
    organizationId: 'org_1',
    workspaceId: 'workspace_1',
    action: 'job.created',
    resource: 'jobs',
    resourceId: '1',
    details: {
      jobName: 'Shopify Product Sync',
      schedule: 'Every hour'
    },
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    timestamp: '2023-06-22T09:15:00Z',
    riskLevel: 'low',
    flagged: false
  }
]

import { delay } from '../utils/delay';

export interface Vendor {
  id: string;
  organization_name: string;
  contact_person: {
    name: string;
    title: string;
    email: string;
    phone: string;
  };
  product_share_count: number;
  price_list: string;
  last_sync: string | null;
  status: 'active' | 'pending' | 'suspended' | 'inactive';
  user_type: 'distributor' | 'supplier' | 'partner';
  guest_org_id: string;
  website?: string;
  address?: string;
  commission_rate?: number;
  tier?: 'basic' | 'standard' | 'premium';
}

export interface CreateVendorRequest {
  organization_name: string;
  contact_person: {
    name: string;
    title: string;
    email: string;
    phone: string;
  };
  website?: string;
  address?: string;
  tier?: 'basic' | 'standard' | 'premium';
  commission_rate?: number;
}

export interface UpdateVendorRequest {
  organization_name?: string;
  contact_person?: {
    name?: string;
    title?: string;
    email?: string;
    phone?: string;
  };
  website?: string;
  address?: string;
  tier?: 'basic' | 'standard' | 'premium';
  commission_rate?: number;
  status?: 'active' | 'pending' | 'suspended' | 'inactive';
}

export interface VendorListResult {
  vendors: Vendor[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface VendorQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  tier?: string;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

// Mock vendor data
const mockVendors: Vendor[] = [
  {
    id: 'vendor_1',
    organization_name: 'TechCorp Solutions',
    contact_person: {
      name: 'John Smith',
      title: 'Partnership Manager',
      email: '<EMAIL>',
      phone: '+****************'
    },
    product_share_count: 156,
    price_list: 'Distributor Pricing',
    last_sync: '2024-01-15T10:30:00Z',
    status: 'active',
    user_type: 'distributor',
    guest_org_id: 'guest_org_1',
    website: 'https://techcorp.com',
    address: '123 Tech Street, Silicon Valley, CA',
    commission_rate: 15,
    tier: 'premium'
  },
  {
    id: 'vendor_2',
    organization_name: 'HealthTech Inc',
    contact_person: {
      name: 'Sarah Johnson',
      title: 'Business Development',
      email: '<EMAIL>',
      phone: '+****************'
    },
    product_share_count: 89,
    price_list: 'Standard Pricing',
    last_sync: '2024-01-14T15:45:00Z',
    status: 'active',
    user_type: 'distributor',
    guest_org_id: 'guest_org_2',
    website: 'https://healthtech.com',
    address: '456 Health Ave, Medical District, NY',
    commission_rate: 12,
    tier: 'standard'
  },
  {
    id: 'vendor_3',
    organization_name: 'EcoFashion Co',
    contact_person: {
      name: 'Mike Green',
      title: 'Founder',
      email: '<EMAIL>',
      phone: '+****************'
    },
    product_share_count: 234,
    price_list: 'Wholesale Pricing',
    last_sync: null,
    status: 'pending',
    user_type: 'distributor',
    guest_org_id: 'guest_org_3',
    website: 'https://ecofashion.com',
    address: '789 Green Blvd, Eco City, OR',
    commission_rate: 10,
    tier: 'basic'
  },
  {
    id: 'vendor_4',
    organization_name: 'PhotoGear Ltd',
    contact_person: {
      name: 'Lisa Chen',
      title: 'Operations Manager',
      email: '<EMAIL>',
      phone: '+****************'
    },
    product_share_count: 67,
    price_list: 'Premium Pricing',
    last_sync: '2024-01-10T08:20:00Z',
    status: 'suspended',
    user_type: 'distributor',
    guest_org_id: 'guest_org_4',
    website: 'https://photogear.com',
    address: '321 Camera Way, Photo Town, CA',
    commission_rate: 18,
    tier: 'premium'
  },
  {
    id: 'vendor_5',
    organization_name: 'ElectroMart',
    contact_person: {
      name: 'David Wilson',
      title: 'Sales Director',
      email: '<EMAIL>',
      phone: '+****************'
    },
    product_share_count: 198,
    price_list: 'Retail Pricing',
    last_sync: '2024-01-16T09:15:00Z',
    status: 'active',
    user_type: 'distributor',
    guest_org_id: 'guest_org_5',
    website: 'https://electromart.com',
    address: '654 Electric Ave, Tech City, TX',
    commission_rate: 14,
    tier: 'standard'
  }
];

export const vendorApi = {
  // Get all vendors with filtering and pagination
  getVendors: async (params?: VendorQueryParams): Promise<VendorListResult> => {
    await delay();
    
    let filteredVendors = [...mockVendors];

    // Apply filters
    if (params?.search) {
      const search = params.search.toLowerCase();
      filteredVendors = filteredVendors.filter(vendor =>
        vendor.organization_name.toLowerCase().includes(search) ||
        vendor.contact_person.name.toLowerCase().includes(search) ||
        vendor.contact_person.email.toLowerCase().includes(search)
      );
    }

    if (params?.status) {
      filteredVendors = filteredVendors.filter(vendor => vendor.status === params.status);
    }

    if (params?.tier) {
      filteredVendors = filteredVendors.filter(vendor => vendor.tier === params.tier);
    }

    // Apply sorting
    if (params?.sort_by) {
      const sortBy = params.sort_by as keyof Vendor;
      const sortOrder = params.sort_order === 'ASC' ? 1 : -1;

      filteredVendors.sort((a, b) => {
        const aVal = a[sortBy];
        const bVal = b[sortBy];

        if (typeof aVal === 'string' && typeof bVal === 'string') {
          return aVal.localeCompare(bVal) * sortOrder;
        }

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return (aVal - bVal) * sortOrder;
        }

        return 0;
      });
    }

    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 20;
    const offset = (page - 1) * limit;
    const paginatedVendors = filteredVendors.slice(offset, offset + limit);

    return {
      vendors: paginatedVendors,
      total: filteredVendors.length,
      page,
      limit,
      total_pages: Math.ceil(filteredVendors.length / limit)
    };
  },

  // Get a single vendor by ID
  getVendor: async (id: string): Promise<Vendor> => {
    await delay();
    const vendor = mockVendors.find(v => v.id === id);
    if (!vendor) {
      throw new Error('Vendor not found');
    }
    return vendor;
  },

  // Create a new vendor
  createVendor: async (vendorData: CreateVendorRequest): Promise<Vendor> => {
    await delay();
    const newVendor: Vendor = {
      id: `vendor_${Date.now()}`,
      ...vendorData,
      product_share_count: 0,
      price_list: 'Standard Pricing',
      last_sync: null,
      status: 'pending',
      user_type: 'distributor',
      guest_org_id: `guest_org_${Date.now()}`,
      tier: vendorData.tier || 'basic',
      commission_rate: vendorData.commission_rate || 10
    };
    mockVendors.push(newVendor);
    return newVendor;
  },

  // Update an existing vendor
  updateVendor: async (id: string, updates: UpdateVendorRequest): Promise<Vendor> => {
    await delay();
    const vendorIndex = mockVendors.findIndex(v => v.id === id);
    if (vendorIndex === -1) {
      throw new Error('Vendor not found');
    }
    
    mockVendors[vendorIndex] = {
      ...mockVendors[vendorIndex],
      ...updates,
      contact_person: {
        ...mockVendors[vendorIndex].contact_person,
        ...updates.contact_person
      }
    };
    
    return mockVendors[vendorIndex];
  },

  // Delete a vendor
  deleteVendor: async (id: string): Promise<void> => {
    await delay();
    const vendorIndex = mockVendors.findIndex(v => v.id === id);
    if (vendorIndex === -1) {
      throw new Error('Vendor not found');
    }
    mockVendors.splice(vendorIndex, 1);
  },

  // Get vendor statistics
  getVendorStats: async () => {
    await delay();
    const totalVendors = mockVendors.length;
    const activeVendors = mockVendors.filter(v => v.status === 'active').length;
    const totalProductsShared = mockVendors.reduce((sum, v) => sum + v.product_share_count, 0);
    const totalRevenue = mockVendors.reduce((sum, v) => sum + (v.product_share_count * 100), 0); // Mock revenue calculation

    return {
      total_vendors: totalVendors,
      active_vendors: activeVendors,
      total_products_shared: totalProductsShared,
      total_revenue: totalRevenue,
      average_commission_rate: mockVendors.reduce((sum, v) => sum + (v.commission_rate || 0), 0) / totalVendors
    };
  }
}; 
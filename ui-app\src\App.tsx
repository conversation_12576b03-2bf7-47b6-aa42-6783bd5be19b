import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import './App.css';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { SettingsProvider } from './contexts/SettingsContext';
import { ChatProvider } from './contexts/ChatContext';
import Layout from './components/Layout';
import ChatBubble from './components/ChatBubble';
import ExpandedChatWindow from './components/ExpandedChatWindow';
import HomePage from './pages/HomePage';
import AppsPage from './pages/AppsPage';
import ProductsPage from './pages/ProductsPage';
import CreateProductPage from './pages/CreateProductPage';
import PriceListsPage from './pages/PriceListsPage';
import MarkupManagementPage from './pages/MarkupManagementPage';
import InboundSharingPage from './pages/InboundSharingPage';
import OutboundSharingPage from './pages/OutboundSharingPage';
import VendorNetworkPage from './pages/VendorNetworkPage';
import OrdersPage from './pages/OrdersPage';
import OrderDetailsPage from './pages/OrderDetailsPageNew';
import BigCommerceOrderDetailsPage from './pages/BigCommerceOrderDetailsPage';
import ShopifyOrderDetailsPage from './pages/ShopifyOrderDetailsPage';
import CustomersPage from './pages/CustomersPage';
import CustomerDetailsPage from './pages/CustomerDetailsPage';
import MyOrganizationPage from './pages/MyOrganizationPage';
import UserDetailPage from './pages/UserDetailPage';
import SettingsPage from './pages/SettingsPage';
import ProductFormPage from './pages/ProductFormPage';
import AdminShopAsCustomerPage from './pages/AdminShopAsCustomerPage';
import WhatsAppPage from './pages/WhatsAppPage';
import NotFoundPage from './pages/NotFoundPage';
import CategoriesPage from './pages/CategoriesPage';
import BrandsPage from './pages/BrandsPage';
import ProductDetailsPage from './pages/ProductDetailsPage';
import MediaPage from './pages/MediaPage';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import CreateProductPageModern from './pages/CreateProductPageModern';
import PIMProductsPage from './pages/PIMProductsPage';
import NucleusIQPage from './pages/NucleusIQPage';
import OrganizationPage from './pages/OrganizationPage';
import OrganizationDetailPage from './pages/OrganizationDetailPage';

// Import integration pages
import IntegrationsDashboard from './pages/Integrations/Dashboard';
import ConnectionsList from './pages/Integrations/Connections/ConnectionsList';
import CreateConnection from './pages/Integrations/Connections/CreateConnection';
import ConnectionDetail from './pages/Integrations/Connections/ConnectionDetail';

// Import platform setup components
import SalesforceSetup from './components/Integrations/Platforms/SalesforceSetup';
import ZohoSetup from './components/Integrations/Platforms/ZohoSetup';

const queryClient = new QueryClient();



function App() {
  const location = useLocation();
  // Collapse sidebar for create/edit product routes
  const collapseSidebar =
    location.pathname === '/products/create' ||
    location.pathname.startsWith('/products/edit/');


  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <SettingsProvider>
          <ChatProvider>
            <Layout collapseSidebar={collapseSidebar}>
            <Routes>
              {/* Original App Routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/apps" element={<AppsPage />} />
              <Route path="/apps/whatsapp" element={<WhatsAppPage />} />
              <Route path="/orders" element={<OrdersPage />} />
              <Route path="/orders/all" element={<OrdersPage />} />
              <Route path="/orders/bigcommerce" element={<OrdersPage />} />
              <Route path="/orders/shopify" element={<OrdersPage />} />
              <Route path="/orders/amazon" element={<OrdersPage />} />
              <Route path="/orders/:id" element={<OrderDetailsPage />} />
              <Route path="/orders/bigcommerce/:id" element={<BigCommerceOrderDetailsPage />} />
              <Route path="/orders/shopify/:id" element={<ShopifyOrderDetailsPage />} />
              <Route path="/orders/amazon/:id" element={<OrderDetailsPage />} />
              <Route path="/products" element={<ProductsPage />} />
              <Route path="/products/view/:id" element={<ProductDetailsPage />} />
              <Route path="/products/create" element={<CreateProductPage />} />
              <Route path="/products/create-modern" element={<CreateProductPageModern />} />
              <Route path="/products/create-with-custom-fields" element={<ProductFormPage />} />
              <Route path="/products/edit/:id" element={<CreateProductPage />} />
              <Route path="/products/price-lists" element={<PriceListsPage />} />
              <Route path="/pim/markup" element={<MarkupManagementPage />} />
              <Route path="/pim/inbound-products" element={<InboundSharingPage />} />
              <Route path="/pim/outbound-products" element={<OutboundSharingPage />} />
              <Route path="/products/shared/vendors" element={<VendorNetworkPage />} />
              <Route path="/customers" element={<CustomersPage />} />
              <Route path="/customers/:id" element={<CustomerDetailsPage />} />
              <Route path="/customers/:id/edit" element={<CustomerDetailsPage />} />
              <Route path="/customers/new" element={<CustomerDetailsPage />} />
              <Route path="/admin/shop-as-customer/:customerId" element={<AdminShopAsCustomerPage />} />
              <Route path="/storefront" element={<HomePage />} />
              <Route path="/sales" element={<HomePage />} />
              <Route path="/media" element={<MediaPage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/settings/general" element={<SettingsPage />} />
              <Route path="/settings/jobs" element={<SettingsPage />} />
              <Route path="/settings/custom-data" element={<SettingsPage />} />
              <Route path="/settings/custom-data/system-fields" element={<SettingsPage />} />
              <Route path="/settings/custom-data/custom-fields" element={<SettingsPage />} />
              <Route path="/settings/organization" element={<OrganizationPage />} />
              <Route path="/settings/organization/:userId" element={<OrganizationDetailPage />} />

              <Route path="/settings/users" element={<Navigate to="/settings/users/my-organization/" replace />} />
              <Route path="/settings/users/my-organization" element={<MyOrganizationPage />} />
              <Route path="/settings/users/my-organization/" element={<MyOrganizationPage />} />
              <Route path="/settings/users/my-organization/:userId" element={<UserDetailPage />} />
              <Route path="/settings/users/my-organization/roles" element={<MyOrganizationPage />} />
              <Route path="/settings/users/my-organization/permissions" element={<Navigate to="/settings/users/my-organization/roles" replace />} />
              <Route path="/settings/users/guest-organization" element={<MyOrganizationPage />} />
              <Route path="/settings/users/guest-organization/users" element={<MyOrganizationPage />} />
              <Route path="/settings/users/guest-organization/users/:userId" element={<UserDetailPage />} />
              <Route path="/settings/notifications" element={<SettingsPage />} />
              <Route path="/settings/components" element={<SettingsPage />} />
              <Route path="/users/my-organization" element={<Navigate to="/settings/users/my-organization/" replace />} />
              <Route path="/users/my-organization/users" element={<Navigate to="/settings/users/my-organization/" replace />} />
              <Route path="/users/my-organization/:userId" element={<Navigate to="/settings/users/my-organization/:userId" replace />} />
              <Route path="/users/my-organization/roles" element={<Navigate to="/settings/users/my-organization/roles" replace />} />
              <Route path="/users/guest-organization" element={<Navigate to="/settings/users/guest-organization" replace />} />
              <Route path="/categories" element={<CategoriesPage />} />
              <Route path="/brands" element={<BrandsPage />} />
              <Route path="/products/:id" element={<ProductDetailsPage />} />
              <Route path="/products/all" element={<ProductsPage />} />
              <Route path="/pim/products/all" element={<PIMProductsPage />} />
              <Route path="/nucleusiq" element={<NucleusIQPage />} />
              
              {/* Integrations Routes */}
              <Route path="/integrations">
                <Route path="dashboard" element={<IntegrationsDashboard />} />
                
                {/* Connections */}
                <Route path="connections" element={<ConnectionsList />} />
                <Route path="connections/create" element={<Navigate to="/integrations/connections/create/step1" replace />} />
                <Route path="connections/create/step1" element={<CreateConnection />} />
                <Route path="connections/create/step2" element={<CreateConnection />} />
                <Route path="connections/create/step3" element={<CreateConnection />} />
                <Route path="connections/create/step4" element={<CreateConnection />} />
                <Route path="connections/:id" element={<ConnectionDetail />} />
                
                {/* Platform-specific setup routes */}
                <Route path="setup/salesforce" element={
                  <SalesforceSetup 
                    onComplete={(config) => console.log('Salesforce setup complete:', config)}
                    onBack={() => window.history.back()}
                  />
                } />
                <Route path="setup/zoho" element={
                  <ZohoSetup 
                    onComplete={(config) => console.log('Zoho setup complete:', config)}
                    onBack={() => window.history.back()}
                  />
                } />
                

              </Route>
              
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Layout>
          
          {/* Chat Components */}
          <ChatBubble />
          <ExpandedChatWindow />
          
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#10B981',
                  secondary: '#fff',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#EF4444',
                  secondary: '#fff',
                },
              },
            }}
          />
        </ChatProvider>
        </SettingsProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;

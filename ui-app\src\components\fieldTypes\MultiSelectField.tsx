import React, { useState, useRef, useEffect } from 'react'
import { ChevronDown, Check, X } from 'lucide-react'
import { FieldProps } from './index'

const MultiSelectField: React.FC<FieldProps> = ({
  id,
  value = [],
  onChange,
  placeholder = 'Select options...',
  required = false,
  disabled = false,
  className = '',
  error,
  options = []
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const selectedValues = Array.isArray(value) ? value : (value ? [value] : [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.value.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelect = (optionValue: string) => {
    const isSelected = selectedValues.includes(optionValue)
    const newValues = isSelected
      ? selectedValues.filter(v => v !== optionValue)
      : [...selectedValues, optionValue]
    
    onChange(newValues)
  }

  const handleRemove = (optionValue: string, e: React.MouseEvent) => {
    e.stopPropagation()
    const newValues = selectedValues.filter(v => v !== optionValue)
    onChange(newValues)
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange([])
  }

  const getSelectedOptions = () => {
    return selectedValues.map(val => options.find(opt => opt.value === val)).filter(Boolean)
  }

  const baseClasses = "w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors cursor-pointer min-h-[42px]"
  const errorClasses = error ? "border-red-300 focus:ring-red-500 focus:border-red-500" : "border-gray-300"
  const disabledClasses = disabled ? "bg-gray-100 cursor-not-allowed" : "hover:border-gray-400"

  return (
    <div className="relative space-y-1" ref={dropdownRef}>
      <div
        className={`${baseClasses} ${errorClasses} ${disabledClasses} ${className} flex items-center justify-between`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-required={required}
      >
        <div className="flex flex-wrap gap-1 flex-1 min-w-0">
          {selectedValues.length > 0 ? (
            getSelectedOptions().map((option) => (
              <span
                key={option?.value}
                className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
              >
                {option?.label}
                {!disabled && (
                  <button
                    type="button"
                    onClick={(e) => handleRemove(option?.value || '', e)}
                    className="ml-1 hover:text-blue-600 focus:outline-none"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </span>
            ))
          ) : (
            <span className="text-gray-500">{placeholder}</span>
          )}
        </div>
        
        <div className="flex items-center space-x-1 flex-shrink-0">
          {selectedValues.length > 0 && !disabled && (
            <button
              type="button"
              onClick={handleClear}
              className="text-gray-400 hover:text-gray-600 p-1"
              title="Clear all"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          <ChevronDown 
            className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          />
        </div>
      </div>

      {isOpen && !disabled && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
          {/* Search input for large option lists */}
          {options.length > 5 && (
            <div className="p-2 border-b border-gray-200">
              <input
                ref={inputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search options..."
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                autoFocus
              />
            </div>
          )}
          
          <div className="max-h-48 overflow-y-auto">
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500">
                {searchTerm ? 'No options found' : 'No options available'}
              </div>
            ) : (
              <>
                {/* Select All / Deselect All */}
                {filteredOptions.length > 1 && (
                  <div className="border-b border-gray-200">
                    <button
                      type="button"
                      onClick={() => {
                        const allFilteredValues = filteredOptions.map(opt => opt.value)
                        const allSelected = allFilteredValues.every(val => selectedValues.includes(val))
                        
                        if (allSelected) {
                          // Deselect all filtered options
                          const newValues = selectedValues.filter(val => !allFilteredValues.includes(val))
                          onChange(newValues)
                        } else {
                          // Select all filtered options
                          const newValues = [...new Set([...selectedValues, ...allFilteredValues])]
                          onChange(newValues)
                        }
                      }}
                      className="w-full text-left px-3 py-2 text-sm font-medium text-blue-600 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none"
                    >
                      {filteredOptions.every(opt => selectedValues.includes(opt.value)) ? 'Deselect All' : 'Select All'}
                    </button>
                  </div>
                )}
                
                {filteredOptions.map((option) => {
                  const isSelected = selectedValues.includes(option.value)
                  
                  return (
                    <button
                      key={option.value}
                      type="button"
                      onClick={() => handleSelect(option.value)}
                      className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 focus:bg-gray-50 focus:outline-none flex items-center justify-between"
                      role="option"
                      aria-selected={isSelected}
                    >
                      <span>{option.label}</span>
                      {isSelected && (
                        <Check className="h-4 w-4 text-blue-600" />
                      )}
                    </button>
                  )
                })}
              </>
            )}
          </div>
        </div>
      )}

      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      
      {selectedValues.length > 0 && (
        <p className="text-xs text-gray-500">
          {selectedValues.length} option{selectedValues.length !== 1 ? 's' : ''} selected
        </p>
      )}
    </div>
  )
}

export default MultiSelectField

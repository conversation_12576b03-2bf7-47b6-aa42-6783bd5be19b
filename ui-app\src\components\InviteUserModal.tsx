import { useState } from 'react'
import { X, Mail, Shield } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { mockRoles } from '../data/mockAuthData'
import RoleBadge from './RoleBadge'
import CustomSelect from './CustomSelect'
import toast from 'react-hot-toast'

interface InviteUserModalProps {
  onClose: () => void
  onInvite: (inviteData: InviteData) => void
}

interface InviteData {
  email: string
  roleId: string
  message?: string
}

const InviteUserModal = ({ onClose, onInvite }: InviteUserModalProps) => {
  const { currentWorkspace } = useAuth()
  const [formData, setFormData] = useState<InviteData>({
    email: '',
    roleId: 'role_viewer',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.email || !formData.roleId) {
      toast.error('Please fill in all required fields')
      return
    }

    setIsSubmitting(true)
    try {
      await onInvite(formData)
    } catch (error) {
      toast.error('Failed to send invitation')
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedRole = mockRoles.find(role => role.id === formData.roleId)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-in fade-in-0 duration-200">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 animate-in zoom-in-95 duration-200">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="h-10 w-10 bg-blue-50 rounded-lg flex items-center justify-center mr-3">
              <Mail className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Invite User</h3>
              <p className="text-sm text-gray-500">
                Add a new member to {currentWorkspace?.name}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label="Close modal"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="email"
                required
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="pl-9 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          {/* Role Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Role *
            </label>
            <CustomSelect
              options={mockRoles.map(role => ({
                value: role.id,
                label: `${role.name} - ${role.description}`
              }))}
              value={formData.roleId}
              onChange={(value) => setFormData({ ...formData, roleId: value })}
              required
            />

            {/* Role Preview */}
            {selectedRole && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md">
                <div className="flex items-center justify-between">
                  <RoleBadge role={selectedRole} size="sm" />
                  <span className="text-xs text-gray-500">
                    {selectedRole.permissions.length} permissions
                  </span>
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {selectedRole.description}
                </p>
              </div>
            )}
          </div>

          {/* Optional Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Welcome Message (Optional)
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => setFormData({ ...formData, message: e.target.value })}
              rows={3}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Welcome to our team! We're excited to have you join us..."
            />
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Sending...' : 'Send Invitation'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </form>

        {/* Info */}
        <div className="px-6 pb-6">
          <div className="bg-blue-50 rounded-md p-3">
            <div className="flex">
              <div className="flex-shrink-0">
                <Shield className="h-4 w-4 text-blue-600" />
              </div>
              <div className="ml-2">
                <p className="text-xs text-blue-800">
                  The user will receive an email invitation with instructions to join your workspace.
                  They'll have 7 days to accept the invitation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InviteUserModal

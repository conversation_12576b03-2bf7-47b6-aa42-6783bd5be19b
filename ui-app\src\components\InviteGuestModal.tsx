import { useState } from 'react'
import { X, Mail, Shield, Building, Calendar, Tag } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import CustomSelect from './CustomSelect'
import toast from 'react-hot-toast'

interface InviteGuestModalProps {
  onClose: () => void
  onInvite: (inviteData: GuestInviteData) => void
}

interface GuestInviteData {
  organization: string
  organizationType: string
  contactPerson: string
  email: string
  expirationDate: string
  message?: string
}

const InviteGuestModal = ({ onClose, onInvite }: InviteGuestModalProps) => {
  const { currentWorkspace } = useAuth()
  const [formData, setFormData] = useState<GuestInviteData>({
    organization: '',
    organizationType: '',
    contactPerson: '',
    email: '',
    expirationDate: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const accessLevelOptions = [
    { value: 'read-only', label: 'Read Only' },
    { value: 'limited', label: 'Limited Access' },
    { value: 'full', label: 'Full Access' }
  ]

  const organizationTypeOptions = [
    { value: 'supplier', label: 'Supplier' },
    { value: 'vendor', label: 'Vendor' },
    { value: 'retailer', label: 'Retailer' },
    { value: 'distributor', label: 'Distributor' },
    { value: 'b2b', label: 'B2B' }
  ]

  // Generate default expiration date (30 days from now)
  const getDefaultExpirationDate = () => {
    const date = new Date()
    date.setDate(date.getDate() + 30)
    return date.toISOString().split('T')[0]
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.email || !formData.organization || !formData.organizationType || !formData.contactPerson || !formData.expirationDate) {
      toast.error('Please fill in all required fields')
      return
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      toast.error('Please enter a valid email address')
      return
    }

    // Validate expiration date is in the future
    const expirationDate = new Date(formData.expirationDate)
    const today = new Date()
    if (expirationDate <= today) {
      toast.error('Expiration date must be in the future')
      return
    }

    setIsSubmitting(true)
    try {
      await onInvite(formData)
    } catch (error) {
      toast.error('Failed to send guest invitation')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof GuestInviteData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-in fade-in-0 duration-200">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 animate-in zoom-in-95 duration-200">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="h-10 w-10 bg-indigo-50 rounded-lg flex items-center justify-center mr-3">
              <Building className="h-5 w-5 text-indigo-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Add Organization</h3>
              <p className="text-sm text-gray-500">
                Invite external organization
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Organization Name */}
          <div>
            <label htmlFor="organization" className="block text-sm font-medium text-gray-700 mb-2">
              Organization Name *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Building className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                id="organization"
                value={formData.organization}
                onChange={(e) => handleInputChange('organization', e.target.value)}
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Organization name"
                required
              />
            </div>
          </div>

          {/* Organization Type */}
          <div>
            <label htmlFor="organizationType" className="block text-sm font-medium text-gray-700 mb-2">
              Organization Type *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <Tag className="h-4 w-4 text-gray-400" />
              </div>
              <CustomSelect
                options={organizationTypeOptions}
                value={formData.organizationType}
                onChange={(value) => handleInputChange('organizationType', value)}
                placeholder="Select organization type"
                required
                className="pl-10"
              />
            </div>
          </div>

          {/* Contact Person */}
          <div>
            <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700 mb-2">
              Contact Person *
            </label>
            <input
              type="text"
              id="contactPerson"
              value={formData.contactPerson}
              onChange={(e) => handleInputChange('contactPerson', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Contact person's name"
              required
            />
          </div>

          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="email"
                id="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          {/* Access Expires */}
          <div>
            <label htmlFor="expirationDate" className="block text-sm font-medium text-gray-700 mb-2">
              Access Expires *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="date"
                id="expirationDate"
                value={formData.expirationDate || getDefaultExpirationDate()}
                onChange={(e) => handleInputChange('expirationDate', e.target.value)}
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                min={new Date().toISOString().split('T')[0]}
                required
              />
            </div>
          </div>

          {/* Welcome Message */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
              Welcome Message (Optional)
            </label>
            <textarea
              id="message"
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Add a personal message to the invitation..."
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-gray-900 border border-transparent rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Sending...' : 'Send Invitation'}
            </button>
          </div>
        </form>

        {/* Info */}
        <div className="px-6 pb-6">
          <div className="bg-indigo-50 rounded-md p-3">
            <div className="flex">
              <div className="flex-shrink-0">
                <Shield className="h-4 w-4 text-indigo-600" />
              </div>
              <div className="ml-2">
                <p className="text-xs text-indigo-800">
                  If the Organization is already a part of Nuclues App, they will be added as an active user in your workspace.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InviteGuestModal

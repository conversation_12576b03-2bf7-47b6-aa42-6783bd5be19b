{"systemParams": "win32-x64-115", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@tanstack/react-query@^5.81.2", "@tiptap/core@^3.0.1", "@tiptap/extension-image@^3.0.1", "@tiptap/extension-link@^3.0.1", "@tiptap/extension-table-cell@^3.0.1", "@tiptap/extension-table-header@^3.0.1", "@tiptap/extension-table-row@^3.0.1", "@tiptap/extension-table@^3.0.1", "@tiptap/extension-text-align@^3.0.1", "@tiptap/extension-underline@^3.0.1", "@tiptap/pm@^3.0.1", "@tiptap/react@^3.0.1", "@tiptap/starter-kit@^3.0.1", "@types/react-dom@^18.2.15", "@types/react@^18.2.37", "@typescript-eslint/eslint-plugin@^6.10.0", "@typescript-eslint/parser@^6.10.0", "@vitejs/plugin-react@^4.1.1", "autoprefixer@^10.4.16", "axios@^1.6.2", "eslint-plugin-react-hooks@^4.6.0", "eslint-plugin-react-refresh@^0.4.4", "eslint@^8.53.0", "lucide-react@^0.294.0", "msw@^2.0.0", "postcss@^8.4.31", "react-dom@^18.2.0", "react-hook-form@^7.48.2", "react-hot-toast@^2.5.2", "react-query@^3.39.3", "react-router-dom@^6.20.1", "react@^18.2.0", "tailwindcss@^3.3.5", "typescript@^5.2.2", "uuid@^11.1.0", "vite@^4.5.0"], "lockfileEntries": {"@alloc/quick-lru@^5.2.0": "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz", "@ampproject/remapping@^2.2.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "@babel/code-frame@^7.27.1": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/compat-data@^7.27.2": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz", "@babel/core@^7.27.4": "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz", "@babel/generator@^7.28.0": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz", "@babel/helper-compilation-targets@^7.27.2": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-globals@^7.28.0": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "@babel/helper-module-imports@^7.27.1": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-transforms@^7.27.3": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "@babel/helper-plugin-utils@^7.27.1": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-string-parser@^7.27.1": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "@babel/helper-validator-identifier@^7.27.1": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "@babel/helper-validator-option@^7.27.1": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "@babel/helpers@^7.27.6": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "@babel/parser@^7.1.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "@babel/parser@^7.20.7": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "@babel/parser@^7.27.2": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "@babel/parser@^7.28.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "@babel/plugin-transform-react-jsx-self@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "@babel/plugin-transform-react-jsx-source@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "@babel/runtime@^7.23.8": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.5.5": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.6.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.7.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/template@^7.27.2": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/traverse@^7.27.1": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "@babel/traverse@^7.27.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "@babel/traverse@^7.28.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "@babel/types@^7.0.0": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.20.7": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.27.1": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.27.6": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@babel/types@^7.28.0": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "@bundled-es-modules/cookie@^2.0.1": "https://registry.npmjs.org/@bundled-es-modules/cookie/-/cookie-2.0.1.tgz", "@bundled-es-modules/statuses@^1.0.1": "https://registry.npmjs.org/@bundled-es-modules/statuses/-/statuses-1.0.1.tgz", "@bundled-es-modules/tough-cookie@^0.1.6": "https://registry.npmjs.org/@bundled-es-modules/tough-cookie/-/tough-cookie-0.1.6.tgz", "@esbuild/android-arm64@0.18.20": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.18.20.tgz#984b4f9c8d0377443cc2dfcef266d02244593622", "@esbuild/android-arm@0.18.20": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.20.tgz#fedb265bc3a589c84cc11f810804f234947c3682", "@esbuild/android-x64@0.18.20": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.18.20.tgz#35cf419c4cfc8babe8893d296cd990e9e9f756f2", "@esbuild/darwin-arm64@0.18.20": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.18.20.tgz#08172cbeccf95fbc383399a7f39cfbddaeb0d7c1", "@esbuild/darwin-x64@0.18.20": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.18.20.tgz#d70d5790d8bf475556b67d0f8b7c5bdff053d85d", "@esbuild/freebsd-arm64@0.18.20": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.20.tgz#98755cd12707f93f210e2494d6a4b51b96977f54", "@esbuild/freebsd-x64@0.18.20": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.20.tgz#c1eb2bff03915f87c29cece4c1a7fa1f423b066e", "@esbuild/linux-arm64@0.18.20": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.18.20.tgz#bad4238bd8f4fc25b5a021280c770ab5fc3a02a0", "@esbuild/linux-arm@0.18.20": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.18.20.tgz#3e617c61f33508a27150ee417543c8ab5acc73b0", "@esbuild/linux-ia32@0.18.20": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.18.20.tgz#699391cccba9aee6019b7f9892eb99219f1570a7", "@esbuild/linux-loong64@0.18.20": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.18.20.tgz#e6fccb7aac178dd2ffb9860465ac89d7f23b977d", "@esbuild/linux-mips64el@0.18.20": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.18.20.tgz#eeff3a937de9c2310de30622a957ad1bd9183231", "@esbuild/linux-ppc64@0.18.20": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.18.20.tgz#2f7156bde20b01527993e6881435ad79ba9599fb", "@esbuild/linux-riscv64@0.18.20": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.18.20.tgz#6628389f210123d8b4743045af8caa7d4ddfc7a6", "@esbuild/linux-s390x@0.18.20": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.18.20.tgz#255e81fb289b101026131858ab99fba63dcf0071", "@esbuild/linux-x64@0.18.20": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.18.20.tgz#c7690b3417af318a9b6f96df3031a8865176d338", "@esbuild/netbsd-x64@0.18.20": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.18.20.tgz#30e8cd8a3dded63975e2df2438ca109601ebe0d1", "@esbuild/openbsd-x64@0.18.20": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.18.20.tgz#7812af31b205055874c8082ea9cf9ab0da6217ae", "@esbuild/sunos-x64@0.18.20": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.18.20.tgz#d5c275c3b4e73c9b0ecd38d1ca62c020f887ab9d", "@esbuild/win32-arm64@0.18.20": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.20.tgz#73bc7f5a9f8a77805f357fab97f290d0e4820ac9", "@esbuild/win32-ia32@0.18.20": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.18.20.tgz#ec93cbf0ef1085cc12e71e0d661d20569ff42102", "@esbuild/win32-x64@0.18.20": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz", "@eslint-community/eslint-utils@^4.2.0": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "@eslint-community/eslint-utils@^4.4.0": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "@eslint-community/regexpp@^4.5.1": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "@eslint-community/regexpp@^4.6.1": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "@eslint/eslintrc@^2.1.4": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz", "@eslint/js@8.57.1": "https://registry.npmjs.org/@eslint/js/-/js-8.57.1.tgz", "@floating-ui/core@^1.7.2": "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.2.tgz", "@floating-ui/dom@^1.0.0": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.2.tgz", "@floating-ui/utils@^0.2.10": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.10.tgz", "@humanwhocodes/config-array@^0.13.0": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz", "@humanwhocodes/module-importer@^1.0.1": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "@humanwhocodes/object-schema@^2.0.3": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz", "@inquirer/confirm@^5.0.0": "https://registry.npmjs.org/@inquirer/confirm/-/confirm-5.1.13.tgz", "@inquirer/core@^10.1.14": "https://registry.npmjs.org/@inquirer/core/-/core-10.1.14.tgz", "@inquirer/figures@^1.0.12": "https://registry.npmjs.org/@inquirer/figures/-/figures-1.0.12.tgz", "@inquirer/type@^3.0.7": "https://registry.npmjs.org/@inquirer/type/-/type-3.0.7.tgz", "@isaacs/cliui@^8.0.2": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz", "@jridgewell/gen-mapping@^0.3.12": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/sourcemap-codec@^1.5.0": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "@jridgewell/trace-mapping@^0.3.28": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "@mswjs/interceptors@^0.39.1": "https://registry.npmjs.org/@mswjs/interceptors/-/interceptors-0.39.2.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@nodelib/fs.walk@^1.2.8": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@open-draft/deferred-promise@^2.2.0": "https://registry.npmjs.org/@open-draft/deferred-promise/-/deferred-promise-2.2.0.tgz", "@open-draft/logger@^0.3.0": "https://registry.npmjs.org/@open-draft/logger/-/logger-0.3.0.tgz", "@open-draft/until@^2.0.0": "https://registry.npmjs.org/@open-draft/until/-/until-2.1.0.tgz", "@open-draft/until@^2.1.0": "https://registry.npmjs.org/@open-draft/until/-/until-2.1.0.tgz", "@pkgjs/parseargs@^0.11.0": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "@remirror/core-constants@3.0.0": "https://registry.npmjs.org/@remirror/core-constants/-/core-constants-3.0.0.tgz", "@remix-run/router@1.23.0": "https://registry.npmjs.org/@remix-run/router/-/router-1.23.0.tgz", "@rolldown/pluginutils@1.0.0-beta.19": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz", "@tanstack/query-core@5.83.0": "https://registry.npmjs.org/@tanstack/query-core/-/query-core-5.83.0.tgz", "@tanstack/react-query@^5.81.2": "https://registry.npmjs.org/@tanstack/react-query/-/react-query-5.83.0.tgz", "@tiptap/core@^3.0.1": "https://registry.npmjs.org/@tiptap/core/-/core-3.0.1.tgz", "@tiptap/extension-blockquote@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-blockquote/-/extension-blockquote-3.0.1.tgz", "@tiptap/extension-bold@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-bold/-/extension-bold-3.0.1.tgz", "@tiptap/extension-bubble-menu@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-bubble-menu/-/extension-bubble-menu-3.0.1.tgz", "@tiptap/extension-bullet-list@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-bullet-list/-/extension-bullet-list-3.0.1.tgz", "@tiptap/extension-code-block@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-code-block/-/extension-code-block-3.0.1.tgz", "@tiptap/extension-code@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-code/-/extension-code-3.0.1.tgz", "@tiptap/extension-document@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-document/-/extension-document-3.0.1.tgz", "@tiptap/extension-dropcursor@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-dropcursor/-/extension-dropcursor-3.0.1.tgz", "@tiptap/extension-floating-menu@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-floating-menu/-/extension-floating-menu-3.0.1.tgz", "@tiptap/extension-gapcursor@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-gapcursor/-/extension-gapcursor-3.0.1.tgz", "@tiptap/extension-hard-break@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-hard-break/-/extension-hard-break-3.0.1.tgz", "@tiptap/extension-heading@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-heading/-/extension-heading-3.0.1.tgz", "@tiptap/extension-horizontal-rule@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-horizontal-rule/-/extension-horizontal-rule-3.0.1.tgz", "@tiptap/extension-image@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-image/-/extension-image-3.0.1.tgz", "@tiptap/extension-italic@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-italic/-/extension-italic-3.0.1.tgz", "@tiptap/extension-link@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-link/-/extension-link-3.0.1.tgz", "@tiptap/extension-list-item@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-list-item/-/extension-list-item-3.0.1.tgz", "@tiptap/extension-list-keymap@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-list-keymap/-/extension-list-keymap-3.0.1.tgz", "@tiptap/extension-list@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-list/-/extension-list-3.0.1.tgz", "@tiptap/extension-ordered-list@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-ordered-list/-/extension-ordered-list-3.0.1.tgz", "@tiptap/extension-paragraph@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-paragraph/-/extension-paragraph-3.0.1.tgz", "@tiptap/extension-strike@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-strike/-/extension-strike-3.0.1.tgz", "@tiptap/extension-table-cell@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-table-cell/-/extension-table-cell-3.0.1.tgz", "@tiptap/extension-table-header@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-table-header/-/extension-table-header-3.0.1.tgz", "@tiptap/extension-table-row@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-table-row/-/extension-table-row-3.0.1.tgz", "@tiptap/extension-table@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-table/-/extension-table-3.0.1.tgz", "@tiptap/extension-text-align@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-text-align/-/extension-text-align-3.0.1.tgz", "@tiptap/extension-text@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-text/-/extension-text-3.0.1.tgz", "@tiptap/extension-underline@^3.0.1": "https://registry.npmjs.org/@tiptap/extension-underline/-/extension-underline-3.0.1.tgz", "@tiptap/extensions@^3.0.1": "https://registry.npmjs.org/@tiptap/extensions/-/extensions-3.0.1.tgz", "@tiptap/pm@^3.0.1": "https://registry.npmjs.org/@tiptap/pm/-/pm-3.0.1.tgz", "@tiptap/react@^3.0.1": "https://registry.npmjs.org/@tiptap/react/-/react-3.0.1.tgz", "@tiptap/starter-kit@^3.0.1": "https://registry.npmjs.org/@tiptap/starter-kit/-/starter-kit-3.0.1.tgz", "@types/babel__core@^7.20.5": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "@types/babel__generator@*": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz", "@types/babel__template@*": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "@types/babel__traverse@*": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "@types/cookie@^0.6.0": "https://registry.npmjs.org/@types/cookie/-/cookie-0.6.0.tgz", "@types/json-schema@^7.0.12": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/linkify-it@^5": "https://registry.npmjs.org/@types/linkify-it/-/linkify-it-5.0.0.tgz", "@types/markdown-it@^14.0.0": "https://registry.npmjs.org/@types/markdown-it/-/markdown-it-14.1.2.tgz", "@types/mdurl@^2": "https://registry.npmjs.org/@types/mdurl/-/mdurl-2.0.0.tgz", "@types/prop-types@*": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.15.tgz", "@types/react-dom@^18.2.15": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.7.tgz", "@types/react@^18.2.37": "https://registry.npmjs.org/@types/react/-/react-18.3.23.tgz", "@types/semver@^7.5.0": "https://registry.npmjs.org/@types/semver/-/semver-7.7.0.tgz", "@types/statuses@^2.0.4": "https://registry.npmjs.org/@types/statuses/-/statuses-2.0.6.tgz", "@types/tough-cookie@^4.0.5": "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.5.tgz", "@types/use-sync-external-store@^0.0.6": "https://registry.npmjs.org/@types/use-sync-external-store/-/use-sync-external-store-0.0.6.tgz", "@typescript-eslint/eslint-plugin@^6.10.0": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-6.21.0.tgz", "@typescript-eslint/parser@^6.10.0": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-6.21.0.tgz", "@typescript-eslint/scope-manager@6.21.0": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz", "@typescript-eslint/type-utils@6.21.0": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-6.21.0.tgz", "@typescript-eslint/types@6.21.0": "https://registry.npmjs.org/@typescript-eslint/types/-/types-6.21.0.tgz", "@typescript-eslint/typescript-estree@6.21.0": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz", "@typescript-eslint/utils@6.21.0": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-6.21.0.tgz", "@typescript-eslint/visitor-keys@6.21.0": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz", "@ungap/structured-clone@^1.2.0": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz", "@vitejs/plugin-react@^4.1.1": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.6.0.tgz", "acorn-jsx@^5.3.2": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn@^8.9.0": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "ajv@^6.12.4": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ansi-escapes@^4.3.2": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-regex@^5.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^6.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz", "ansi-styles@^4.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^6.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz", "any-promise@^1.0.0": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz", "anymatch@~3.1.2": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "arg@^5.0.2": "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz", "argparse@^2.0.1": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "array-union@^2.1.0": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "asynckit@^0.4.0": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "autoprefixer@^10.4.16": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz", "axios@^1.6.2": "https://registry.npmjs.org/axios/-/axios-1.10.0.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "big-integer@^1.6.16": "https://registry.npmjs.org/big-integer/-/big-integer-1.6.52.tgz", "binary-extensions@^2.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "brace-expansion@^2.0.1": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz", "braces@^3.0.3": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "braces@~3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "broadcast-channel@^3.4.1": "https://registry.npmjs.org/broadcast-channel/-/broadcast-channel-3.7.0.tgz", "browserslist@^4.24.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "browserslist@^4.24.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "call-bind-apply-helpers@^1.0.1": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.2": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "callsites@^3.0.0": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "camelcase-css@^2.0.1": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz", "caniuse-lite@^1.0.30001702": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "caniuse-lite@^1.0.30001726": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "chalk@^4.0.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chokidar@^3.6.0": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "cli-width@^4.1.0": "https://registry.npmjs.org/cli-width/-/cli-width-4.1.0.tgz", "cliui@^8.0.1": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "color-convert@^2.0.1": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "color-name@~1.1.4": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "combined-stream@^1.0.8": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "commander@^4.0.0": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "convert-source-map@^2.0.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "cookie@^0.7.2": "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz", "crelt@^1.0.0": "https://registry.npmjs.org/crelt/-/crelt-1.0.6.tgz", "cross-spawn@^7.0.2": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.6": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cssesc@^3.0.0": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz", "csstype@^3.0.2": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "csstype@^3.1.3": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.2": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.4": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "deep-is@^0.1.3": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz", "delayed-stream@~1.0.0": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "detect-node@^2.0.4": "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz", "detect-node@^2.1.0": "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz", "didyoumean@^1.2.2": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz", "dir-glob@^3.0.1": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "dlv@^1.1.3": "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz", "doctrine@^3.0.0": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz", "dunder-proto@^1.0.1": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "eastasianwidth@^0.2.0": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "electron-to-chromium@^1.5.173": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.182.tgz", "emoji-regex@^8.0.0": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "emoji-regex@^9.2.2": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz", "entities@^4.4.0": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz", "es-define-property@^1.0.1": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "es-errors@^1.3.0": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "es-object-atoms@^1.0.0": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-object-atoms@^1.1.1": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-set-tostringtag@^2.1.0": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "esbuild@^0.18.10": "https://registry.npmjs.org/esbuild/-/esbuild-0.18.20.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escalade@^3.2.0": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "eslint-plugin-react-hooks@^4.6.0": "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.2.tgz", "eslint-plugin-react-refresh@^0.4.4": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz", "eslint-scope@^7.2.2": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz", "eslint-visitor-keys@^3.4.1": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-visitor-keys@^3.4.3": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint@^8.53.0": "https://registry.npmjs.org/eslint/-/eslint-8.57.1.tgz", "espree@^9.6.0": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz", "espree@^9.6.1": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz", "esquery@^1.4.2": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz", "esrecurse@^4.3.0": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.2.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "esutils@^2.0.2": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-glob@^3.2.9": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fast-glob@^3.3.2": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fastq@^1.6.0": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz", "file-entry-cache@^6.0.1": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "fill-range@^7.1.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "find-up@^5.0.0": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "flat-cache@^3.0.4": "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz", "flatted@^3.2.9": "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz", "follow-redirects@^1.15.6": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "foreground-child@^3.1.0": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz", "form-data@^4.0.0": "https://registry.npmjs.org/form-data/-/form-data-4.0.3.tgz", "fraction.js@^4.3.7": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz", "fs.realpath@^1.0.0": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@~2.3.2": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.2": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.5": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.2.6": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-proto@^1.0.1": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "glob-parent@^5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^6.0.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz", "glob-parent@~5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob@^10.3.10": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz", "glob@^7.1.3": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "globals@^13.19.0": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz", "globby@^11.1.0": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "goober@^2.1.16": "https://registry.npmjs.org/goober/-/goober-2.1.16.tgz", "gopd@^1.2.0": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "graphemer@^1.4.0": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz", "graphql@^16.8.1": "https://registry.npmjs.org/graphql/-/graphql-16.11.0.tgz", "has-flag@^4.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "has-symbols@^1.0.3": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "has-symbols@^1.1.0": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "has-tostringtag@^1.0.2": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "hasown@^2.0.2": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "headers-polyfill@^4.0.2": "https://registry.npmjs.org/headers-polyfill/-/headers-polyfill-4.0.3.tgz", "ignore@^5.2.0": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "ignore@^5.2.4": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "import-fresh@^3.2.1": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz", "imurmurhash@^0.1.4": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "inflight@^1.0.4": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "is-binary-path@~2.1.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-core-module@^2.16.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "is-extglob@^2.1.1": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-glob@^4.0.0": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.3": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-node-process@^1.2.0": "https://registry.npmjs.org/is-node-process/-/is-node-process-1.2.0.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-path-inside@^3.0.3": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz", "isexe@^2.0.0": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "jackspeak@^3.1.2": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz", "jiti@^1.21.6": "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz", "js-sha3@0.8.0": "https://registry.npmjs.org/js-sha3/-/js-sha3-0.8.0.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@^4.1.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "jsesc@^3.0.2": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "json-buffer@3.0.1": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "json5@^2.2.3": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "keyv@^4.5.3": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz", "levn@^0.4.1": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "lilconfig@^3.0.0": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz", "lilconfig@^3.1.3": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz", "lines-and-columns@^1.1.6": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "linkify-it@^5.0.0": "https://registry.npmjs.org/linkify-it/-/linkify-it-5.0.0.tgz", "linkifyjs@^4.2.0": "https://registry.npmjs.org/linkifyjs/-/linkifyjs-4.3.1.tgz", "locate-path@^6.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "lodash.merge@^4.6.2": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "loose-envify@^1.1.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "lru-cache@^10.2.0": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "lru-cache@^5.1.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "lucide-react@^0.294.0": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.294.0.tgz", "markdown-it@^14.0.0": "https://registry.npmjs.org/markdown-it/-/markdown-it-14.1.0.tgz", "match-sorter@^6.0.2": "https://registry.npmjs.org/match-sorter/-/match-sorter-6.4.0.tgz", "math-intrinsics@^1.1.0": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "mdurl@^2.0.0": "https://registry.npmjs.org/mdurl/-/mdurl-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "merge2@^1.4.1": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "micromatch@^4.0.8": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "microseconds@0.2.0": "https://registry.npmjs.org/microseconds/-/microseconds-0.2.0.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-types@^2.1.12": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "minimatch@9.0.3": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.3.tgz", "minimatch@^3.0.5": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.1": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.2": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^9.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "minipass@^5.0.0 || ^6.0.2 || ^7.0.0": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minipass@^7.1.2": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "ms@^2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "msw@^2.0.0": "https://registry.npmjs.org/msw/-/msw-2.10.4.tgz", "mute-stream@^2.0.0": "https://registry.npmjs.org/mute-stream/-/mute-stream-2.0.0.tgz", "mz@^2.7.0": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz", "nano-time@1.0.0": "https://registry.npmjs.org/nano-time/-/nano-time-1.0.0.tgz", "nanoid@^3.3.11": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "natural-compare@^1.4.0": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "node-releases@^2.0.19": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-range@^0.1.2": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "object-assign@^4.0.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-hash@^3.0.0": "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz", "oblivious-set@1.0.0": "https://registry.npmjs.org/oblivious-set/-/oblivious-set-1.0.0.tgz", "once@^1.3.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "optionator@^0.9.3": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz", "orderedmap@^2.0.0": "https://registry.npmjs.org/orderedmap/-/orderedmap-2.1.1.tgz", "outvariant@^1.4.0": "https://registry.npmjs.org/outvariant/-/outvariant-1.4.3.tgz", "outvariant@^1.4.3": "https://registry.npmjs.org/outvariant/-/outvariant-1.4.3.tgz", "p-limit@^3.0.2": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "package-json-from-dist@^1.0.0": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "parent-module@^1.0.0": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "path-exists@^4.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-key@^3.1.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-scurry@^1.11.1": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "path-to-regexp@^6.3.0": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.3.0.tgz", "path-type@^4.0.0": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "picocolors@^1.1.1": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "pify@^2.3.0": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "pirates@^4.0.1": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz", "postcss-import@^15.1.0": "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz", "postcss-js@^4.0.1": "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz", "postcss-load-config@^4.0.2": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz", "postcss-nested@^6.2.0": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz", "postcss-selector-parser@^6.1.1": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.1.2": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-value-parser@^4.0.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.2.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss@^8.4.27": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "postcss@^8.4.31": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "postcss@^8.4.47": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "prelude-ls@^1.2.1": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz", "prosemirror-changeset@^2.3.0": "https://registry.npmjs.org/prosemirror-changeset/-/prosemirror-changeset-2.3.1.tgz", "prosemirror-collab@^1.3.1": "https://registry.npmjs.org/prosemirror-collab/-/prosemirror-collab-1.3.1.tgz", "prosemirror-commands@^1.0.0": "https://registry.npmjs.org/prosemirror-commands/-/prosemirror-commands-1.7.1.tgz", "prosemirror-commands@^1.6.2": "https://registry.npmjs.org/prosemirror-commands/-/prosemirror-commands-1.7.1.tgz", "prosemirror-dropcursor@^1.8.1": "https://registry.npmjs.org/prosemirror-dropcursor/-/prosemirror-dropcursor-1.8.2.tgz", "prosemirror-gapcursor@^1.3.2": "https://registry.npmjs.org/prosemirror-gapcursor/-/prosemirror-gapcursor-1.3.2.tgz", "prosemirror-history@^1.0.0": "https://registry.npmjs.org/prosemirror-history/-/prosemirror-history-1.4.1.tgz", "prosemirror-history@^1.4.1": "https://registry.npmjs.org/prosemirror-history/-/prosemirror-history-1.4.1.tgz", "prosemirror-inputrules@^1.4.0": "https://registry.npmjs.org/prosemirror-inputrules/-/prosemirror-inputrules-1.5.0.tgz", "prosemirror-keymap@^1.0.0": "https://registry.npmjs.org/prosemirror-keymap/-/prosemirror-keymap-1.2.3.tgz", "prosemirror-keymap@^1.2.2": "https://registry.npmjs.org/prosemirror-keymap/-/prosemirror-keymap-1.2.3.tgz", "prosemirror-markdown@^1.13.1": "https://registry.npmjs.org/prosemirror-markdown/-/prosemirror-markdown-1.13.2.tgz", "prosemirror-menu@^1.2.4": "https://registry.npmjs.org/prosemirror-menu/-/prosemirror-menu-1.2.5.tgz", "prosemirror-model@^1.0.0": "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.25.2.tgz", "prosemirror-model@^1.20.0": "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.25.2.tgz", "prosemirror-model@^1.21.0": "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.25.2.tgz", "prosemirror-model@^1.24.1": "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.25.2.tgz", "prosemirror-model@^1.25.0": "https://registry.npmjs.org/prosemirror-model/-/prosemirror-model-1.25.2.tgz", "prosemirror-schema-basic@^1.2.3": "https://registry.npmjs.org/prosemirror-schema-basic/-/prosemirror-schema-basic-1.2.4.tgz", "prosemirror-schema-list@^1.5.0": "https://registry.npmjs.org/prosemirror-schema-list/-/prosemirror-schema-list-1.5.1.tgz", "prosemirror-state@^1.0.0": "https://registry.npmjs.org/prosemirror-state/-/prosemirror-state-1.4.3.tgz", "prosemirror-state@^1.2.2": "https://registry.npmjs.org/prosemirror-state/-/prosemirror-state-1.4.3.tgz", "prosemirror-state@^1.4.3": "https://registry.npmjs.org/prosemirror-state/-/prosemirror-state-1.4.3.tgz", "prosemirror-tables@^1.6.4": "https://registry.npmjs.org/prosemirror-tables/-/prosemirror-tables-1.7.1.tgz", "prosemirror-trailing-node@^3.0.0": "https://registry.npmjs.org/prosemirror-trailing-node/-/prosemirror-trailing-node-3.0.0.tgz", "prosemirror-transform@^1.0.0": "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.4.tgz", "prosemirror-transform@^1.1.0": "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.4.tgz", "prosemirror-transform@^1.10.2": "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.4.tgz", "prosemirror-transform@^1.10.3": "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.4.tgz", "prosemirror-transform@^1.7.3": "https://registry.npmjs.org/prosemirror-transform/-/prosemirror-transform-1.10.4.tgz", "prosemirror-view@^1.0.0": "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.40.0.tgz", "prosemirror-view@^1.1.0": "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.40.0.tgz", "prosemirror-view@^1.27.0": "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.40.0.tgz", "prosemirror-view@^1.31.0": "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.40.0.tgz", "prosemirror-view@^1.38.1": "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.40.0.tgz", "prosemirror-view@^1.39.1": "https://registry.npmjs.org/prosemirror-view/-/prosemirror-view-1.40.0.tgz", "proxy-from-env@^1.1.0": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "psl@^1.1.33": "https://registry.npmjs.org/psl/-/psl-1.15.0.tgz", "punycode.js@^2.3.1": "https://registry.npmjs.org/punycode.js/-/punycode.js-2.3.1.tgz", "punycode@^2.1.0": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "punycode@^2.1.1": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "punycode@^2.3.1": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "querystringify@^2.1.1": "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz", "queue-microtask@^1.2.2": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "react-dom@^18.2.0": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz", "react-hook-form@^7.48.2": "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.60.0.tgz", "react-hot-toast@^2.5.2": "https://registry.npmjs.org/react-hot-toast/-/react-hot-toast-2.5.2.tgz", "react-query@^3.39.3": "https://registry.npmjs.org/react-query/-/react-query-3.39.3.tgz", "react-refresh@^0.17.0": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.17.0.tgz", "react-router-dom@^6.20.1": "https://registry.npmjs.org/react-router-dom/-/react-router-dom-6.30.1.tgz", "react-router@6.30.1": "https://registry.npmjs.org/react-router/-/react-router-6.30.1.tgz", "react@^18.2.0": "https://registry.npmjs.org/react/-/react-18.3.1.tgz", "read-cache@^1.0.0": "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz", "readdirp@~3.6.0": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "remove-accents@0.5.0": "https://registry.npmjs.org/remove-accents/-/remove-accents-0.5.0.tgz", "require-directory@^2.1.1": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "requires-port@^1.0.0": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "resolve-from@^4.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "resolve@^1.1.7": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.22.8": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "reusify@^1.0.4": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz", "rimraf@3.0.2": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "rimraf@^3.0.2": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "rollup@^3.27.1": "https://registry.npmjs.org/rollup/-/rollup-3.29.5.tgz", "rope-sequence@^1.3.0": "https://registry.npmjs.org/rope-sequence/-/rope-sequence-1.3.4.tgz", "run-parallel@^1.1.9": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "scheduler@^0.23.2": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz", "semver@^6.3.1": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^7.5.4": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "shebang-command@^2.0.0": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "signal-exit@^4.0.1": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz", "signal-exit@^4.1.0": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz", "slash@^3.0.0": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "source-map-js@^1.2.1": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "statuses@^2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.2.tgz", "strict-event-emitter@^0.5.1": "https://registry.npmjs.org/strict-event-emitter/-/strict-event-emitter-0.5.1.tgz", "string-width-cjs@npm:string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.1.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^5.0.1": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "string-width@^5.1.2": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "strip-ansi-cjs@npm:strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^7.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz", "strip-json-comments@^3.1.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "sucrase@^3.35.0": "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz", "supports-color@^7.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "tailwindcss@^3.3.5": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz", "text-table@^0.2.0": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "thenify-all@^1.0.0": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz", "thenify@>= 3.1.0 < 4": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "tough-cookie@^4.1.4": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.4.tgz", "ts-api-utils@^1.0.1": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.3.tgz", "ts-interface-checker@^0.1.9": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", "type-check@^0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-check@~0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-fest@^0.20.2": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "type-fest@^0.21.3": "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz", "type-fest@^4.26.1": "https://registry.npmjs.org/type-fest/-/type-fest-4.41.0.tgz", "typescript@^5.2.2": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz", "uc.micro@^2.0.0": "https://registry.npmjs.org/uc.micro/-/uc.micro-2.1.0.tgz", "uc.micro@^2.1.0": "https://registry.npmjs.org/uc.micro/-/uc.micro-2.1.0.tgz", "universalify@^0.2.0": "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz", "unload@2.2.0": "https://registry.npmjs.org/unload/-/unload-2.2.0.tgz", "update-browserslist-db@^1.1.3": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "uri-js@^4.2.2": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "url-parse@^1.5.3": "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz", "use-sync-external-store@^1.4.0": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "util-deprecate@^1.0.2": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "uuid@^11.1.0": "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz", "vite@^4.5.0": "https://registry.npmjs.org/vite/-/vite-4.5.14.tgz", "w3c-keyname@^2.2.0": "https://registry.npmjs.org/w3c-keyname/-/w3c-keyname-2.2.8.tgz", "which@^2.0.1": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "word-wrap@^1.2.5": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^6.2.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^8.1.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "y18n@^5.0.5": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "yallist@^3.0.2": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "yaml@^2.3.4": "https://registry.npmjs.org/yaml/-/yaml-2.8.0.tgz", "yargs-parser@^21.1.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs@^17.7.2": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "yocto-queue@^0.1.0": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "yoctocolors-cjs@^2.1.2": "https://registry.npmjs.org/yoctocolors-cjs/-/yoctocolors-cjs-2.1.2.tgz"}, "files": [], "artifacts": {}}
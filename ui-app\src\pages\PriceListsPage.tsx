import { useState, useMemo } from 'react'
import {
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Settings,
  ChevronDown,
  ChevronUp,
  X,
  DollarSign,
  Package,
  TrendingUp,
  Users,
  Calendar
} from 'lucide-react'
import {
  mockChannels,
  mockCustomerGroups,
  mockSuppliers,
  mockClassifications,
  mockTags,
  mockPriceListItems
} from '../data/mockData'
import { PriceListFilters } from '../types/priceList'
import CustomSelect from '../components/CustomSelect'
import ColumnSelector from '../components/ColumnSelector'
import CSVModal from '../components/CSVModal'
import StatCard from '../components/StatCard'
import SectionHeader from '../components/SectionHeader'

const PriceListsPage = () => {
  const [selectedChannels, setSelectedChannels] = useState<string[]>(['ch1'])
  const [searchTerm, setSearchTerm] = useState('')
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false)
  const [filters, setFilters] = useState<PriceListFilters>({})
  const [selectedColumns, setSelectedColumns] = useState<string[]>([
    'product_name', 'classification', 'current_stock', 'cost', 'wholesale',
    'value_tier', 'tier_1', 'csp', 'jan', 'weeks_on_hand', 'turn_rate', 'purchaser'
  ])
  const [isColumnSelectorOpen, setIsColumnSelectorOpen] = useState(false)
  const [csvModalOpen, setCsvModalOpen] = useState(false)
  const [csvModalMode, setCsvModalMode] = useState<'import' | 'export'>('export')

  // Channel selection handlers
  const handleChannelChange = (channelId: string) => {
    if (channelId === 'both') {
      setSelectedChannels(['ch1', 'ch2'])
    } else {
      setSelectedChannels([channelId])
    }
  }

  // Filter data based on search and filters
  const filteredData = useMemo(() => {
    let data = [...mockPriceListItems]

    // Apply search filter
    if (searchTerm) {
      data = data.filter(item =>
        item.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.sku.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply other filters
    if (filters.purchaser?.length) {
      data = data.filter(item => filters.purchaser!.includes(item.purchaser))
    }

    if (filters.primary_supplier?.length) {
      data = data.filter(item => filters.primary_supplier!.includes(item.supplier.name))
    }

    if (filters.classification?.length) {
      data = data.filter(item =>
        item.classification && filters.classification!.includes(item.classification.name)
      )
    }

    return data
  }, [searchTerm, filters])

  // CSV handlers
  const handleExport = (options: any) => {
    console.log('Exporting CSV with options:', options)
    // Implement CSV export logic here
  }

  const handleImport = (file: File) => {
    console.log('Importing CSV file:', file.name)
    // Implement CSV import logic here
  }

  const handleRefresh = () => {
    console.log('Refreshing data...')
    // Implement data refresh logic here
  }

  // Generate table columns based on channel selection
  const generateColumns = () => {
    const baseColumns = [
      { key: 'product_name', label: 'Product Name', sticky: true },
      { key: 'classification', label: 'Classification' },
      { key: 'current_stock', label: 'Current Stock' },
      { key: 'cost', label: 'Cost' }
    ]

    // Customer group pricing columns
    const customerGroups = [
      'wholesale', 'value_tier', 'tier_1', 'tier_2', 'tier_pro',
      'distributor', 'mvd_warehouse', 'tcd_warehouse', 'vip'
    ]
    const pricingColumns = []

    if (selectedChannels.length === 1) {
      // Single channel view
      for (const groupId of customerGroups) {
        const groupName = mockCustomerGroups.find(g => g.id === groupId)?.name || groupId
        pricingColumns.push({
          key: groupId,
          label: groupName
        })
      }
    } else {
      // Both channels view - duplicate each customer group for both channels
      for (const groupId of customerGroups) {
        const groupName = mockCustomerGroups.find(g => g.id === groupId)?.name || groupId
        pricingColumns.push({
          key: `${groupId}_ch1`,
          label: `${groupName} (Channel 1)`
        })
        pricingColumns.push({
          key: `${groupId}_ch2`,
          label: `${groupName} (Channel 2)`
        })
      }
    }

    const salesColumns = []
    if (selectedChannels.length === 1) {
      salesColumns.push(
        { key: 'csp', label: 'CSP' },
        { key: 'jan', label: 'Jan' },
        { key: 'feb', label: 'Feb' },
        { key: 'mar', label: 'Mar' },
        { key: 'apr', label: 'Apr' },
        { key: 'may', label: 'May' },
        { key: 'jun', label: 'Jun' }
      )
    } else {
      // Both channels - duplicate CSP and monthly columns for each channel
      salesColumns.push(
        { key: 'csp_ch1', label: 'CSP (Channel 1)' },
        { key: 'csp_ch2', label: 'CSP (Channel 2)' },
        { key: 'jan_ch1', label: 'Jan (Channel 1)' },
        { key: 'jan_ch2', label: 'Jan (Channel 2)' },
        { key: 'feb_ch1', label: 'Feb (Channel 1)' },
        { key: 'feb_ch2', label: 'Feb (Channel 2)' },
        { key: 'mar_ch1', label: 'Mar (Channel 1)' },
        { key: 'mar_ch2', label: 'Mar (Channel 2)' },
        { key: 'apr_ch1', label: 'Apr (Channel 1)' },
        { key: 'apr_ch2', label: 'Apr (Channel 2)' },
        { key: 'may_ch1', label: 'May (Channel 1)' },
        { key: 'may_ch2', label: 'May (Channel 2)' },
        { key: 'jun_ch1', label: 'Jun (Channel 1)' },
        { key: 'jun_ch2', label: 'Jun (Channel 2)' }
      )
    }

    const metaColumns = [
      { key: 'weeks_on_hand', label: 'Weeks On Hand' },
      { key: 'turn_rate', label: 'Turn Rate' },
      { key: 'purchaser', label: 'Purchaser' },
      { key: 'supplier', label: 'Supplier' },
      { key: 'last_updated_by', label: 'Last Updated By' },
      { key: 'date_created', label: 'Date Created' },
      { key: 'last_updated_date', label: 'Last Updated Date' }
    ]

    return [...baseColumns, ...pricingColumns, ...salesColumns, ...metaColumns]
  }

  const columns = generateColumns()

  // Auto-adjust selected columns based on channel selection
  const adjustedSelectedColumns = useMemo(() => {
    const baseSelectedColumns = [
      'product_name', 'classification', 'current_stock', 'cost',
      'wholesale', 'value_tier', 'tier_1', 'csp', 'jan', 'feb',
      'weeks_on_hand', 'turn_rate', 'purchaser'
    ]

    if (selectedChannels.length === 2) {
      // Both channels - replace single channel columns with dual channel equivalents
      return baseSelectedColumns.flatMap(col => {
        if (col === 'csp') return ['csp_ch1', 'csp_ch2']
        if (['jan', 'feb', 'mar', 'apr', 'may', 'jun'].includes(col)) {
          return [`${col}_ch1`, `${col}_ch2`]
        }
        if (['wholesale', 'value_tier', 'tier_1', 'tier_2', 'tier_pro', 'distributor', 'mvd_warehouse', 'tcd_warehouse', 'vip'].includes(col)) {
          return [`${col}_ch1`, `${col}_ch2`]
        }
        return [col]
      })
    }
    return baseSelectedColumns
  }, [selectedChannels])

  const visibleColumns = columns.filter(col => adjustedSelectedColumns.includes(col.key))

  // Get cell value for a specific column
  const getCellValue = (item: any, columnKey: string) => {
    switch (columnKey) {
      case 'product_name':
        return item.product_name
      case 'classification':
        return item.classification?.name || '-'
      case 'current_stock':
        return item.current_stock
      case 'cost':
        return `$${item.cost.toFixed(2)}`
      case 'weeks_on_hand':
        return item.weeks_on_hand
      case 'turn_rate':
        return item.turn_rate
      case 'purchaser':
        return item.purchaser
      case 'supplier':
        return item.supplier.name
      case 'last_updated_by':
        return item.last_updated_by
      case 'date_created':
        return new Date(item.date_created).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      case 'last_updated_date':
        return new Date(item.last_updated_date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      default:
        // Handle pricing columns
        if (columnKey.includes('_ch')) {
          const [groupId, channelId] = columnKey.split('_')
          return `$${item.pricing[groupId]?.[channelId]?.toFixed(2) || '0.00'}`
        }

        // Handle sales columns
        if (columnKey.startsWith('csp')) {
          if (columnKey.includes('_')) {
            // Dual channel: csp_ch1, csp_ch2
            const channelId = columnKey.split('_')[1]
            return item.sales_data[channelId]?.csp_count || 0
          } else {
            // Single channel: csp
            const channelId = selectedChannels[0]
            return item.sales_data[channelId]?.csp_count || 0
          }
        }

        if (['jan', 'feb', 'mar', 'apr', 'may', 'jun'].some(month => columnKey.startsWith(month))) {
          const parts = columnKey.split('_')
          const month = parts[0] as keyof typeof item.sales_data.ch1.monthly_sales

          if (parts.length > 1) {
            // Dual channel: jan_ch1, jan_ch2, etc.
            const channelId = parts[1]
            return item.sales_data[channelId]?.monthly_sales[month] || 0
          } else {
            // Single channel: jan, feb, etc.
            const channelId = selectedChannels[0]
            return item.sales_data[channelId]?.monthly_sales[month] || 0
          }
        }

        // Handle customer group pricing for single channel
        if (item.pricing[columnKey]) {
          const channelId = selectedChannels[0]
          return `$${item.pricing[columnKey][channelId]?.toFixed(2) || '0.00'}`
        }

        return '-'
    }
  }

  return (
    <div className="space-y-6 max-w-full overflow-hidden">
      <SectionHeader
        icon={<DollarSign className="h-8 w-8" />}
        title="Consolidated Price List"
        subtitle="View, manage, and export your consolidated product price lists"
        actions={
          <>
            <button className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center">
              Save Changes
            </button>
            <button className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors flex items-center">
              + Create Price List
            </button>
          </>
        }
      />

      {/* Filter Panel */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <button
            onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
            className="flex items-center justify-between w-full text-left"
          >
            <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
            {isFilterPanelOpen ? (
              <ChevronUp className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronDown className="h-5 w-5 text-gray-500" />
            )}
          </button>
        </div>

        {isFilterPanelOpen && (
          <div className="p-6">
            {/* First Row - 4 columns */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Purchaser</label>
                <CustomSelect
                  options={[...new Set(mockPriceListItems.map(item => item.purchaser))].map(name => ({ value: name, label: name }))}
                  value={filters.purchaser || []}
                  onChange={(values) => setFilters(prev => ({ ...prev, purchaser: values }))}
                  multiple
                  placeholder="Select User"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Primary Supplier</label>
                <CustomSelect
                  options={mockSuppliers.map(supplier => ({ value: supplier.name, label: supplier.name }))}
                  value={filters.primary_supplier || []}
                  onChange={(values) => setFilters(prev => ({ ...prev, primary_supplier: values }))}
                  multiple
                  placeholder="Select Primary Supplier"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Classifications</label>
                <CustomSelect
                  options={mockClassifications.map(cls => ({ value: cls.name, label: cls.name }))}
                  value={filters.classification || []}
                  onChange={(values) => setFilters(prev => ({ ...prev, classification: values }))}
                  multiple
                  placeholder="Select Classification"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                <CustomSelect
                  options={mockTags.map(tag => ({ value: tag.name, label: tag.name }))}
                  value={filters.tags || []}
                  onChange={(values) => setFilters(prev => ({ ...prev, tags: values }))}
                  multiple
                  placeholder="SELECT TAGS"
                />
              </div>
            </div>

            {/* Second Row - 4 columns */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Classified as</label>
                <CustomSelect
                  options={mockClassifications.map(cls => ({ value: cls.name, label: cls.name }))}
                  value={filters.classified_as || []}
                  onChange={(values) => setFilters(prev => ({ ...prev, classified_as: values }))}
                  multiple
                  placeholder="Select Classified As"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Products</label>
                <CustomSelect
                  options={mockPriceListItems.map(item => ({ value: item.product_name, label: item.product_name }))}
                  value={filters.products || []}
                  onChange={(values) => setFilters(prev => ({ ...prev, products: values }))}
                  multiple
                  placeholder="Select Products"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Top Products</label>
                <CustomSelect
                  options={[
                    { value: 'top_10', label: 'Top 10' },
                    { value: 'top_50', label: 'Top 50' },
                    { value: 'top_100', label: 'Top 100' },
                    { value: 'top_200', label: 'Top 200' },
                    { value: 'top_500', label: 'Top 500' }
                  ]}
                  value={filters.top_products || ''}
                  onChange={(value) => setFilters(prev => ({ ...prev, top_products: value }))}
                  placeholder="Select Top Products"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Cost Margin%</label>
                <CustomSelect
                  options={[
                    { value: '+20%', label: '+20%' },
                    { value: '+15%', label: '+15%' },
                    { value: '+10%', label: '+10%' },
                    { value: '+5%', label: '+5%' },
                    { value: '0%', label: '0%' },
                    { value: '-5%', label: '-5%' },
                    { value: '-10%', label: '-10%' },
                    { value: '-15%', label: '-15%' },
                    { value: '-20%', label: '-20%' }
                  ]}
                  value={filters.cost_margin || ''}
                  onChange={(value) => setFilters(prev => ({ ...prev, cost_margin: value }))}
                  placeholder="Select Cost Margin"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Toolbar */}
      <div className="bg-white rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by product name or SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-80 text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Channel Selection Dropdown */}
            <div className="relative">
              <select
                value={selectedChannels.length === 2 ? 'both' : selectedChannels[0]}
                onChange={(e) => handleChannelChange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="ch1">Channel 1</option>
                <option value="ch2">Channel 2</option>
                <option value="both">Both Channels</option>
              </select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                setCsvModalMode('export')
                setCsvModalOpen(true)
              }}
              className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              Download CSV
            </button>

            <button
              onClick={() => {
                setCsvModalMode('import')
                setCsvModalOpen(true)
              }}
              className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Upload className="h-4 w-4 mr-2" />
              Import CSV
            </button>

            <button
              onClick={() => setIsColumnSelectorOpen(true)}
              className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Settings className="h-4 w-4 mr-2" />
              Select Columns
            </button>

            <button
              onClick={handleRefresh}
              className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Price List Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto max-w-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {visibleColumns.map((column) => (
                  <th
                    key={column.key}
                    className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                      column.sticky ? 'sticky left-0 bg-gray-50 z-10' : ''
                    }`}
                  >
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredData.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  {visibleColumns.map((column) => (
                    <td
                      key={column.key}
                      className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${
                        column.sticky ? 'sticky left-0 bg-white z-10' : ''
                      }`}
                    >
                      {getCellValue(item, column.key)}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredData.length === 0 && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No price list items found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search or filter criteria.
            </p>
          </div>
        )}
      </div>

      {/* Column Selector Modal */}
      <ColumnSelector
        columns={columns}
        selectedColumns={selectedColumns}
        onSelectionChange={setSelectedColumns}
        isOpen={isColumnSelectorOpen}
        onClose={() => setIsColumnSelectorOpen(false)}
      />

      {/* CSV Import/Export Modal */}
      <CSVModal
        isOpen={csvModalOpen}
        onClose={() => setCsvModalOpen(false)}
        mode={csvModalMode}
        onExport={handleExport}
        onImport={handleImport}
      />
    </div>
  )
}

export default PriceListsPage

import React, { useRef, useState } from 'react';
import {
  Package, DollarSign, BarChart3, Truck, Search, Image, Settings, Palette
} from 'lucide-react';

const TABS = [
  { id: 'basic', label: 'Basic Information', icon: Package },
  { id: 'pricing', label: 'Pricing', icon: DollarSign },
  { id: 'inventory', label: 'Inventory', icon: BarChart3 },
  { id: 'shipping', label: 'Shipping', icon: Truck },
  { id: 'seo', label: 'Search Engine Optimization', icon: Search },
  { id: 'images', label: 'Images', icon: Image },
  { id: 'variants', label: 'Variants', icon: Palette },
  { id: 'advanced', label: 'Advanced', icon: Settings },
];

const CreateProductPageModern = () => {
  const [activeTab, setActiveTab] = useState('basic');
  const sectionRefs = useRef<Record<string, HTMLElement | null>>({});

  const scrollToSection = (sectionId: string) => {
    setActiveTab(sectionId);
    const element = sectionRefs.current[sectionId];
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Track active section on scroll
  React.useEffect(() => {
    const handleScroll = () => {
      const sections = Object.keys(sectionRefs.current);
      const scrollPosition = window.scrollY + 200;
      for (const sectionId of sections) {
        const element = sectionRefs.current[sectionId];
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveTab(sectionId);
            break;
          }
        }
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sticky Sidebar */}
      <aside className="w-80 sticky top-16 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sections</h3>
          <nav className="space-y-2">
            {TABS.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              return (
                <button
                  key={tab.id}
                  onClick={() => scrollToSection(tab.id)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-500'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-3 flex-shrink-0 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                  <span className="font-medium">{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 flex flex-col items-center py-10">
        <div className="w-full max-w-3xl space-y-10">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-3">
              <Package className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Add New Product</h1>
              </div>
            </div>
            <button className="px-6 py-2 rounded-full bg-blue-600 text-white font-semibold shadow hover:bg-blue-700 transition">Create Product</button>
          </div>

          {/* Section Placeholders */}
          {TABS.map(tab => (
            <section
              key={tab.id}
              ref={el => (sectionRefs.current[tab.id] = el)}
              id={tab.id}
              className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100"
            >
              <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <tab.icon className="h-5 w-5 text-blue-600 mr-2" />
                {tab.label}
              </h2>
              <div className="h-24 bg-gray-50 rounded-lg flex items-center justify-center text-gray-400">[Section: {tab.label}]</div>
            </section>
          ))}
        </div>
      </main>
    </div>
  );
};

export default CreateProductPageModern; 
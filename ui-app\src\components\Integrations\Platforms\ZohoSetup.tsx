import React, { useState } from 'react';
import { 
  Globe, 
  ExternalLink, 
  Copy, 
  CheckCircle, 
  AlertCircle, 
  Info,
  Eye,
  EyeOff,
  Download
} from 'lucide-react';

interface ZohoSetupProps {
  onComplete: (config: any) => void;
  onBack: () => void;
}

const ZohoSetup: React.FC<ZohoSetupProps> = ({ onComplete, onBack }) => {
  const [step, setStep] = useState(1);
  const [config, setConfig] = useState({
    apiKey: '',
    baseUrl: 'https://www.zohoapis.com/crm/v3',
    timeout: 30,
    dataCenter: 'US'
  });
  const [showApiKey, setShowApiKey] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string | number) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleTestConnection = async () => {
    setLoading(true);
    try {
      // Mock API call to test connection
      await new Promise(resolve => setTimeout(resolve, 2000));
      setStep(3);
    } catch (error) {
      console.error('Connection test failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleComplete = () => {
    onComplete(config);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const dataCenters = [
    { value: 'US', label: 'United States (US)', url: 'https://www.zohoapis.com/crm/v3' },
    { value: 'EU', label: 'Europe (EU)', url: 'https://www.zohoapis.eu/crm/v3' },
    { value: 'IN', label: 'India (IN)', url: 'https://www.zohoapis.in/crm/v3' },
    { value: 'AU', label: 'Australia (AU)', url: 'https://www.zohoapis.com.au/crm/v3' },
    { value: 'JP', label: 'Japan (JP)', url: 'https://www.zohoapis.jp/crm/v3' }
  ];

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4">
          <Globe className="w-8 h-8 text-orange-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Zoho CRM Setup</h2>
        <p className="text-gray-600 mt-2">Configure your Zoho CRM API integration</p>
      </div>

      <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
        <div className="flex items-start">
          <Info className="w-5 h-5 text-orange-600 mt-0.5 mr-3" />
          <div className="text-sm text-orange-800">
            <p className="font-medium mb-2">Before you begin, you need to generate a Self-Client API Key in Zoho CRM:</p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Log in to your Zoho CRM account</li>
              <li>Go to Setup → Developer Space → APIs</li>
              <li>Click on "Generate Self-Client"</li>
              <li>Fill in the required information</li>
              <li>Select the required scopes (ZohoCRM.modules.ALL, ZohoCRM.settings.ALL)</li>
              <li>Generate the API Key</li>
              <li>Copy the generated API Key</li>
            </ol>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-2" />
          <div className="text-sm text-blue-800">
            <p className="font-medium">Required API Scopes:</p>
            <ul className="mt-1 list-disc list-inside space-y-1">
              <li><code>ZohoCRM.modules.ALL</code> - Access to all CRM modules</li>
              <li><code>ZohoCRM.settings.ALL</code> - Access to CRM settings</li>
              <li><code>ZohoCRM.users.READ</code> - Read user information</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <button
          onClick={() => setStep(2)}
          className="inline-flex items-center px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
        >
          I've Generated the API Key
          <ExternalLink className="w-4 h-4 ml-2" />
        </button>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Enter API Configuration</h2>
        <p className="text-gray-600 mt-2">Add your Zoho CRM API credentials and settings</p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            API Key *
          </label>
          <div className="relative">
            <input
              type={showApiKey ? 'text' : 'password'}
              value={config.apiKey}
              onChange={(e) => handleInputChange('apiKey', e.target.value)}
              placeholder="••••••••••••••••••••••••••••••••"
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              required
            />
            <button
              type="button"
              onClick={() => setShowApiKey(!showApiKey)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showApiKey ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            This is the Self-Client API Key generated from Zoho CRM Developer Space
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Data Center
          </label>
          <select
            value={config.dataCenter}
            onChange={(e) => {
              const selected = dataCenters.find(dc => dc.value === e.target.value);
              handleInputChange('dataCenter', e.target.value);
              if (selected) {
                handleInputChange('baseUrl', selected.url);
              }
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            {dataCenters.map((dc) => (
              <option key={dc.value} value={dc.value}>
                {dc.label}
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-500 mt-1">
            Select the data center where your Zoho CRM instance is hosted
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Base URL
          </label>
          <input
            type="text"
            value={config.baseUrl}
            onChange={(e) => handleInputChange('baseUrl', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            readOnly
          />
          <p className="text-xs text-gray-500 mt-1">
            This URL is automatically set based on your data center selection
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Timeout (seconds)
          </label>
          <input
            type="number"
            value={config.timeout}
            onChange={(e) => handleInputChange('timeout', parseInt(e.target.value))}
            min="10"
            max="120"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
          <p className="text-xs text-gray-500 mt-1">
            Maximum time to wait for API responses (10-120 seconds)
          </p>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 mr-2" />
          <div className="text-sm text-yellow-800">
            <p className="font-medium">Important Notes:</p>
            <ul className="mt-1 list-disc list-inside space-y-1">
              <li>API Key should have the required scopes mentioned above</li>
              <li>Ensure your Zoho CRM account has API access enabled</li>
              <li>Rate limits apply: 100,000 API calls per day</li>
              <li>Keep your API Key secure and don't share it</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setStep(1)}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Back
        </button>
        <button
          onClick={handleTestConnection}
          disabled={!config.apiKey || loading}
          className="inline-flex items-center px-6 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Testing...
            </>
          ) : (
            'Test Connection'
          )}
        </button>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Connection Successful!</h2>
        <p className="text-gray-600 mt-2">Your Zoho CRM connection has been configured successfully</p>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="font-medium text-green-800 mb-3">Connection Details:</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-green-700">Data Center:</span>
            <span className="text-green-900">{config.dataCenter}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-green-700">Base URL:</span>
            <span className="text-green-900">{config.baseUrl}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-green-700">Timeout:</span>
            <span className="text-green-900">{config.timeout} seconds</span>
          </div>
          <div className="flex justify-between">
            <span className="text-green-700">Status:</span>
            <span className="text-green-900">Connected</span>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-2" />
          <div className="text-sm text-blue-800">
            <p className="font-medium">Available Zoho CRM Modules:</p>
            <ul className="mt-1 list-disc list-inside space-y-1">
              <li>Leads, Contacts, Accounts, Deals</li>
              <li>Tasks, Events, Calls, Emails</li>
              <li>Products, Price Books, Quotes</li>
              <li>Invoices, Sales Orders</li>
              <li>Custom Modules</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="w-5 h-5 text-orange-600 mt-0.5 mr-2" />
          <div className="text-sm text-orange-800">
            <p className="font-medium">Next Steps:</p>
            <ul className="mt-1 list-disc list-inside space-y-1">
              <li>Create data mappings for Zoho CRM modules</li>
              <li>Set up webhooks for real-time updates</li>
              <li>Configure field mappings and transformations</li>
              <li>Test data synchronization</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <button
          onClick={handleComplete}
          className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          Complete Setup
          <CheckCircle className="w-4 h-4 ml-2" />
        </button>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto p-6">
      {step === 1 && renderStep1()}
      {step === 2 && renderStep2()}
      {step === 3 && renderStep3()}
    </div>
  );
};

export default ZohoSetup; 
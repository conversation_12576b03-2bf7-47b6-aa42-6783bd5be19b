import React, { useState } from 'react'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Download, 
  Upload, 
  Settings, 
  Eye,
  Copy,
  Check,
  X,
  ChevronDown,
  MoreVertical,
  Save,
  Package,
  Users,
  Database,
  Bell,
  Shield,
  Clock,
  Layers
} from 'lucide-react'
import MultiSelectDropdown from '../components/MultiSelectDropdown'
import CustomSelect from '../components/CustomSelect'
import ColorPicker from '../components/ColorPicker'
import SectionHeader from '../components/SectionHeader'

interface ComponentExample {
  name: string
  description: string
  usage: string
  example: React.ReactNode
  code: string
  category: string
}

const ComponentsPage: React.FC = () => {
  const [selectedColors, setSelectedColors] = useState<string[]>([])
  const [selectedOptions, setSelectedOptions] = useState<string[]>([])
  const [singleSelectValue, setSingleSelectValue] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('all')

  // Sample data for components
  const colorOptions = [
    { value: 'red', label: 'Red' },
    { value: 'blue', label: 'Blue' },
    { value: 'green', label: 'Green' },
    { value: 'yellow', label: 'Yellow' },
    { value: 'purple', label: 'Purple' }
  ]

  const selectOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4' }
  ]

  const multiSelectOptions = [
    { value: 'react', label: 'React' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'tailwind', label: 'Tailwind CSS' },
    { value: 'node', label: 'Node.js' },
    { value: 'python', label: 'Python' }
  ]

  const components: ComponentExample[] = [
    // Buttons
    {
      name: 'Primary Button',
      description: 'Main action button with blue background',
      usage: 'btn btn-primary',
      category: 'buttons',
      example: (
        <button className="btn btn-primary flex items-center">
          <Plus className="h-4 w-4 mr-2" />
          Create New
        </button>
      ),
      code: `<button className="btn btn-primary flex items-center">
  <Plus className="h-4 w-4 mr-2" />
  Create New
</button>`
    },
    {
      name: 'Secondary Button',
      description: 'Secondary action button with gray background',
      usage: 'btn btn-secondary',
      category: 'buttons',
      example: (
        <button className="btn btn-secondary flex items-center">
          <Edit className="h-4 w-4 mr-2" />
          Edit
        </button>
      ),
      code: `<button className="btn btn-secondary flex items-center">
  <Edit className="h-4 w-4 mr-2" />
  Edit
</button>`
    },
    {
      name: 'Danger Button',
      description: 'Destructive action button with red background',
      usage: 'btn bg-red-600 text-white hover:bg-red-700',
      category: 'buttons',
      example: (
        <button className="btn bg-red-600 text-white hover:bg-red-700 flex items-center">
          <Trash2 className="h-4 w-4 mr-2" />
          Delete
        </button>
      ),
      code: `<button className="btn bg-red-600 text-white hover:bg-red-700 flex items-center">
  <Trash2 className="h-4 w-4 mr-2" />
  Delete
</button>`
    },
    {
      name: 'Icon Button',
      description: 'Button with only an icon',
      usage: 'p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md',
      category: 'buttons',
      example: (
        <div className="flex space-x-2">
          <button className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-md transition-colors">
            <Eye className="h-4 w-4" />
          </button>
          <button className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
            <Edit className="h-4 w-4" />
          </button>
          <button className="p-1.5 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md transition-colors">
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      ),
      code: `<button className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-md transition-colors">
  <Eye className="h-4 w-4" />
</button>`
    },

    // Form Elements
    {
      name: 'Text Input',
      description: 'Standard text input field',
      usage: 'input',
      category: 'forms',
      example: (
        <input
          type="text"
          placeholder="Enter text..."
          className="input"
        />
      ),
      code: `<input
  type="text"
  placeholder="Enter text..."
  className="input"
/>`
    },
    {
      name: 'Search Input',
      description: 'Search input with icon',
      usage: 'search-input pl-10',
      category: 'forms',
      example: (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search..."
            className="search-input pl-10 w-80"
          />
        </div>
      ),
      code: `<div className="relative">
  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
  <input
    type="text"
    placeholder="Search..."
    className="search-input pl-10 w-80"
  />
</div>`
    },
    {
      name: 'Textarea',
      description: 'Multi-line text input',
      usage: 'input (with rows attribute)',
      category: 'forms',
      example: (
        <textarea
          placeholder="Enter description..."
          rows={3}
          className="input"
        />
      ),
      code: `<textarea
  placeholder="Enter description..."
  rows={3}
  className="input"
/>`
    },
    {
      name: 'Checkbox',
      description: 'Standard checkbox input',
      usage: 'form-checkbox',
      category: 'forms',
      example: (
        <label className="flex items-center space-x-2">
          <input type="checkbox" className="form-checkbox h-4 w-4 text-blue-600" />
          <span>Checkbox label</span>
        </label>
      ),
      code: `<label className=\"flex items-center space-x-2\">
  <input type=\"checkbox\" className=\"form-checkbox h-4 w-4 text-blue-600\" />
  <span>Checkbox label</span>
</label>`
    },
    {
      name: 'Radio Button',
      description: 'Standard radio input',
      usage: 'form-radio',
      category: 'forms',
      example: (
        <label className="flex items-center space-x-2">
          <input type="radio" className="form-radio h-4 w-4 text-blue-600" name="radio-example" />
          <span>Radio label</span>
        </label>
      ),
      code: `<label className=\"flex items-center space-x-2\">
  <input type=\"radio\" className=\"form-radio h-4 w-4 text-blue-600\" name=\"radio-example\" />
  <span>Radio label</span>
</label>`
    },
    {
      name: 'Switch',
      description: 'Toggle switch input',
      usage: 'relative inline-flex h-6 w-11 items-center rounded-full',
      category: 'forms',
      example: (
        <button
          type="button"
          className="relative inline-flex h-6 w-11 items-center rounded-full bg-blue-600 focus:outline-none"
          aria-pressed="true"
        >
          <span className="sr-only">Enable</span>
          <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
        </button>
      ),
      code: `<button
  type=\"button\"
  className=\"relative inline-flex h-6 w-11 items-center rounded-full bg-blue-600 focus:outline-none\"
  aria-pressed=\"true\"
>
  <span className=\"sr-only\">Enable</span>
  <span className=\"inline-block h-4 w-4 transform rounded-full bg-white translate-x-6\" />
</button>`
    },
    {
      name: 'File Upload',
      description: 'File input for uploading documents or images',
      usage: 'file-input',
      category: 'forms',
      example: (
        <label className="block">
          <span className="block text-sm font-medium text-gray-700 mb-1">Upload file</span>
          <input type="file" className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" />
        </label>
      ),
      code: `<label className=\"block\">
  <span className=\"block text-sm font-medium text-gray-700 mb-1\">Upload file</span>
  <input type=\"file\" className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\" />
</label>`
    },

    // Dropdowns
    {
      name: 'Single Select Dropdown',
      description: 'Dropdown for single selection',
      usage: 'CustomSelect component',
      category: 'dropdowns',
      example: (
        <CustomSelect
          options={selectOptions}
          value={singleSelectValue}
          onChange={setSingleSelectValue}
          placeholder="Select an option..."
          className="w-64"
        />
      ),
      code: `<CustomSelect
  options={selectOptions}
  value={singleSelectValue}
  onChange={setSingleSelectValue}
  placeholder="Select an option..."
  className="w-64"
/>`
    },
    {
      name: 'Multi Select Dropdown',
      description: 'Dropdown for multiple selections',
      usage: 'MultiSelectDropdown component',
      category: 'dropdowns',
      example: (
        <MultiSelectDropdown
          options={multiSelectOptions}
          value={selectedOptions}
          onChange={setSelectedOptions}
          placeholder="Select technologies..."
          className="w-64"
        />
      ),
      code: `<MultiSelectDropdown
  options={multiSelectOptions}
  value={selectedOptions}
  onChange={setSelectedOptions}
  placeholder="Select technologies..."
  className="w-64"
/>`
    },

    // Cards
    {
      name: 'Standard Card',
      description: 'Basic card container with padding and shadow',
      usage: 'card',
      category: 'layout',
      example: (
        <div className="card w-80">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Card Title</h3>
          <p className="text-gray-600">This is a standard card with content.</p>
        </div>
      ),
      code: `<div className="card">
  <h3 className="text-lg font-medium text-gray-900 mb-2">Card Title</h3>
  <p className="text-gray-600">This is a standard card with content.</p>
</div>`
    },
    {
      name: 'Interactive Card',
      description: 'Card with hover effects',
      usage: 'card hover:shadow-md transition-all duration-200',
      category: 'layout',
      example: (
        <div className="card w-80 hover:shadow-md transition-all duration-200 cursor-pointer">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Interactive Card</h3>
              <p className="text-gray-600">Hover to see effects</p>
            </div>
            <ChevronDown className="h-5 w-5 text-gray-400" />
          </div>
        </div>
      ),
      code: `<div className="card hover:shadow-md transition-all duration-200 cursor-pointer">
  <div className="flex items-center justify-between">
    <div>
      <h3 className="text-lg font-medium text-gray-900">Interactive Card</h3>
      <p className="text-gray-600">Hover to see effects</p>
    </div>
    <ChevronDown className="h-5 w-5 text-gray-400" />
  </div>
</div>`
    },

    // Tables
    {
      name: 'Data Table',
      description: 'Standard data table with actions',
      usage: 'table with action buttons',
      category: 'tables',
      example: (
        <div className="overflow-hidden border border-gray-200 rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  Sample Item
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Active
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center space-x-2 justify-end">
                    <button className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-md">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-1.5 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      ),
      code: `<div className="overflow-hidden border border-gray-200 rounded-lg">
  <table className="min-w-full divide-y divide-gray-200">
    <thead className="bg-gray-50">
      <tr>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Name
        </th>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
          Status
        </th>
        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
          Actions
        </th>
      </tr>
    </thead>
    <tbody className="bg-white divide-y divide-gray-200">
      <tr>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          Sample Item
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
            Active
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center space-x-2 justify-end">
            <button className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-md">
              <Eye className="h-4 w-4" />
            </button>
            <button className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md">
              <Edit className="h-4 w-4" />
            </button>
            <button className="p-1.5 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md">
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>`
    },

    // Status Badges
    {
      name: 'Status Badge',
      description: 'Status indicator with colored background',
      usage: 'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
      category: 'badges',
      example: (
        <div className="flex space-x-2">
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
            Active
          </span>
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
            Pending
          </span>
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
            Error
          </span>
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
            Inactive
          </span>
        </div>
      ),
      code: `<span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
  Active
</span>`
    },

    // Modals
    {
      name: 'Modal Overlay',
      description: 'Modal background overlay',
      usage: 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50',
      category: 'modals',
      example: (
        <div className="relative max-w-md w-full mx-auto border border-gray-200 rounded-lg shadow-xl bg-white">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Modal Title</h3>
              <button className="text-gray-400 hover:text-gray-600" tabIndex={-1}>
                <X className="h-5 w-5" />
              </button>
            </div>
            <p className="text-gray-600 mb-4">This is a modal content example.</p>
            <div className="flex justify-end space-x-3">
              <button className="btn btn-secondary">Cancel</button>
              <button className="btn btn-primary">Confirm</button>
            </div>
          </div>
        </div>
      ),
      code: `<div className=\"relative max-w-md w-full mx-auto border border-gray-200 rounded-lg shadow-xl bg-white\">
  <div className=\"p-6\">
    <div className=\"flex items-center justify-between mb-4\">
      <h3 className=\"text-lg font-medium text-gray-900\">Modal Title</h3>
      <button className=\"text-gray-400 hover:text-gray-600\">
        <X className=\"h-5 w-5\" />
      </button>
    </div>
    <p className=\"text-gray-600 mb-4\">This is a modal content example.</p>
    <div className=\"flex justify-end space-x-3\">
      <button className=\"btn btn-secondary\">Cancel</button>
      <button className=\"btn btn-primary\">Confirm</button>
    </div>
  </div>
</div>`
    },

    // Navigation
    {
      name: 'Tab Navigation',
      description: 'Horizontal tab navigation',
      usage: 'flex space-x-8 border-b border-gray-200',
      category: 'navigation',
      example: (
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            <button className="py-2 px-1 border-b-2 border-blue-500 text-sm font-medium text-blue-600">
              Active Tab
            </button>
            <button className="py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
              Inactive Tab
            </button>
            <button className="py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
              Another Tab
            </button>
          </nav>
        </div>
      ),
      code: `<div className="border-b border-gray-200">
  <nav className="flex space-x-8">
    <button className="py-2 px-1 border-b-2 border-blue-500 text-sm font-medium text-blue-600">
      Active Tab
    </button>
    <button className="py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
      Inactive Tab
    </button>
  </nav>
</div>`
    },

    // Loading States
    {
      name: 'Loading Spinner',
      description: 'Animated loading spinner',
      usage: 'animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600',
      category: 'loading',
      example: (
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-sm text-gray-600">Loading...</span>
        </div>
      ),
      code: `<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>`
    },

    // Alerts
    {
      name: 'Success Alert',
      description: 'Success message alert',
      usage: 'bg-green-50 border border-green-200 rounded-md p-4',
      category: 'alerts',
      example: (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <Check className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Success</h3>
              <div className="mt-2 text-sm text-green-700">
                Operation completed successfully.
              </div>
            </div>
          </div>
        </div>
      ),
      code: `<div className="bg-green-50 border border-green-200 rounded-md p-4">
  <div className="flex">
    <Check className="h-5 w-5 text-green-400" />
    <div className="ml-3">
      <h3 className="text-sm font-medium text-green-800">Success</h3>
      <div className="mt-2 text-sm text-green-700">
        Operation completed successfully.
      </div>
    </div>
  </div>
</div>`
    },
    {
      name: 'Error Alert',
      description: 'Error message alert',
      usage: 'bg-red-50 border border-red-200 rounded-md p-4',
      category: 'alerts',
      example: (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <X className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                Something went wrong. Please try again.
              </div>
            </div>
          </div>
        </div>
      ),
      code: `<div className="bg-red-50 border border-red-200 rounded-md p-4">
  <div className="flex">
    <X className="h-5 w-5 text-red-400" />
    <div className="ml-3">
      <h3 className="text-sm font-medium text-red-800">Error</h3>
      <div className="mt-2 text-sm text-red-700">
        Something went wrong. Please try again.
      </div>
    </div>
  </div>
</div>`
    }
  ]

  const categories = [
    { id: 'all', name: 'All Components', icon: Package },
    { id: 'buttons', name: 'Buttons', icon: Package },
    { id: 'forms', name: 'Form Elements', icon: Package },
    { id: 'dropdowns', name: 'Dropdowns', icon: ChevronDown },
    { id: 'layout', name: 'Layout', icon: Package },
    { id: 'tables', name: 'Tables', icon: Package },
    { id: 'badges', name: 'Badges', icon: Package },
    { id: 'modals', name: 'Modals', icon: Package },
    { id: 'navigation', name: 'Navigation', icon: Package },
    { id: 'loading', name: 'Loading States', icon: Clock },
    { id: 'alerts', name: 'Alerts', icon: Bell }
  ]

  const filteredComponents = components.filter(component => 
    activeTab === 'all' || component.category === activeTab
  )

  const filteredBySearch = filteredComponents.filter(component =>
    component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    component.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <SectionHeader
        icon={<Package className="h-7 w-7" />}
        title="Component Library"
        subtitle="Complete collection of UI components used throughout the application"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filter */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search components..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input pl-10 w-full"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveTab(category.id)}
                  className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === category.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Components Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {filteredBySearch.map((component, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{component.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{component.description}</p>
                  </div>
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {component.category}
                  </span>
                </div>

                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-2">Usage:</div>
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded text-gray-800">
                    {component.usage}
                  </code>
                </div>

                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-2">Example:</div>
                  <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    {component.example}
                  </div>
                </div>

                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                    View Code
                  </summary>
                  <pre className="mt-2 text-xs bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                    <code>{component.code}</code>
                  </pre>
                </details>
              </div>
            </div>
          ))}
        </div>

        {filteredBySearch.length === 0 && (
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No components found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ComponentsPage 
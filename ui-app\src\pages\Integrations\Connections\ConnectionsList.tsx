import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreVertical, 
  Play, 
  Pause, 
  Settings, 
  Trash2,
  TestTube,
  Eye,
  Edit,
  Database,
  Globe,
  Zap,
  Mail,
  CreditCard,
  Building
} from 'lucide-react';
import SectionHeader from '../../../components/SectionHeader';

interface Connection {
  id: string;
  name: string;
  platform: string;
  status: 'active' | 'inactive' | 'error' | 'testing';
  lastSyncAt: string | null;
  createdAt: string;
  updatedAt: string;
  authType: string;
  description?: string;
}

const ConnectionsList: React.FC = () => {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [platformFilter, setPlatformFilter] = useState<string>('all');

  useEffect(() => {
    // Mock data loading
    setTimeout(() => {
      setConnections([
        {
          id: '1',
          name: 'Salesforce Production',
          platform: 'Salesforce',
          status: 'active',
          lastSyncAt: '2024-01-15T14:30:00Z',
          createdAt: '2024-01-01T10:00:00Z',
          updatedAt: '2024-01-15T14:30:00Z',
          authType: 'OAuth2',
          description: 'Main Salesforce instance for customer data'
        },
        {
          id: '2',
          name: 'Zoho CRM',
          platform: 'Zoho',
          status: 'active',
          lastSyncAt: '2024-01-15T13:45:00Z',
          createdAt: '2024-01-05T09:15:00Z',
          updatedAt: '2024-01-15T13:45:00Z',
          authType: 'API Key',
          description: 'Zoho CRM for lead and contact management'
        },
        {
          id: '3',
          name: 'HubSpot Marketing',
          platform: 'HubSpot',
          status: 'inactive',
          lastSyncAt: null,
          createdAt: '2024-01-10T11:30:00Z',
          updatedAt: '2024-01-12T16:20:00Z',
          authType: 'API Key',
          description: 'HubSpot for marketing automation'
        },
        {
          id: '4',
          name: 'Stripe Payments',
          platform: 'Stripe',
          status: 'active',
          lastSyncAt: '2024-01-15T12:15:00Z',
          createdAt: '2024-01-08T14:00:00Z',
          updatedAt: '2024-01-15T12:15:00Z',
          authType: 'Webhook',
          description: 'Stripe for payment processing'
        },
        {
          id: '5',
          name: 'Mailchimp Campaigns',
          platform: 'Mailchimp',
          status: 'error',
          lastSyncAt: '2024-01-14T18:30:00Z',
          createdAt: '2024-01-12T10:45:00Z',
          updatedAt: '2024-01-14T18:30:00Z',
          authType: 'API Key',
          description: 'Mailchimp for email marketing'
        },
        {
          id: '6',
          name: 'Generic REST API',
          platform: 'Generic',
          status: 'testing',
          lastSyncAt: null,
          createdAt: '2024-01-15T09:00:00Z',
          updatedAt: '2024-01-15T09:00:00Z',
          authType: 'Basic Auth',
          description: 'Custom REST API integration'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'salesforce':
        return <Building className="w-5 h-5 text-blue-600" />;
      case 'zoho':
        return <Globe className="w-5 h-5 text-orange-600" />;
      case 'hubspot':
        return <Zap className="w-5 h-5 text-orange-500" />;
      case 'stripe':
        return <CreditCard className="w-5 h-5 text-purple-600" />;
      case 'mailchimp':
        return <Mail className="w-5 h-5 text-yellow-600" />;
      default:
        return <Database className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    
    switch (status) {
      case 'active':
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800`}>
            <div className="w-2 h-2 bg-green-400 rounded-full mr-1.5"></div>
            Active
          </span>
        );
      case 'inactive':
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800`}>
            <div className="w-2 h-2 bg-gray-400 rounded-full mr-1.5"></div>
            Inactive
          </span>
        );
      case 'error':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            <div className="w-2 h-2 bg-red-400 rounded-full mr-1.5"></div>
            Error
          </span>
        );
      case 'testing':
        return (
          <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
            <div className="w-2 h-2 bg-yellow-400 rounded-full mr-1.5"></div>
            Testing
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800`}>
            Unknown
          </span>
        );
    }
  };

  const filteredConnections = connections.filter(connection => {
    const matchesSearch = connection.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         connection.platform.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || connection.status === statusFilter;
    const matchesPlatform = platformFilter === 'all' || connection.platform === platformFilter;
    
    return matchesSearch && matchesStatus && matchesPlatform;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<Database className="h-7 w-7" />}
        title="Connections"
        subtitle="Manage your data source connections"
        actions={
          <Link
            to="/integrations/connections/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Connection
          </Link>
        }
      />

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search connections..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="error">Error</option>
            <option value="testing">Testing</option>
          </select>

          {/* Platform Filter */}
          <select
            value={platformFilter}
            onChange={(e) => setPlatformFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Platforms</option>
            <option value="Salesforce">Salesforce</option>
            <option value="Zoho">Zoho</option>
            <option value="HubSpot">HubSpot</option>
            <option value="Stripe">Stripe</option>
            <option value="Mailchimp">Mailchimp</option>
            <option value="Generic">Generic</option>
          </select>
        </div>
      </div>

      {/* Connections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredConnections.map((connection) => (
          <div key={connection.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {getPlatformIcon(connection.platform)}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{connection.name}</h3>
                    <p className="text-sm text-gray-500">{connection.platform}</p>
                  </div>
                </div>
                <div className="relative">
                  <button className="p-1 hover:bg-gray-100 rounded">
                    <MoreVertical className="w-4 h-4 text-gray-400" />
                  </button>
                </div>
              </div>

              {/* Description */}
              {connection.description && (
                <p className="text-sm text-gray-600 mb-4">{connection.description}</p>
              )}

              {/* Status */}
              <div className="flex items-center justify-between mb-4">
                {getStatusBadge(connection.status)}
                <span className="text-xs text-gray-500">
                  {connection.authType}
                </span>
              </div>

              {/* Last Sync */}
              <div className="mb-4">
                <p className="text-xs text-gray-500 mb-1">Last Sync</p>
                <p className="text-sm text-gray-900">
                  {connection.lastSyncAt ? formatDate(connection.lastSyncAt) : 'Never'}
                </p>
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <Link
                  to={`/integrations/connections/${connection.id}`}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  View
                </Link>
                <Link
                  to={`/integrations/connections/${connection.id}/test`}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <TestTube className="w-4 h-4 mr-1" />
                  Test
                </Link>
                <Link
                  to={`/integrations/connections/${connection.id}/edit`}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredConnections.length === 0 && (
        <div className="text-center py-12">
          <Database className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No connections found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || statusFilter !== 'all' || platformFilter !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'Get started by creating your first connection.'}
          </p>
          {!searchTerm && statusFilter === 'all' && platformFilter === 'all' && (
            <div className="mt-6">
              <Link
                to="/integrations/connections/create"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Connection
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ConnectionsList; 
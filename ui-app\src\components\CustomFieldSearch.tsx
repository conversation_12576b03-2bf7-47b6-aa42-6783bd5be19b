import { useState, useEffect } from 'react'
import { Search, Filter, X, ChevronDown, Calendar, Hash, Type, ToggleLeft } from 'lucide-react'

interface SearchFilters {
  search: string
  resourceType: string
  fieldType: string
  namespace: string
  isRequired: boolean | null
  isSearchable: boolean | null
  isFilterable: boolean | null
  isAiEnabled: boolean | null
  isActive: boolean | null
  dateRange: {
    from: string
    to: string
  }
}

interface CustomFieldSearchProps {
  onFiltersChange: (filters: SearchFilters) => void
  resourceTypes: string[]
  fieldTypes: string[]
  namespaces: string[]
  totalResults: number
  className?: string
}

const CustomFieldSearch: React.FC<CustomFieldSearchProps> = ({
  onFiltersChange,
  resourceTypes,
  fieldTypes,
  namespaces,
  totalResults,
  className = ''
}) => {
  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    resourceType: '',
    fieldType: '',
    namespace: '',
    isRequired: null,
    isSearchable: null,
    isFilterable: null,
    isAiEnabled: null,
    isActive: null,
    dateRange: {
      from: '',
      to: ''
    }
  })

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [activeFilterCount, setActiveFilterCount] = useState(0)

  useEffect(() => {
    onFiltersChange(filters)
  }, [filters, onFiltersChange])

  useEffect(() => {
    // Count active filters
    let count = 0
    if (filters.search) count++
    if (filters.resourceType) count++
    if (filters.fieldType) count++
    if (filters.namespace) count++
    if (filters.isRequired !== null) count++
    if (filters.isSearchable !== null) count++
    if (filters.isFilterable !== null) count++
    if (filters.isAiEnabled !== null) count++
    if (filters.isActive !== null) count++
    if (filters.dateRange.from || filters.dateRange.to) count++
    
    setActiveFilterCount(count)
  }, [filters])

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const clearAllFilters = () => {
    setFilters({
      search: '',
      resourceType: '',
      fieldType: '',
      namespace: '',
      isRequired: null,
      isSearchable: null,
      isFilterable: null,
      isAiEnabled: null,
      isActive: null,
      dateRange: {
        from: '',
        to: ''
      }
    })
  }

  const BooleanFilter: React.FC<{
    label: string
    value: boolean | null
    onChange: (value: boolean | null) => void
    icon?: React.ReactNode
  }> = ({ label, value, onChange, icon }) => (
    <div className="space-y-2">
      <label className="flex items-center text-sm font-medium text-gray-700">
        {icon && <span className="mr-2">{icon}</span>}
        {label}
      </label>
      <div className="flex space-x-2">
        <button
          onClick={() => onChange(null)}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            value === null
              ? 'bg-blue-100 text-blue-800 border-blue-300'
              : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
          }`}
        >
          All
        </button>
        <button
          onClick={() => onChange(true)}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            value === true
              ? 'bg-green-100 text-green-800 border-green-300'
              : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
          }`}
        >
          Yes
        </button>
        <button
          onClick={() => onChange(false)}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            value === false
              ? 'bg-red-100 text-red-800 border-red-300'
              : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
          }`}
        >
          No
        </button>
      </div>
    </div>
  )

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Search Bar */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search custom fields by name, key, or description..."
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          {filters.search && (
            <button
              onClick={() => updateFilter('search', '')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className={`flex items-center space-x-2 px-4 py-2 border rounded-lg transition-colors ${
            showAdvancedFilters || activeFilterCount > 1
              ? 'border-blue-300 bg-blue-50 text-blue-700'
              : 'border-gray-300 hover:bg-gray-50'
          }`}
        >
          <Filter className="h-4 w-4" />
          <span className="text-sm font-medium">
            Filters
            {activeFilterCount > 1 && (
              <span className="ml-1 px-1.5 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
                {activeFilterCount}
              </span>
            )}
          </span>
          <ChevronDown className={`h-4 w-4 transition-transform ${showAdvancedFilters ? 'rotate-180' : ''}`} />
        </button>

        {activeFilterCount > 0 && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            Clear all
          </button>
        )}
      </div>

      {/* Results Count */}
      <div className="text-sm text-gray-500">
        {totalResults} field{totalResults !== 1 ? 's' : ''} found
        {activeFilterCount > 0 && ` with ${activeFilterCount} filter${activeFilterCount !== 1 ? 's' : ''} applied`}
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Resource Type Filter */}
            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <Type className="h-4 w-4 mr-2" />
                Resource Type
              </label>
              <select
                value={filters.resourceType}
                onChange={(e) => updateFilter('resourceType', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Resources</option>
                {resourceTypes.map(type => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            {/* Field Type Filter */}
            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <Hash className="h-4 w-4 mr-2" />
                Field Type
              </label>
              <select
                value={filters.fieldType}
                onChange={(e) => updateFilter('fieldType', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Types</option>
                {fieldTypes.map(type => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}
                  </option>
                ))}
              </select>
            </div>

            {/* Namespace Filter */}
            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <ToggleLeft className="h-4 w-4 mr-2" />
                Namespace
              </label>
              <select
                value={filters.namespace}
                onChange={(e) => updateFilter('namespace', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Namespaces</option>
                {namespaces.map(namespace => (
                  <option key={namespace} value={namespace}>
                    {namespace.charAt(0).toUpperCase() + namespace.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Boolean Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <BooleanFilter
              label="Required"
              value={filters.isRequired}
              onChange={(value) => updateFilter('isRequired', value)}
            />
            <BooleanFilter
              label="Searchable"
              value={filters.isSearchable}
              onChange={(value) => updateFilter('isSearchable', value)}
            />
            <BooleanFilter
              label="Filterable"
              value={filters.isFilterable}
              onChange={(value) => updateFilter('isFilterable', value)}
            />
            <BooleanFilter
              label="AI Enabled"
              value={filters.isAiEnabled}
              onChange={(value) => updateFilter('isAiEnabled', value)}
            />
          </div>

          {/* Date Range Filter */}
          <div className="space-y-2">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <Calendar className="h-4 w-4 mr-2" />
              Created Date Range
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-1">From</label>
                <input
                  type="date"
                  value={filters.dateRange.from}
                  onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, from: e.target.value })}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">To</label>
                <input
                  type="date"
                  value={filters.dateRange.to}
                  onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, to: e.target.value })}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Status Filter */}
          <BooleanFilter
            label="Active Status"
            value={filters.isActive}
            onChange={(value) => updateFilter('isActive', value)}
          />
        </div>
      )}
    </div>
  )
}

export default CustomFieldSearch

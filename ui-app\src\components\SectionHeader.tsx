import React from 'react';

interface SectionHeaderProps {
  icon?: React.ReactNode;
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
  className?: string;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({ icon, title, subtitle, actions, className = '' }) => (
  <div className={`flex items-center justify-between w-full mb-6 ${className}`}>
    <div className="flex items-start gap-3 min-w-0">
      {icon && <span className="h-8 w-8 flex items-center justify-center text-blue-600 flex-shrink-0">{icon}</span>}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 leading-tight">{title}</h1>
        {subtitle && <p className="text-gray-500 mt-1 leading-snug">{subtitle}</p>}
      </div>
    </div>
    {actions && (
      <div className="flex items-center space-x-2">{actions}</div>
    )}
  </div>
);

export default SectionHeader; 
import React, { useState, useEffect } from 'react';
import { 
  Share2, 
  Users, 
  Package, 
  DollarSign, 
  TrendingUp, 
  Plus,
  ArrowUpRight,
  ArrowDownLeft,
  Network,
  RefreshCw
} from 'lucide-react';
import StatCard from '../components/StatCard';
import SectionHeader from '../components/SectionHeader';
import ShareProductModal from '../components/ShareProductModal';
import SharedProductsTable from '../components/SharedProductsTable';
import { vendorApi, Vendor } from '../services/vendorApi';
import { productSharingApi } from '../services/productSharingApi';
import { productApi } from '../services/productApi';

const ProductSharingPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'vendors' | 'shared' | 'inbound'>('overview');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [vendorStats, setVendorStats] = useState<any>(null);
  const [sharingStats, setSharingStats] = useState<any>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [vendorsResult, vendorStatsResult, sharingStatsResult, productsResult] = await Promise.all([
        vendorApi.getVendors(),
        vendorApi.getVendorStats(),
        productSharingApi.getSharingStats(),
        productApi.getProducts({ limit: 50 })
      ]);
      
      setVendors(vendorsResult.vendors);
      setVendorStats(vendorStatsResult);
      setSharingStats(sharingStatsResult);
      setProducts(productsResult.products);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleShareProduct = (product: any) => {
    setSelectedProduct(product);
    setShowShareModal(true);
  };

  const handleShareSuccess = () => {
    loadData(); // Refresh data after sharing
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', text: 'Active' },
      pending: { color: 'bg-yellow-100 text-yellow-800', text: 'Pending' },
      suspended: { color: 'bg-red-100 text-red-800', text: 'Suspended' },
      inactive: { color: 'bg-gray-100 text-gray-800', text: 'Inactive' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<Network className="h-8 w-8" />}
        title="Product Sharing"
        subtitle="Manage product sharing with vendor partners and distributors"
        actions={
          <button
            onClick={loadData}
            className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        }
      />

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="Total Vendors"
          value={vendorStats?.total_vendors || 0}
          subtitle="Registered partners"
          icon={Users}
          iconColor="text-purple-600"
          trend={{
            value: `${vendorStats?.active_vendors || 0} active`,
            isPositive: true,
            period: "currently"
          }}
        />
        <StatCard
          title="Products Shared"
          value={sharingStats?.total_shares || 0}
          subtitle="Active shares"
          icon={Package}
          iconColor="text-blue-600"
          trend={{
            value: `${sharingStats?.active_shares || 0} active`,
            isPositive: true,
            period: "currently"
          }}
        />
        <StatCard
          title="Total Revenue"
          value={`$${(sharingStats?.total_revenue || 0).toLocaleString()}`}
          subtitle="Generated revenue"
          icon={DollarSign}
          iconColor="text-green-600"
          trend={{
            value: `${sharingStats?.total_sales || 0} sales`,
            isPositive: true,
            period: "total"
          }}
        />
        <StatCard
          title="Avg Revenue/Share"
          value={`$${(sharingStats?.average_revenue_per_share || 0).toFixed(0)}`}
          subtitle="Per shared product"
          icon={TrendingUp}
          iconColor="text-orange-600"
          trend={{
            value: "per share",
            isPositive: true,
            period: "average"
          }}
        />
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: Network },
            { id: 'vendors', label: 'Vendors', icon: Users },
            { id: 'shared', label: 'Shared Products', icon: Share2 },
            { id: 'inbound', label: 'Inbound Products', icon: ArrowDownLeft }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <ArrowUpRight className="h-5 w-5 mr-2 text-blue-600" />
                Share Products
              </h3>
              <p className="text-gray-600 mb-4">
                Share your products with vendor partners to expand your reach and increase sales.
              </p>
              <button
                onClick={() => setActiveTab('shared')}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Start Sharing
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Users className="h-5 w-5 mr-2 text-green-600" />
                Manage Vendors
              </h3>
              <p className="text-gray-600 mb-4">
                Add new vendors, update contact information, and manage partnership settings.
              </p>
              <button
                onClick={() => setActiveTab('vendors')}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Manage Vendors
              </button>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <ArrowDownLeft className="h-5 w-5 mr-2 text-purple-600" />
                Import Products
              </h3>
              <p className="text-gray-600 mb-4">
                Browse and import products from your vendor partners to expand your catalog.
              </p>
              <button
                onClick={() => setActiveTab('inbound')}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                Browse Products
              </button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {vendors.slice(0, 5).map((vendor) => (
                  <div key={vendor.id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="h-4 w-4 text-gray-500" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">{vendor.organization_name}</p>
                        <p className="text-sm text-gray-500">{vendor.product_share_count} products shared</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(vendor.status)}
                      <span className="text-sm text-gray-500">
                        {vendor.last_sync ? new Date(vendor.last_sync).toLocaleDateString() : 'Never synced'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'vendors' && (
        <div className="space-y-6">
          {/* Vendor Management */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Vendor Partners</h3>
              <button className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Add Vendor
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Products Shared</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Sync</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {vendors.map((vendor) => (
                    <tr key={vendor.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{vendor.organization_name}</div>
                        <div className="text-sm text-gray-500">{vendor.tier} tier</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{vendor.contact_person.name}</div>
                        <div className="text-sm text-gray-500">{vendor.contact_person.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{vendor.product_share_count}</div>
                        <div className="text-sm text-gray-500">products</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(vendor.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {vendor.last_sync ? new Date(vendor.last_sync).toLocaleDateString() : 'Never'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                        <button className="text-red-600 hover:text-red-900">Remove</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'shared' && (
        <div className="space-y-6">
          {/* Product Selection for Sharing */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Your Products</h3>
              <button
                onClick={() => setShowShareModal(true)}
                className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share Products
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {products.slice(0, 10).map((product) => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.sku}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.price}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {product.is_visible ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleShareProduct(product)}
                          className="text-green-600 hover:text-green-900"
                        >
                          Share
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Shared Products Table */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Shared Products</h3>
            </div>
            <div className="p-6">
              <SharedProductsTable 
                onRefresh={loadData} 
                onShareProduct={(product) => {
                  // Create a mock product object for the modal
                  const mockProduct = {
                    id: product.product_id,
                    name: product.product_name,
                    sku: product.product_sku
                  };
                  setSelectedProduct(mockProduct);
                  setShowShareModal(true);
                }}
              />
            </div>
          </div>
        </div>
      )}

      {activeTab === 'inbound' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Inbound Products</h3>
            <p className="text-gray-600">
              Browse and import products from your vendor partners. This feature allows you to see products 
              that vendors have shared with you and import them into your catalog.
            </p>
            <div className="mt-4">
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Browse Vendor Products
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Share Product Modal */}
      {showShareModal && selectedProduct && (
        <ShareProductModal
          isOpen={showShareModal}
          onClose={() => {
            setShowShareModal(false);
            setSelectedProduct(null);
          }}
          productId={selectedProduct.id}
          productName={selectedProduct.name}
          productSku={selectedProduct.sku || ''}
          onShareSuccess={handleShareSuccess}
        />
      )}
    </div>
  );
};

export default ProductSharingPage; 
import React, { useState } from 'react'
import {
  ArrowDownLeft,
  Search,
  Filter,
  Download,
  Upload,
  Plus,
  Eye,
  ShoppingCart,
  Check,
  X,
  Clock,
  AlertCircle,
  Building2,
  Package,
  DollarSign,
  Star,
  MoreHorizontal,
  RefreshCw,
  CheckCircle
} from 'lucide-react'
import StatCard from '../components/StatCard'
import MultiSelectDropdown from '../components/MultiSelectDropdown'
import SectionHeader from '../components/SectionHeader'

const InboundSharingPage = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedVendors, setSelectedVendors] = useState<string[]>([])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])

  // Options for multi-select dropdowns
  const vendorOptions = [
    { value: 'acme-corp', label: 'Acme Corp' },
    { value: 'tech-solutions', label: 'Tech Solutions' },
    { value: 'global-supply', label: 'Global Supply' }
  ]

  const categoryOptions = [
    { value: 'electronics', label: 'Electronics' },
    { value: 'clothing', label: 'Clothing' },
    { value: 'home-garden', label: 'Home & Garden' }
  ]

  const statusOptions = [
    { value: 'available', label: 'Available' },
    { value: 'pending', label: 'Pending' },
    { value: 'imported', label: 'Imported' }
  ]
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [showProductModal, setShowProductModal] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)

  // Mock data for vendor products
  const mockVendorProducts = [
    {
      id: '1',
      name: 'Premium Wireless Earbuds',
      sku: 'VEN-PWE-001',
      vendor: 'TechCorp Solutions',
      vendor_id: 'vendor_1',
      category: 'Electronics',
      price: 149.99,
      wholesale_price: 89.99,
      stock: 250,
      rating: 4.8,
      status: 'available',
      last_updated: '2024-01-15T10:30:00Z',
      description: 'High-quality wireless earbuds with noise cancellation',
      image_url: null,
      vendor_rating: 4.9,
      min_order_qty: 10
    },
    {
      id: '2',
      name: 'Smart Fitness Tracker Pro',
      sku: 'VEN-SFT-002',
      vendor: 'HealthTech Inc',
      vendor_id: 'vendor_2',
      category: 'Wearables',
      price: 199.99,
      wholesale_price: 129.99,
      stock: 150,
      rating: 4.6,
      status: 'pending_approval',
      last_updated: '2024-01-14T15:45:00Z',
      description: 'Advanced fitness tracker with heart rate monitoring',
      image_url: null,
      vendor_rating: 4.7,
      min_order_qty: 5
    },
    {
      id: '3',
      name: 'Organic Cotton Hoodie',
      sku: 'VEN-OCH-003',
      vendor: 'EcoFashion Co',
      vendor_id: 'vendor_3',
      category: 'Clothing',
      price: 79.99,
      wholesale_price: 45.99,
      stock: 0,
      rating: 4.4,
      status: 'out_of_stock',
      last_updated: '2024-01-13T09:20:00Z',
      description: 'Sustainable organic cotton hoodie in multiple colors',
      image_url: null,
      vendor_rating: 4.5,
      min_order_qty: 20
    },
    {
      id: '4',
      name: 'Professional Camera Lens',
      sku: 'VEN-PCL-004',
      vendor: 'PhotoGear Ltd',
      vendor_id: 'vendor_4',
      category: 'Photography',
      price: 899.99,
      wholesale_price: 649.99,
      stock: 45,
      rating: 4.9,
      status: 'imported',
      last_updated: '2024-01-12T14:10:00Z',
      description: '85mm f/1.4 professional portrait lens',
      image_url: null,
      vendor_rating: 4.8,
      min_order_qty: 1
    }
  ]

  const mockVendors = [
    { id: 'vendor_1', name: 'TechCorp Solutions', rating: 4.9, products: 156 },
    { id: 'vendor_2', name: 'HealthTech Inc', rating: 4.7, products: 89 },
    { id: 'vendor_3', name: 'EcoFashion Co', rating: 4.5, products: 234 },
    { id: 'vendor_4', name: 'PhotoGear Ltd', rating: 4.8, products: 67 }
  ]

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      available: { color: 'bg-green-100 text-green-800', icon: Check, text: 'Available' },
      pending_approval: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pending' },
      out_of_stock: { color: 'bg-red-100 text-red-800', icon: AlertCircle, text: 'Out of Stock' },
      imported: { color: 'bg-blue-100 text-blue-800', icon: Check, text: 'Imported' }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig]
    const Icon = config.icon
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3 mr-1" />
        {config.text}
      </span>
    )
  }

  const handleViewProduct = (product: any) => {
    setSelectedProduct(product)
    setShowProductModal(true)
  }

  const handleImportProduct = (productId: string) => {
    console.log('Importing product:', productId)
    // Here you would implement the actual import logic
  }

  const handleBulkImport = () => {
    console.log('Bulk importing products:', selectedProducts)
    // Here you would implement bulk import logic
  }

  const filteredProducts = mockVendorProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesVendor = selectedVendors.length === 0 || selectedVendors.includes(product.vendor_id)
    const matchesCategory = selectedCategories.length === 0 || selectedCategories.includes(product.category)
    const matchesStatus = selectedStatuses.length === 0 || selectedStatuses.includes(product.status)

    return matchesSearch && matchesVendor && matchesCategory && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <SectionHeader
        icon={<ArrowDownLeft className="h-8 w-8" />}
        title="Inbound Products"
        subtitle="Browse and import products from your vendor partners"
        actions={
          <>
            {selectedProducts.length > 0 && (
              <button
                onClick={handleBulkImport}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center mr-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Import Selected ({selectedProducts.length})
              </button>
            )}
            <button
              className="px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
              onClick={() => { /* Refresh logic here */ }}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </>
        }
      />

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title="Available Products"
          value={mockVendorProducts.filter(p => p.status === 'available').length}
          subtitle="Ready to import"
          icon={Package}
          iconColor="text-blue-600"
          trend={{
            value: "12%",
            isPositive: true,
            period: "last month"
          }}
        />
        <StatCard
          title="Active Vendors"
          value={mockVendors.length}
          subtitle="Connected partners"
          icon={Building2}
          iconColor="text-green-600"
          trend={{
            value: "2",
            isPositive: true,
            period: "last quarter"
          }}
        />
        <StatCard
          title="Pending Approval"
          value={mockVendorProducts.filter(p => p.status === 'pending_approval').length}
          subtitle="Awaiting review"
          icon={Clock}
          iconColor="text-yellow-600"
          trend={{
            value: "5%",
            isPositive: false,
            period: "last week"
          }}
        />
        <StatCard
          title="Imported Products"
          value={mockVendorProducts.filter(p => p.status === 'imported').length}
          subtitle="Successfully added"
          icon={CheckCircle}
          iconColor="text-purple-600"
          trend={{
            value: "18",
            isPositive: true,
            period: "last 30 days"
          }}
        />
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full text-sm focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <MultiSelectDropdown
          options={vendorOptions}
          value={selectedVendors}
          onChange={setSelectedVendors}
          placeholder="All Vendors"
          searchPlaceholder="Search vendors..."
        />
        <MultiSelectDropdown
          options={categoryOptions}
          value={selectedCategories}
          onChange={setSelectedCategories}
          placeholder="All Categories"
          searchPlaceholder="Search categories..."
        />
        <MultiSelectDropdown
          options={statusOptions}
          value={selectedStatuses}
          onChange={setSelectedStatuses}
          placeholder="All Status"
          searchPlaceholder="Search status..."
        />
        <div className="flex space-x-2">
          <button className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedProducts(filteredProducts.map(p => p.id))
                      } else {
                        setSelectedProducts([])
                      }
                    }}
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pricing</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      checked={selectedProducts.includes(product.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedProducts([...selectedProducts, product.id])
                        } else {
                          setSelectedProducts(selectedProducts.filter(id => id !== product.id))
                        }
                      }}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                          <Package className="h-5 w-5 text-gray-500" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        <div className="text-sm text-gray-500">{product.sku}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Building2 className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm text-gray-900">{product.vendor}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">{product.category}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center">
                        <DollarSign className="h-3 w-3 text-gray-400" />
                        <span className="font-medium">{product.wholesale_price}</span>
                        <span className="text-gray-500 ml-1">/ ${product.price}</span>
                      </div>
                      <div className="text-xs text-gray-500">Min: {product.min_order_qty} units</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm ${product.stock > 0 ? 'text-gray-900' : 'text-red-600'}`}>
                      {product.stock} units
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(product.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleViewProduct(product)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {product.status === 'available' && (
                        <button
                          onClick={() => handleImportProduct(product.id)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <ShoppingCart className="h-4 w-4" />
                        </button>
                      )}
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Product Detail Modal */}
      {showProductModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-6">
              <h3 className="text-xl font-semibold text-gray-900">{selectedProduct.name}</h3>
              <button
                onClick={() => setShowProductModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Product Image */}
              <div>
                <div className="aspect-square bg-gray-200 rounded-lg flex items-center justify-center mb-4">
                  <Package className="h-24 w-24 text-gray-400" />
                </div>
                <div className="grid grid-cols-3 gap-2">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                      <Package className="h-8 w-8 text-gray-300" />
                    </div>
                  ))}
                </div>
              </div>

              {/* Product Details */}
              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Product Information</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500">SKU:</span>
                      <span className="text-gray-900">{selectedProduct.sku}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Category:</span>
                      <span className="text-gray-900">{selectedProduct.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Vendor:</span>
                      <span className="text-gray-900">{selectedProduct.vendor}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Pricing</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Retail Price:</span>
                      <span className="text-gray-900">${selectedProduct.price}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Wholesale Price:</span>
                      <span className="text-green-600 font-medium">${selectedProduct.wholesale_price}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Min Order Qty:</span>
                      <span className="text-gray-900">{selectedProduct.min_order_qty} units</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Availability</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Stock:</span>
                      <span className={`${selectedProduct.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {selectedProduct.stock} units
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Status:</span>
                      {getStatusBadge(selectedProduct.status)}
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Last Updated:</span>
                      <span className="text-gray-900">
                        {new Date(selectedProduct.last_updated).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-600">{selectedProduct.description}</p>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 pt-4">
                  {selectedProduct.status === 'available' && (
                    <button
                      onClick={() => {
                        handleImportProduct(selectedProduct.id)
                        setShowProductModal(false)
                      }}
                      className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      <ShoppingCart className="h-4 w-4 mr-2 inline" />
                      Import Product
                    </button>
                  )}
                  <button
                    onClick={() => setShowProductModal(false)}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default InboundSharingPage

import { Settings, UserPlus, Users, Clock, Layers, Bell, Package } from 'lucide-react';
import GuestOrganizationUsers from '../components/users/GuestOrganizationUsers';
import SectionHeader from '../components/SectionHeader';
import { Link, useLocation } from 'react-router-dom';

const settingsTabs = [
  { id: 'general', name: 'General', icon: Settings, href: '/settings/general' },
  { id: 'jobs', name: 'Jobs', icon: Clock, href: '/settings/jobs' },
  { id: 'users', name: 'User Management', icon: Users, href: '/settings/users/my-organization/' },
  { id: 'organization', name: 'Organization', icon: UserPlus, href: '/settings/organization' },
  { id: 'custom-data', name: 'Custom Data', icon: Layers, href: '/settings/custom-data' },
  { id: 'notifications', name: 'Notifications', icon: Bell, href: '/settings/notifications' },
  { id: 'components', name: 'Components', icon: Package, href: '/settings/components' },
];

const OrganizationPage = () => {
  const location = useLocation();
  const activeSettingsTab = settingsTabs.find(tab => location.pathname.startsWith(tab.href))?.id;
  return (
    <div className="-m-6 min-h-screen bg-gray-50">
      <div className="flex">
        <div className="w-72 bg-white border-r border-gray-200 min-h-screen">
          <div className="p-6 border-b border-gray-100">
            <SectionHeader
              icon={<Settings className="h-7 w-7" />}
              title="Organization"
              subtitle="Manage users for your organization"
            />
          </div>
          <nav className="p-4 space-y-1">
            {settingsTabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <Link
                  key={tab.id}
                  to={tab.href}
                  className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                    activeSettingsTab === tab.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-sm'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-3 ${
                    activeSettingsTab === tab.id ? 'text-blue-600' : 'text-gray-400'
                  }`} />
                  {tab.name}
                </Link>
              );
            })}
          </nav>
        </div>
        <div className="flex-1">
          <div className="p-8">
            <div className="flex items-center justify-between mb-6">
              <SectionHeader
                icon={<UserPlus className="h-7 w-7" />}
                title="Organization"
                subtitle="Manage users for your organization"
              />
            </div>
            <div className="border-b border-gray-200 mb-6">
              <nav className="-mb-px flex space-x-8">
                <Link
                  to="/settings/organization"
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors border-blue-500 text-blue-600`}
                >
                  <div className="flex items-center space-x-2">
                    <UserPlus className="h-4 w-4" />
                    <span>Organization</span>
                  </div>
                </Link>
              </nav>
            </div>
            <div className="space-y-6">
              <GuestOrganizationUsers />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganizationPage; 
import { useState, useEffect } from 'react'
import { ChevronDown, Calendar, Check, X, Plus, Settings } from 'lucide-react'
import {
  TextField,
  NumberField,
  SingleSelectField,
  MultiSelectField
} from './fieldTypes'

interface CustomField {
  id: string
  label: string
  key: string
  field_type: string
  resource_type: string
  namespace: string
  description?: string
  placeholder_text?: string
  help_text?: string
  is_required: boolean
  is_searchable: boolean
  is_filterable: boolean
  is_ai_enabled: boolean
  is_active: boolean
  field_options?: any
  validation_rules?: any
}

interface CustomFieldRendererProps {
  fields: CustomField[]
  values: { [key: string]: any }
  onChange: (key: string, value: any) => void
  resourceType: string
  showFieldManagement?: boolean
  onAddField?: () => void
  onFieldSettings?: (field: CustomField) => void
  onRemoveField?: (fieldId: string) => void
}

const CustomFieldRenderer: React.FC<CustomFieldRendererProps> = ({
  fields,
  values,
  onChange,
  resourceType,
  showFieldManagement = false,
  onAddField,
  onFieldSettings,
  onRemoveField
}) => {
  const [openDropdowns, setOpenDropdowns] = useState<{ [key: string]: boolean }>({})

  const toggleDropdown = (fieldKey: string) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey]
    }))
  }

  const renderField = (field: CustomField) => {
    const value = values[field.key] || ''
    const fieldId = `custom_field_${field.key}`
    const hasError = field.is_required && !value
    const errorMessage = hasError ? `${field.label} is required` : undefined

    const commonProps = {
      id: fieldId,
      value,
      onChange: (newValue: any) => onChange(field.key, newValue),
      placeholder: field.placeholder_text || `Enter ${field.label.toLowerCase()}...`,
      required: field.is_required,
      error: errorMessage,
      validation: field.validation_rules
    }

    switch (field.field_type) {
      case 'text':
        return <TextField {...commonProps} />

      case 'number':
        return <NumberField {...commonProps} />

      case 'single_select':
        return (
          <SingleSelectField
            {...commonProps}
            options={field.field_options?.options || []}
          />
        )

      case 'multi_select':
        return (
          <MultiSelectField
            {...commonProps}
            options={field.field_options?.options || []}
          />
        )

      case 'textarea':
        return <TextField {...commonProps} />

      case 'email':
        return <TextField {...commonProps} />

      case 'url':
        return <TextField {...commonProps} />

      case 'date':
        return <TextField {...commonProps} />

      case 'boolean':
        return <TextField {...commonProps} />

      default:
        return (
          <input
            id={fieldId}
            type="text"
            value={value}
            onChange={(e) => onChange(field.key, e.target.value)}
            placeholder={field.placeholder_text || `Enter ${field.label.toLowerCase()}...`}
            className={`${baseClasses} ${errorClasses}`}
            required={field.is_required}
          />
        )
    }
  }

  // Filter fields for the current resource type
  const relevantFields = fields.filter(field => 
    field.resource_type === resourceType || field.resource_type === 'global'
  ).filter(field => field.is_active)

  if (relevantFields.length === 0 && !showFieldManagement) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Custom Fields Header */}
      {(relevantFields.length > 0 || showFieldManagement) && (
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Custom Fields</h3>
            <p className="text-sm text-gray-500">Additional information for this {resourceType.slice(0, -1)}</p>
          </div>
          {showFieldManagement && (
            <button
              type="button"
              onClick={onAddField}
              className="btn btn-secondary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Field
            </button>
          )}
        </div>
      )}

      {/* Custom Fields */}
      {relevantFields.map((field) => (
        <div key={field.id} className="space-y-2">
          <div className="flex items-center justify-between">
            <label
              htmlFor={`custom_field_${field.key}`}
              className="block text-sm font-medium text-gray-700"
            >
              {field.label}
              {field.is_required && <span className="text-red-500 ml-1">*</span>}
            </label>
            {showFieldManagement && (
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => onFieldSettings?.(field)}
                  className="text-gray-400 hover:text-gray-600 p-1 rounded"
                  title="Field Settings"
                >
                  <Settings className="h-4 w-4" />
                </button>
                <button
                  type="button"
                  onClick={() => onRemoveField?.(field.id)}
                  className="text-red-400 hover:text-red-600 p-1 rounded"
                  title="Remove Field"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
          
          {renderField(field)}
          
          {field.help_text && (
            <p className="text-xs text-gray-500">{field.help_text}</p>
          )}
          
          {field.is_required && !values[field.key] && (
            <p className="text-xs text-red-500">This field is required</p>
          )}
        </div>
      ))}

      {/* No fields message */}
      {relevantFields.length === 0 && showFieldManagement && (
        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="flex flex-col items-center">
            <Settings className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No custom fields</h3>
            <p className="text-gray-500 mb-4">Add custom fields to collect additional information</p>
            <button
              type="button"
              onClick={onAddField}
              className="btn btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Field
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomFieldRenderer

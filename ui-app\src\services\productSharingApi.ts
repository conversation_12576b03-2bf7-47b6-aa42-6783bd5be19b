import { delay } from '../utils/delay';

export interface SharedProduct {
  id: string;
  product_id: string;
  product_name: string;
  product_sku: string;
  vendor_id: string;
  vendor_name: string;
  shared_at: string;
  is_active: boolean;
  permissions: ProductPermissions;
  expires_at?: string;
  revenue_generated: number;
  total_sales: number;
  last_sync: string | null;
}

export interface ProductPermissions {
  view: boolean;
  edit: boolean;
  share: boolean;
  delete: boolean;
  pricing_access: boolean;
  inventory_access: boolean;
}

export interface ShareProductRequest {
  product_id: string;
  vendor_id: string;
  permissions: ProductPermissions;
  expires_at?: string;
}

export interface ShareProductResponse {
  success: boolean;
  message: string;
  shared_product: SharedProduct;
}

export interface UnshareProductRequest {
  product_id: string;
  vendor_id: string;
}

export interface UnshareProductResponse {
  success: boolean;
  message: string;
}

export interface SharedProductListResult {
  shared_products: SharedProduct[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface SharedProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  vendor_id?: string;
  product_id?: string;
  is_active?: boolean;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

// Mock shared products data
const mockSharedProducts: SharedProduct[] = [
  {
    id: 'share_1',
    product_id: 'prod_1',
    product_name: 'Wireless Bluetooth Headphones',
    product_sku: 'WBH-001',
    vendor_id: 'vendor_1',
    vendor_name: 'TechCorp Solutions',
    shared_at: '2024-01-15T10:30:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 6757.11,
    total_sales: 89,
    last_sync: '2024-01-15T10:30:00Z'
  },
  {
    id: 'share_2',
    product_id: 'prod_2',
    product_name: 'Smart Fitness Watch',
    product_sku: 'SFW-002',
    vendor_id: 'vendor_2',
    vendor_name: 'HealthTech Inc',
    shared_at: '2024-01-14T15:45:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: false
    },
    revenue_generated: 3449.77,
    total_sales: 23,
    last_sync: '2024-01-14T15:45:00Z'
  },
  {
    id: 'share_3',
    product_id: 'prod_1',
    product_name: 'Wireless Bluetooth Headphones',
    product_sku: 'WBH-001',
    vendor_id: 'vendor_5',
    vendor_name: 'ElectroMart',
    shared_at: '2024-01-13T09:20:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 8999.88,
    total_sales: 120,
    last_sync: '2024-01-16T09:15:00Z'
  },
  {
    id: 'share_4',
    product_id: 'prod_4',
    product_name: 'Professional Camera Lens',
    product_sku: 'PCL-004',
    vendor_id: 'vendor_4',
    vendor_name: 'PhotoGear Ltd',
    shared_at: '2024-01-12T14:10:00Z',
    is_active: false,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: false,
      inventory_access: false
    },
    expires_at: '2024-02-12T14:10:00Z',
    revenue_generated: 8399.88,
    total_sales: 12,
    last_sync: '2024-01-12T14:10:00Z'
  },
  {
    id: 'share_5',
    product_id: 'prod_5',
    product_name: 'Gaming Laptop Pro',
    product_sku: 'GLP-005',
    vendor_id: 'vendor_1',
    vendor_name: 'TechCorp Solutions',
    shared_at: '2024-01-11T11:20:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 15499.99,
    total_sales: 45,
    last_sync: '2024-01-15T16:30:00Z'
  },
  {
    id: 'share_6',
    product_id: 'prod_6',
    product_name: 'Wireless Charging Pad',
    product_sku: 'WCP-006',
    vendor_id: 'vendor_3',
    vendor_name: 'EcoFashion Co',
    shared_at: '2024-01-10T09:15:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: false
    },
    revenue_generated: 2899.50,
    total_sales: 67,
    last_sync: '2024-01-14T12:45:00Z'
  },
  {
    id: 'share_7',
    product_id: 'prod_7',
    product_name: 'Smart Home Hub',
    product_sku: 'SHH-007',
    vendor_id: 'vendor_2',
    vendor_name: 'HealthTech Inc',
    shared_at: '2024-01-09T14:30:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 5678.90,
    total_sales: 34,
    last_sync: '2024-01-13T10:20:00Z'
  },
  {
    id: 'share_8',
    product_id: 'prod_8',
    product_name: 'Portable Bluetooth Speaker',
    product_sku: 'PBS-008',
    vendor_id: 'vendor_5',
    vendor_name: 'ElectroMart',
    shared_at: '2024-01-08T16:45:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 4234.75,
    total_sales: 89,
    last_sync: '2024-01-12T08:15:00Z'
  },
  {
    id: 'share_9',
    product_id: 'prod_9',
    product_name: '4K Action Camera',
    product_sku: '4AC-009',
    vendor_id: 'vendor_4',
    vendor_name: 'PhotoGear Ltd',
    shared_at: '2024-01-07T13:20:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: false
    },
    revenue_generated: 7899.99,
    total_sales: 23,
    last_sync: '2024-01-11T15:40:00Z'
  },
  {
    id: 'share_10',
    product_id: 'prod_10',
    product_name: 'Mechanical Gaming Keyboard',
    product_sku: 'MGK-010',
    vendor_id: 'vendor_1',
    vendor_name: 'TechCorp Solutions',
    shared_at: '2024-01-06T10:10:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 3456.78,
    total_sales: 56,
    last_sync: '2024-01-10T11:30:00Z'
  },
  {
    id: 'share_11',
    product_id: 'prod_11',
    product_name: 'Smart LED Strip Lights',
    product_sku: 'SLS-011',
    vendor_id: 'vendor_3',
    vendor_name: 'EcoFashion Co',
    shared_at: '2024-01-05T12:25:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 2345.60,
    total_sales: 78,
    last_sync: '2024-01-09T14:20:00Z'
  },
  {
    id: 'share_12',
    product_id: 'prod_12',
    product_name: 'Fitness Tracker Elite',
    product_sku: 'FTE-012',
    vendor_id: 'vendor_2',
    vendor_name: 'HealthTech Inc',
    shared_at: '2024-01-04T08:50:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: false
    },
    revenue_generated: 4567.89,
    total_sales: 45,
    last_sync: '2024-01-08T16:10:00Z'
  },
  {
    id: 'share_13',
    product_id: 'prod_13',
    product_name: 'Wireless Earbuds Pro',
    product_sku: 'WEP-013',
    vendor_id: 'vendor_5',
    vendor_name: 'ElectroMart',
    shared_at: '2024-01-03T15:35:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 6789.12,
    total_sales: 67,
    last_sync: '2024-01-07T12:45:00Z'
  },
  {
    id: 'share_14',
    product_id: 'prod_14',
    product_name: 'Smartphone Gimbal Stabilizer',
    product_sku: 'SGS-014',
    vendor_id: 'vendor_4',
    vendor_name: 'PhotoGear Ltd',
    shared_at: '2024-01-02T11:40:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 3456.78,
    total_sales: 34,
    last_sync: '2024-01-06T09:30:00Z'
  },
  {
    id: 'share_15',
    product_id: 'prod_15',
    product_name: 'USB-C Hub Adapter',
    product_sku: 'UHA-015',
    vendor_id: 'vendor_1',
    vendor_name: 'TechCorp Solutions',
    shared_at: '2024-01-01T14:15:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: false
    },
    revenue_generated: 1234.56,
    total_sales: 89,
    last_sync: '2024-01-05T13:20:00Z'
  },
  {
    id: 'share_16',
    product_id: 'prod_16',
    product_name: 'Smart Coffee Maker',
    product_sku: 'SCM-016',
    vendor_id: 'vendor_3',
    vendor_name: 'EcoFashion Co',
    shared_at: '2023-12-31T10:25:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 5678.90,
    total_sales: 23,
    last_sync: '2024-01-04T11:15:00Z'
  },
  {
    id: 'share_17',
    product_id: 'prod_17',
    product_name: 'Heart Rate Monitor',
    product_sku: 'HRM-017',
    vendor_id: 'vendor_2',
    vendor_name: 'HealthTech Inc',
    shared_at: '2023-12-30T16:50:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 2345.67,
    total_sales: 45,
    last_sync: '2024-01-03T14:40:00Z'
  },
  {
    id: 'share_18',
    product_id: 'prod_18',
    product_name: 'Portable Power Bank',
    product_sku: 'PPB-018',
    vendor_id: 'vendor_5',
    vendor_name: 'ElectroMart',
    shared_at: '2023-12-29T12:30:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: false
    },
    revenue_generated: 3456.78,
    total_sales: 67,
    last_sync: '2024-01-02T10:25:00Z'
  },
  {
    id: 'share_19',
    product_id: 'prod_19',
    product_name: 'Drone with Camera',
    product_sku: 'DWC-019',
    vendor_id: 'vendor_4',
    vendor_name: 'PhotoGear Ltd',
    shared_at: '2023-12-28T09:45:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 8999.99,
    total_sales: 12,
    last_sync: '2024-01-01T15:50:00Z'
  },
  {
    id: 'share_20',
    product_id: 'prod_20',
    product_name: 'Wireless Mouse Gaming',
    product_sku: 'WMG-020',
    vendor_id: 'vendor_1',
    vendor_name: 'TechCorp Solutions',
    shared_at: '2023-12-27T13:20:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 2345.67,
    total_sales: 78,
    last_sync: '2023-12-31T12:10:00Z'
  },
  {
    id: 'share_21',
    product_id: 'prod_21',
    product_name: 'Smart Thermostat',
    product_sku: 'STH-021',
    vendor_id: 'vendor_3',
    vendor_name: 'EcoFashion Co',
    shared_at: '2023-12-26T11:15:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: false
    },
    revenue_generated: 4567.89,
    total_sales: 34,
    last_sync: '2023-12-30T14:30:00Z'
  },
  {
    id: 'share_22',
    product_id: 'prod_22',
    product_name: 'Blood Pressure Monitor',
    product_sku: 'BPM-022',
    vendor_id: 'vendor_2',
    vendor_name: 'HealthTech Inc',
    shared_at: '2023-12-25T15:40:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 3456.78,
    total_sales: 56,
    last_sync: '2023-12-29T16:20:00Z'
  },
  {
    id: 'share_23',
    product_id: 'prod_23',
    product_name: 'Bluetooth Car Speaker',
    product_sku: 'BCS-023',
    vendor_id: 'vendor_5',
    vendor_name: 'ElectroMart',
    shared_at: '2023-12-24T10:30:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: true
    },
    revenue_generated: 2345.67,
    total_sales: 45,
    last_sync: '2023-12-28T11:45:00Z'
  },
  {
    id: 'share_24',
    product_id: 'prod_24',
    product_name: 'Camera Tripod Professional',
    product_sku: 'CTP-024',
    vendor_id: 'vendor_4',
    vendor_name: 'PhotoGear Ltd',
    shared_at: '2023-12-23T14:25:00Z',
    is_active: true,
    permissions: {
      view: true,
      edit: false,
      share: false,
      delete: false,
      pricing_access: true,
      inventory_access: false
    },
    revenue_generated: 1234.56,
    total_sales: 23,
    last_sync: '2023-12-27T13:15:00Z'
  }
];

export const productSharingApi = {
  // Get all shared products with filtering and pagination
  getSharedProducts: async (params?: SharedProductQueryParams): Promise<SharedProductListResult> => {
    await delay();
    
    let filteredProducts = [...mockSharedProducts];

    // Apply filters
    if (params?.search) {
      const search = params.search.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.product_name.toLowerCase().includes(search) ||
        product.product_sku.toLowerCase().includes(search) ||
        product.vendor_name.toLowerCase().includes(search)
      );
    }

    if (params?.vendor_id) {
      filteredProducts = filteredProducts.filter(product => product.vendor_id === params.vendor_id);
    }

    if (params?.product_id) {
      filteredProducts = filteredProducts.filter(product => product.product_id === params.product_id);
    }

    if (params?.is_active !== undefined) {
      filteredProducts = filteredProducts.filter(product => product.is_active === params.is_active);
    }

    // Apply sorting
    if (params?.sort_by) {
      const sortBy = params.sort_by as keyof SharedProduct;
      const sortOrder = params.sort_order === 'ASC' ? 1 : -1;

      filteredProducts.sort((a, b) => {
        const aVal = a[sortBy];
        const bVal = b[sortBy];

        if (typeof aVal === 'string' && typeof bVal === 'string') {
          return aVal.localeCompare(bVal) * sortOrder;
        }

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return (aVal - bVal) * sortOrder;
        }

        return 0;
      });
    }

    // Apply pagination
    const page = params?.page || 1;
    const limit = params?.limit || 20;
    const offset = (page - 1) * limit;
    const paginatedProducts = filteredProducts.slice(offset, offset + limit);

    return {
      shared_products: paginatedProducts,
      total: filteredProducts.length,
      page,
      limit,
      total_pages: Math.ceil(filteredProducts.length / limit)
    };
  },

  // Share a product with a vendor
  shareProduct: async (request: ShareProductRequest): Promise<ShareProductResponse> => {
    await delay();
    
    // Check if already shared
    const existingShare = mockSharedProducts.find(
      share => share.product_id === request.product_id && share.vendor_id === request.vendor_id
    );

    if (existingShare) {
      // Update existing share
      existingShare.permissions = request.permissions;
      existingShare.expires_at = request.expires_at;
      existingShare.is_active = true;
      existingShare.shared_at = new Date().toISOString();

      return {
        success: true,
        message: 'Product sharing updated successfully',
        shared_product: existingShare
      };
    }

    // Create new share
    const newShare: SharedProduct = {
      id: `share_${Date.now()}`,
      product_id: request.product_id,
      product_name: 'Product Name', // This would come from product lookup
      product_sku: 'SKU', // This would come from product lookup
      vendor_id: request.vendor_id,
      vendor_name: 'Vendor Name', // This would come from vendor lookup
      shared_at: new Date().toISOString(),
      is_active: true,
      permissions: request.permissions,
      expires_at: request.expires_at,
      revenue_generated: 0,
      total_sales: 0,
      last_sync: null
    };

    mockSharedProducts.push(newShare);

    return {
      success: true,
      message: 'Product shared successfully',
      shared_product: newShare
    };
  },

  // Unshare a product from a vendor
  unshareProduct: async (request: UnshareProductRequest): Promise<UnshareProductResponse> => {
    await delay();
    
    const shareIndex = mockSharedProducts.findIndex(
      share => share.product_id === request.product_id && share.vendor_id === request.vendor_id
    );

    if (shareIndex === -1) {
      throw new Error('Product share not found');
    }

    // Remove the share
    mockSharedProducts.splice(shareIndex, 1);

    return {
      success: true,
      message: 'Product unshared successfully'
    };
  },

  // Update sharing permissions
  updateSharingPermissions: async (
    productId: string,
    vendorId: string,
    permissions: ProductPermissions
  ): Promise<ShareProductResponse> => {
    await delay();
    
    const share = mockSharedProducts.find(
      s => s.product_id === productId && s.vendor_id === vendorId
    );

    if (!share) {
      throw new Error('Product share not found');
    }

    share.permissions = permissions;
    share.shared_at = new Date().toISOString();

    return {
      success: true,
      message: 'Sharing permissions updated successfully',
      shared_product: share
    };
  },

  // Get sharing statistics
  getSharingStats: async () => {
    await delay();
    
    const totalShares = mockSharedProducts.length;
    const activeShares = mockSharedProducts.filter(s => s.is_active).length;
    const totalRevenue = mockSharedProducts.reduce((sum, s) => sum + s.revenue_generated, 0);
    const totalSales = mockSharedProducts.reduce((sum, s) => sum + s.total_sales, 0);

    return {
      total_shares: totalShares,
      active_shares: activeShares,
      total_revenue: totalRevenue,
      total_sales: totalSales,
      average_revenue_per_share: totalShares > 0 ? totalRevenue / totalShares : 0
    };
  },

  // Bulk share products
  bulkShareProducts: async (requests: ShareProductRequest[]): Promise<ShareProductResponse[]> => {
    await delay();
    
    const results: ShareProductResponse[] = [];
    
    for (const request of requests) {
      try {
        const result = await productSharingApi.shareProduct(request);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          message: `Failed to share product ${request.product_id} with vendor ${request.vendor_id}`,
          shared_product: null as any
        });
      }
    }

    return results;
  },

  // Bulk unshare products
  bulkUnshareProducts: async (requests: UnshareProductRequest[]): Promise<UnshareProductResponse[]> => {
    await delay();
    
    const results: UnshareProductResponse[] = [];
    
    for (const request of requests) {
      try {
        const result = await productSharingApi.unshareProduct(request);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          message: `Failed to unshare product ${request.product_id} from vendor ${request.vendor_id}`
        });
      }
    }

    return results;
  }
}; 
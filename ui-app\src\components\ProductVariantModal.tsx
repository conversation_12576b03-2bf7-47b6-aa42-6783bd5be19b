import React, { useState, useEffect } from 'react'
import { X, Plus, Trash2, Edit, Save, ArrowRight, Palette } from 'lucide-react'
import { CreateProductVariantRequest, CreateVariantOptionValueRequest } from '../types/product'

interface VariantOption {
  id: string
  name: string
  display_name: string
  type: 'radio' | 'checkbox' | 'text' | 'number' | 'textarea'
  values: VariantOptionValue[]
  sort_order: number
}

interface VariantOptionValue {
  id: string
  label: string
  sort_order: number
}

interface ProductVariantModalProps {
  isOpen: boolean
  onClose: () => void
  variants: CreateProductVariantRequest[]
  onSave: (variants: CreateProductVariantRequest[]) => void
}

const ProductVariantModal: React.FC<ProductVariantModalProps> = ({
  isOpen,
  onClose,
  variants,
  onSave
}) => {
  const [variantOptions, setVariantOptions] = useState<VariantOption[]>([])
  const [generatedVariants, setGeneratedVariants] = useState<CreateProductVariantRequest[]>([])
  const [activeTab, setActiveTab] = useState<'options' | 'variants'>('options')
  const [editingOption, setEditingOption] = useState<VariantOption | null>(null)
  const [showOptionModal, setShowOptionModal] = useState(false)
  const [variantFilter, setVariantFilter] = useState<'all' | 'active' | 'disabled'>('all')

  // Initialize with existing variants if any
  useEffect(() => {
    if (variants.length > 0) {
      // Convert existing variants back to options structure
      const options: VariantOption[] = []
      const optionMap = new Map<string, VariantOption>()

      variants.forEach(variant => {
        variant.option_values?.forEach(optionValue => {
          if (!optionMap.has(optionValue.option_display_name)) {
            const newOption: VariantOption = {
              id: `option_${optionValue.option_id}`,
              name: optionValue.option_display_name.toLowerCase().replace(/\s+/g, '_'),
              display_name: optionValue.option_display_name,
              type: 'radio',
              values: [],
              sort_order: options.length
            }
            optionMap.set(optionValue.option_display_name, newOption)
            options.push(newOption)
          }

          const option = optionMap.get(optionValue.option_display_name)!
          if (!option.values.find(v => v.label === optionValue.label)) {
            option.values.push({
              id: `value_${optionValue.option_id}_${optionValue.label}`,
              label: optionValue.label,
              sort_order: option.values.length
            })
          }
        })
      })

      setVariantOptions(options)
      setGeneratedVariants(variants)
    }
  }, [variants])

  const addVariantOption = () => {
    setEditingOption(null)
    setShowOptionModal(true)
  }

  const editVariantOption = (option: VariantOption) => {
    setEditingOption(option)
    setShowOptionModal(true)
  }

  const deleteVariantOption = (optionId: string) => {
    setVariantOptions(prev => prev.filter(opt => opt.id !== optionId))
  }

  const saveVariantOption = (option: VariantOption) => {
    if (editingOption) {
      setVariantOptions(prev => 
        prev.map(opt => opt.id === editingOption.id ? option : opt)
      )
    } else {
      setVariantOptions(prev => [...prev, { ...option, id: `option_${Date.now()}` }])
    }
    setShowOptionModal(false)
    setEditingOption(null)
  }

  const generateVariants = () => {
    if (variantOptions.length === 0) return

    // Generate all possible combinations
    const combinations = generateCombinations(variantOptions)
    
    const newVariants: CreateProductVariantRequest[] = combinations.map((combination, index) => {
      const optionValues: CreateVariantOptionValueRequest[] = combination.map((value, optionIndex) => ({
        option_id: optionIndex + 1,
        option_display_name: variantOptions[optionIndex].display_name,
        label: value.label
      }))

      return {
        sku: `SKU-${index + 1}`,
        price: 0,
        inventory_level: 0,
        is_free_shipping: false,
        purchasing_disabled: false,
        option_values: optionValues
      }
    })

    setGeneratedVariants(newVariants)
    setActiveTab('variants')
  }

  const generateCombinations = (options: VariantOption[]): VariantOptionValue[][] => {
    if (options.length === 0) return []
    if (options.length === 1) return options[0].values.map(value => [value])

    const [firstOption, ...restOptions] = options
    const restCombinations = generateCombinations(restOptions)
    
    return firstOption.values.flatMap(value => 
      restCombinations.map(combination => [value, ...combination])
    )
  }

  const updateVariant = (index: number, field: keyof CreateProductVariantRequest, value: any) => {
    setGeneratedVariants(prev => 
      prev.map((variant, i) => 
        i === index ? { ...variant, [field]: value } : variant
      )
    )
  }

  const deleteVariant = (index: number) => {
    setGeneratedVariants(prev => prev.filter((_, i) => i !== index))
  }

  const handleSave = () => {
    onSave(generatedVariants)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Palette className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Product Variants</h2>
              <p className="text-sm text-gray-600">Manage product options and variants</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('options')}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'options'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Variant Options ({variantOptions.length})
          </button>
          <button
            onClick={() => setActiveTab('variants')}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'variants'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Generated Variants ({generatedVariants.length})
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          {activeTab === 'options' && (
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Variant Options</h3>
                  <p className="text-sm text-gray-600">
                    Define the options that will create variants (e.g., Size, Color, Material)
                  </p>
                </div>
                <button
                  onClick={addVariantOption}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Option
                </button>
              </div>

              {variantOptions.length === 0 ? (
                <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                  <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No variant options yet</h3>
                  <p className="text-gray-600 mb-4">
                    Add options like Size, Color, or Material to create product variants
                  </p>
                  <button
                    onClick={addVariantOption}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Option
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {variantOptions.map((option) => (
                    <div key={option.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">{option.display_name}</h4>
                          <p className="text-sm text-gray-600">
                            {option.values.length} values • {option.type}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => editVariantOption(option)}
                            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => deleteVariantOption(option.id)}
                            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {option.values.map((value) => (
                          <span
                            key={value.id}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {value.label}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {variantOptions.length > 0 && (
                <div className="mt-6 flex justify-end">
                  <button
                    onClick={generateVariants}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    Generate Variants
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'variants' && (
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Generated Variants</h3>
                  <p className="text-sm text-gray-600">
                    {generatedVariants.length} variants created from your options
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <select
                    value={variantFilter}
                    onChange={(e) => setVariantFilter(e.target.value as 'all' | 'active' | 'disabled')}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Variants</option>
                    <option value="active">Active Only</option>
                    <option value="disabled">Disabled Only</option>
                  </select>
                  <button
                    onClick={() => {
                      setGeneratedVariants(prev => 
                        prev.map(variant => ({ ...variant, purchasing_disabled: false }))
                      )
                    }}
                    className="inline-flex items-center px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
                  >
                    Enable All
                  </button>
                  <button
                    onClick={() => {
                      setGeneratedVariants(prev => 
                        prev.map(variant => ({ ...variant, purchasing_disabled: true }))
                      )
                    }}
                    className="inline-flex items-center px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm"
                  >
                    Disable All
                  </button>
                  <button
                    onClick={() => setActiveTab('options')}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Options
                  </button>
                </div>
              </div>

              {generatedVariants.length === 0 ? (
                <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                  <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No variants generated</h3>
                  <p className="text-gray-600 mb-4">
                    Add variant options and generate variants to see them here
                  </p>
                  <button
                    onClick={() => setActiveTab('options')}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
                    Back to Options
                  </button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Variant
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          SKU
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Inventory
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {generatedVariants
                        .filter(variant => {
                          if (variantFilter === 'all') return true;
                          if (variantFilter === 'active') return !variant.purchasing_disabled;
                          if (variantFilter === 'disabled') return variant.purchasing_disabled;
                          return true;
                        })
                        .map((variant, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {variant.option_values?.map((opt, i) => (
                                  <span key={i} className="inline-block mr-2">
                                    <span className="font-medium">{opt.option_display_name}:</span> {opt.label}
                                  </span>
                                ))}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="text"
                                value={variant.sku || ''}
                                onChange={(e) => updateVariant(index, 'sku', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="SKU"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="number"
                                step="0.01"
                                min="0"
                                value={variant.price || ''}
                                onChange={(e) => updateVariant(index, 'price', parseFloat(e.target.value) || 0)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="0.00"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="number"
                                min="0"
                                value={variant.inventory_level || ''}
                                onChange={(e) => updateVariant(index, 'inventory_level', parseInt(e.target.value) || 0)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="0"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <button
                                  onClick={() => updateVariant(index, 'purchasing_disabled', !variant.purchasing_disabled)}
                                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                                    variant.purchasing_disabled ? 'bg-gray-200' : 'bg-blue-600'
                                  }`}
                                >
                                  <span
                                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                      variant.purchasing_disabled ? 'translate-x-1' : 'translate-x-6'
                                    }`}
                                  />
                                </button>
                                <span className={`ml-2 text-xs font-medium ${
                                  variant.purchasing_disabled ? 'text-gray-500' : 'text-green-600'
                                }`}>
                                  {variant.purchasing_disabled ? 'Disabled' : 'Active'}
                                </span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => deleteVariant(index)}
                                className="text-red-600 hover:text-red-900 transition-colors"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {activeTab === 'options' && `${variantOptions.length} options defined`}
            {activeTab === 'variants' && `${generatedVariants.length} variants ready`}
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={generatedVariants.length === 0}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Variants
            </button>
          </div>
        </div>
      </div>

      {/* Option Modal */}
      {showOptionModal && (
        <VariantOptionModal
          isOpen={showOptionModal}
          onClose={() => {
            setShowOptionModal(false)
            setEditingOption(null)
          }}
          option={editingOption}
          onSave={saveVariantOption}
        />
      )}
    </div>
  )
}

// Variant Option Modal Component
interface VariantOptionModalProps {
  isOpen: boolean
  onClose: () => void
  option: VariantOption | null
  onSave: (option: VariantOption) => void
}

const VariantOptionModal: React.FC<VariantOptionModalProps> = ({
  isOpen,
  onClose,
  option,
  onSave
}) => {
  const [formData, setFormData] = useState<VariantOption>({
    id: '',
    name: '',
    display_name: '',
    type: 'radio',
    values: [],
    sort_order: 0
  })

  useEffect(() => {
    if (option) {
      setFormData(option)
    } else {
      setFormData({
        id: '',
        name: '',
        display_name: '',
        type: 'radio',
        values: [],
        sort_order: 0
      })
    }
  }, [option])

  const addValue = () => {
    setFormData(prev => ({
      ...prev,
      values: [...prev.values, { id: `value_${Date.now()}`, label: '', sort_order: prev.values.length }]
    }))
  }

  const updateValue = (index: number, field: keyof VariantOptionValue, value: any) => {
    setFormData(prev => ({
      ...prev,
      values: prev.values.map((val, i) => 
        i === index ? { ...val, [field]: value } : val
      )
    }))
  }

  const deleteValue = (index: number) => {
    setFormData(prev => ({
      ...prev,
      values: prev.values.filter((_, i) => i !== index)
    }))
  }

  const handleSave = () => {
    if (!formData.display_name.trim()) {
      alert('Please enter a display name')
      return
    }

    if (formData.values.length === 0) {
      alert('Please add at least one value')
      return
    }

    if (formData.values.some(v => !v.label.trim())) {
      alert('Please fill in all value labels')
      return
    }

    const optionToSave = {
      ...formData,
      name: formData.display_name.toLowerCase().replace(/\s+/g, '_'),
      values: formData.values.map((v, i) => ({ ...v, sort_order: i }))
    }

    onSave(optionToSave)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {option ? 'Edit Variant Option' : 'Add Variant Option'}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Display Name *
            </label>
            <input
              type="text"
              value={formData.display_name}
              onChange={(e) => setFormData(prev => ({ ...prev, display_name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Size, Color, Material"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Option Type
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="radio">Radio Button</option>
              <option value="checkbox">Checkbox</option>
              <option value="text">Text Input</option>
              <option value="number">Number Input</option>
              <option value="textarea">Text Area</option>
            </select>
          </div>

          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Option Values *
              </label>
              <button
                onClick={addValue}
                className="inline-flex items-center px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Value
              </button>
            </div>
            <div className="space-y-2">
              {formData.values.map((value, index) => (
                <div key={value.id} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={value.label}
                    onChange={(e) => updateValue(index, 'label', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder={`Value ${index + 1}`}
                  />
                  <button
                    onClick={() => deleteValue(index)}
                    className="p-2 text-red-600 hover:text-red-800 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Option
          </button>
        </div>
      </div>
    </div>
  )
}

export default ProductVariantModal

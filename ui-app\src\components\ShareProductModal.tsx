import React, { useState, useEffect } from 'react';
import { X, Users, Settings, Calendar, Check, AlertCircle } from 'lucide-react';
import { vendorApi, Vendor } from '../services/vendorApi';
import { productSharingApi, ProductPermissions, ShareProductRequest } from '../services/productSharingApi';

interface ShareProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  productSku: string;
  onShareSuccess: () => void;
}

const ShareProductModal: React.FC<ShareProductModalProps> = ({
  isOpen,
  onClose,
  productId,
  productName,
  productSku,
  onShareSuccess
}) => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [selectedVendors, setSelectedVendors] = useState<string[]>([]);
  const [permissions, setPermissions] = useState<ProductPermissions>({
    view: true,
    edit: false,
    share: false,
    delete: false,
    pricing_access: true,
    inventory_access: true
  });
  const [expiresAt, setExpiresAt] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadVendors();
    }
  }, [isOpen]);

  const loadVendors = async () => {
    try {
      const result = await vendorApi.getVendors({ limit: 100 });
      setVendors(result.vendors.filter(v => v.status === 'active'));
    } catch (error) {
      console.error('Failed to load vendors:', error);
    }
  };

  const handleVendorToggle = (vendorId: string) => {
    setSelectedVendors(prev => 
      prev.includes(vendorId) 
        ? prev.filter(id => id !== vendorId)
        : [...prev, vendorId]
    );
  };

  const handlePermissionChange = (permission: keyof ProductPermissions) => {
    setPermissions(prev => ({
      ...prev,
      [permission]: !prev[permission]
    }));
  };

  const handleShare = async () => {
    if (selectedVendors.length === 0) {
      alert('Please select at least one vendor');
      return;
    }

    setLoading(true);
    try {
      const requests: ShareProductRequest[] = selectedVendors.map(vendorId => ({
        product_id: productId,
        vendor_id: vendorId,
        permissions,
        expires_at: expiresAt || undefined
      }));

      const results = await productSharingApi.bulkShareProducts(requests);
      const successCount = results.filter(r => r.success).length;
      
      if (successCount > 0) {
        alert(`Successfully shared product with ${successCount} vendor(s)`);
        onShareSuccess();
        onClose();
      } else {
        alert('Failed to share product with any vendors');
      }
    } catch (error) {
      console.error('Failed to share product:', error);
      alert('Failed to share product');
    } finally {
      setLoading(false);
    }
  };

  const filteredVendors = vendors.filter(vendor =>
    vendor.organization_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.contact_person.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">Share Product</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Product Info */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h4 className="font-medium text-gray-900 mb-2">Product Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Name:</span>
              <span className="ml-2 font-medium">{productName}</span>
            </div>
            <div>
              <span className="text-gray-500">SKU:</span>
              <span className="ml-2 font-medium">{productSku}</span>
            </div>
          </div>
        </div>

        {/* Vendor Selection */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Select Vendors
          </h4>
          
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="Search vendors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>

          <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md">
            {filteredVendors.map((vendor) => (
              <div
                key={vendor.id}
                className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                  selectedVendors.includes(vendor.id) ? 'bg-blue-50 border-blue-200' : ''
                }`}
                onClick={() => handleVendorToggle(vendor.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedVendors.includes(vendor.id)}
                      onChange={() => handleVendorToggle(vendor.id)}
                      className="mr-3 rounded border-gray-300"
                    />
                    <div>
                      <div className="font-medium text-gray-900">{vendor.organization_name}</div>
                      <div className="text-sm text-gray-500">
                        {vendor.contact_person.name} • {vendor.contact_person.email}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">{vendor.tier}</div>
                    <div className="text-xs text-gray-500">{vendor.commission_rate}% commission</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Permissions */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-4 flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Permissions
          </h4>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={permissions.view}
                  onChange={() => handlePermissionChange('view')}
                  className="mr-2 rounded border-gray-300"
                />
                <span className="text-sm">View Product</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={permissions.edit}
                  onChange={() => handlePermissionChange('edit')}
                  className="mr-2 rounded border-gray-300"
                />
                <span className="text-sm">Edit Product</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={permissions.share}
                  onChange={() => handlePermissionChange('share')}
                  className="mr-2 rounded border-gray-300"
                />
                <span className="text-sm">Share Product</span>
              </label>
            </div>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={permissions.pricing_access}
                  onChange={() => handlePermissionChange('pricing_access')}
                  className="mr-2 rounded border-gray-300"
                />
                <span className="text-sm">Access Pricing</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={permissions.inventory_access}
                  onChange={() => handlePermissionChange('inventory_access')}
                  className="mr-2 rounded border-gray-300"
                />
                <span className="text-sm">Access Inventory</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={permissions.delete}
                  onChange={() => handlePermissionChange('delete')}
                  className="mr-2 rounded border-gray-300"
                />
                <span className="text-sm">Delete Product</span>
              </label>
            </div>
          </div>
        </div>

        {/* Expiration */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-4 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Expiration (Optional)
          </h4>
          
          <input
            type="datetime-local"
            value={expiresAt}
            onChange={(e) => setExpiresAt(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          />
          <p className="text-xs text-gray-500 mt-1">
            Leave empty for no expiration
          </p>
        </div>

        {/* Summary */}
        {selectedVendors.length > 0 && (
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-blue-900 mb-2 flex items-center">
              <Check className="h-4 w-4 mr-2" />
              Summary
            </h4>
            <p className="text-sm text-blue-700">
              Product "{productName}" will be shared with {selectedVendors.length} vendor(s) 
              with the selected permissions.
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            onClick={handleShare}
            disabled={loading || selectedVendors.length === 0}
            className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sharing...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Share Product
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShareProductModal; 
// Order types based on BigCommerce API structure
export interface OrderAddress {
  id?: string
  first_name: string
  last_name: string
  company?: string
  street_1: string
  street_2?: string
  city: string
  state: string
  zip: string
  country: string
  country_iso2: string
  phone?: string
  email?: string
}

export interface OrderProduct {
  id: string
  order_id: string
  product_id: string
  variant_id?: string
  name: string
  sku?: string
  type: 'physical' | 'digital'
  base_price: number
  price_ex_tax: number
  price_inc_tax: number
  price_tax: number
  base_total: number
  total_ex_tax: number
  total_inc_tax: number
  total_tax: number
  weight?: number
  width?: number
  height?: number
  depth?: number
  quantity: number
  base_cost_price?: number
  cost_price_inc_tax?: number
  cost_price_ex_tax?: number
  cost_price_tax?: number
  is_refunded: boolean
  quantity_refunded: number
  refund_amount: number
  return_id?: string
  wrapping_name?: string
  wrapping_message?: string
  wrapping_cost_ex_tax?: number
  wrapping_cost_inc_tax?: number
  wrapping_cost_tax?: number
  product_options?: Array<{
    id: string
    option_id: string
    order_product_id: string
    product_option_id: string
    display_name: string
    display_value: string
    value: string
    type: string
    name: string
  }>
  configurable_fields?: Array<{
    name: string
    value: string
  }>
  parent_order_product_id?: string
  upc?: string
  variant_sku?: string
  base_wrapping_cost?: number
  fixed_shipping_cost?: number
  ebay_item_id?: string
  ebay_transaction_id?: string
  option_set_id?: string
  parent_order_product?: any
  bundle_layout_type?: string
  coupon_amount?: number
  discount_amount?: number
  applied_discounts?: Array<{
    id: string
    amount: number
  }>
  product_image?: {
    url_zoom?: string
    url_standard?: string
    url_thumbnail?: string
    url_tiny?: string
  }
}

export interface OrderShipment {
  id: string
  order_id: string
  customer_id: string
  order_address_id: string
  date_created: string
  tracking_number?: string
  shipping_method?: string
  shipping_provider?: string
  tracking_carrier?: string
  tracking_link?: string
  comments?: string
  billing_address: OrderAddress
  shipping_address: OrderAddress
  items: Array<{
    order_product_id: string
    product_id: string
    quantity: number
  }>
}

export interface OrderCoupon {
  id: string
  coupon_id: string
  order_id: string
  code: string
  amount: number
  type: 'per_item_discount' | 'percentage_discount' | 'per_total_discount' | 'shipping_discount' | 'free_shipping' | 'promotion'
  discount: number
}

export interface OrderTax {
  id: string
  order_id: string
  tax_rate_id: string
  tax_class_id: string
  name: string
  class: string
  rate: number
  priority: number
  priority_amount: number
  line_amount: number
}

export interface Order {
  id: string
  customer_id: string
  date_created: string
  date_modified: string
  date_shipped?: string
  status_id: number
  status: string
  subtotal_ex_tax: number
  subtotal_inc_tax: number
  subtotal_tax: number
  base_shipping_cost: number
  shipping_cost_ex_tax: number
  shipping_cost_inc_tax: number
  shipping_cost_tax: number
  shipping_cost_tax_class_id: number
  base_handling_cost: number
  handling_cost_ex_tax: number
  handling_cost_inc_tax: number
  handling_cost_tax: number
  handling_cost_tax_class_id: number
  base_wrapping_cost: number
  wrapping_cost_ex_tax: number
  wrapping_cost_inc_tax: number
  wrapping_cost_tax: number
  wrapping_cost_tax_class_id: number
  total_ex_tax: number
  total_inc_tax: number
  total_tax: number
  items_total: number
  items_shipped: number
  payment_method: string
  payment_provider_id?: string
  payment_status: string
  refunded_amount: number
  order_is_digital: boolean
  store_credit_amount: number
  gift_certificate_amount: number
  ip_address: string
  ip_address_v6?: string
  geoip_country?: string
  geoip_country_iso2?: string
  currency_id: number
  currency_code: string
  currency_exchange_rate: number
  default_currency_id: number
  default_currency_code: string
  staff_notes?: string
  customer_message?: string
  discount_amount: number
  coupon_discount: number
  shipping_address_count: number
  is_deleted: boolean
  ebay_order_id?: string
  cart_id?: string
  billing_address: OrderAddress
  shipping_addresses: OrderAddress[]
  products: OrderProduct[]
  shipping_cost_tax_class_name?: string
  handling_cost_tax_class_name?: string
  wrapping_cost_tax_class_name?: string
  payment_source?: string
  order_source?: string
  channel_id?: number
  external_source?: string
  external_id?: string
  external_merchant_id?: string
  tax_provider_id?: string
  store_default_currency_code?: string
  store_default_to_transactional_exchange_rate?: number
  custom_status?: string
  customer_locale?: string
  external_order_id?: string
  global_sales_tax_amount?: number
  taxes?: OrderTax[]
  coupons?: OrderCoupon[]
  shipments?: OrderShipment[]
  channel?: string // Channel identifier (bigcommerce, shopify, amazon, etc.)
}

export interface OrderListItem {
  id: string
  customer_id: string
  customer_name?: string
  customer_email?: string
  date_created: string
  date_modified: string
  status: string
  status_id: number
  payment_status: string
  payment_method: string
  total_inc_tax: number
  items_total: number
  currency_code: string
  billing_address: {
    first_name: string
    last_name: string
    email?: string
  }
  shipping_addresses: Array<{
    first_name: string
    last_name: string
    city: string
    state: string
    country: string
  }>
  channel?: string // Channel identifier (bigcommerce, shopify, amazon, etc.)
}

export interface OrderQueryParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  payment_status?: string
  date_range?: {
    start?: string
    end?: string
  }
  customer_id?: string
  channel?: string // Filter by channel (bigcommerce, shopify, amazon, etc.)
  sort_by?: string
  sort_order?: 'ASC' | 'DESC'
}

export interface OrderListResult {
  orders: OrderListItem[]
  total: number
  page: number
  limit: number
  total_pages: number
}

import { useState } from 'react'
import { Plus, Edit, Trash2, Save, X, Tag, Search } from 'lucide-react'
import toast from 'react-hot-toast'

interface OrganizationType {
  id: string
  name: string
  description: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

const OrganizationTypeManagement = () => {
  const [organizationTypes, setOrganizationTypes] = useState<OrganizationType[]>([
    {
      id: '1',
      name: 'Supplier',
      description: 'Organizations that supply products or services',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: 'Vendor',
      description: 'Organizations that sell products or services',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '3',
      name: 'Retailer',
      description: 'Organizations that sell products directly to consumers',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '4',
      name: 'Distributor',
      description: 'Organizations that distribute products to retailers',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '5',
      name: 'B2B',
      description: 'Business-to-business organizations',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editingData, setEditingData] = useState<Partial<OrganizationType>>({})
  const [showAddForm, setShowAddForm] = useState(false)
  const [newType, setNewType] = useState({ name: '', description: '' })

  const filteredTypes = organizationTypes.filter(type =>
    type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    type.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleEdit = (type: OrganizationType) => {
    setEditingId(type.id)
    setEditingData({ name: type.name, description: type.description })
  }

  const handleSave = (id: string) => {
    if (!editingData.name?.trim()) {
      toast.error('Organization type name is required')
      return
    }

    setOrganizationTypes(prev =>
      prev.map(type =>
        type.id === id
          ? {
              ...type,
              name: editingData.name!,
              description: editingData.description || '',
              updatedAt: new Date().toISOString()
            }
          : type
      )
    )
    setEditingId(null)
    setEditingData({})
    toast.success('Organization type updated successfully')
  }

  const handleCancel = () => {
    setEditingId(null)
    setEditingData({})
  }

  const handleDelete = (id: string) => {
    const type = organizationTypes.find(t => t.id === id)
    if (type && window.confirm(`Are you sure you want to delete "${type.name}"?`)) {
      setOrganizationTypes(prev => prev.filter(t => t.id !== id))
      toast.success('Organization type deleted successfully')
    }
  }

  const handleToggleActive = (id: string) => {
    setOrganizationTypes(prev =>
      prev.map(type =>
        type.id === id
          ? { ...type, isActive: !type.isActive, updatedAt: new Date().toISOString() }
          : type
      )
    )
    toast.success('Organization type status updated')
  }

  const handleAdd = () => {
    if (!newType.name.trim()) {
      toast.error('Organization type name is required')
      return
    }

    const newOrganizationType: OrganizationType = {
      id: Date.now().toString(),
      name: newType.name.trim(),
      description: newType.description.trim(),
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    setOrganizationTypes(prev => [...prev, newOrganizationType])
    setNewType({ name: '', description: '' })
    setShowAddForm(false)
    toast.success('Organization type added successfully')
  }

  const handleCancelAdd = () => {
    setNewType({ name: '', description: '' })
    setShowAddForm(false)
  }

  return (
    <div>
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          Manage organization types for guest organizations. These types help categorize and organize external partners.
        </p>
      </div>

      {/* Header with Search and Add Button */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search organization types..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 w-64"
            />
          </div>
        </div>

        {/* Add Button */}
        <button
          onClick={() => setShowAddForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Organization Type
        </button>
      </div>

      {/* Add Form */}
      {showAddForm && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Add New Organization Type</h3>
            <button
              onClick={handleCancelAdd}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label htmlFor="newName" className="block text-sm font-medium text-gray-700 mb-1">
                Name *
              </label>
              <input
                type="text"
                id="newName"
                value={newType.name}
                onChange={(e) => setNewType(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter organization type name"
              />
            </div>
            <div>
              <label htmlFor="newDescription" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <input
                type="text"
                id="newDescription"
                value={newType.description}
                onChange={(e) => setNewType(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter description"
              />
            </div>
          </div>
          <div className="flex items-center justify-end space-x-3 mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={handleCancelAdd}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleAdd}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
            >
              Add Type
            </button>
          </div>
        </div>
      )}

      {/* Organization Types Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Organization Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Updated
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredTypes.map((type) => (
              <tr key={type.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  {editingId === type.id ? (
                    <input
                      type="text"
                      value={editingData.name || ''}
                      onChange={(e) => setEditingData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  ) : (
                    <div className="flex items-center">
                      <Tag className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm font-medium text-gray-900">{type.name}</span>
                    </div>
                  )}
                </td>
                <td className="px-6 py-4">
                  {editingId === type.id ? (
                    <input
                      type="text"
                      value={editingData.description || ''}
                      onChange={(e) => setEditingData(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  ) : (
                    <span className="text-sm text-gray-500">{type.description}</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => handleToggleActive(type.id)}
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      type.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {type.isActive ? 'Active' : 'Inactive'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(type.updatedAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {editingId === type.id ? (
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleSave(type.id)}
                        className="text-green-600 hover:text-green-900"
                      >
                        <Save className="h-4 w-4" />
                      </button>
                      <button
                        onClick={handleCancel}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleEdit(type)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(type.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Empty state */}
      {filteredTypes.length === 0 && (
        <div className="text-center py-12">
          <Tag className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No organization types found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? 'Try adjusting your search terms.' : 'Get started by adding your first organization type.'}
          </p>
        </div>
      )}
    </div>
  )
}

export default OrganizationTypeManagement 
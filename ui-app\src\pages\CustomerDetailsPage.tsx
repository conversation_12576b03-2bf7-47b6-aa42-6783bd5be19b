import { useState, useEffect } from 'react'
import { useParams, useNavigate, useSearchParams } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  ArrowLeft, Edit, MoreHorizontal, Mail, Phone, MapPin, Building, Globe,
  CreditCard, DollarSign, Calendar, Users, TrendingUp, Package, FileText,
  MessageSquare, Clock, CheckCircle, XCircle, AlertCircle, Star, Plus,
  Download, Upload, Copy, ExternalLink, Settings, RefreshCw, Eye,
  Truck, Receipt, Target, Award, Activity, BarChart3, PieChart,
  Filter, Search, ChevronDown, ChevronRight, Bookmark, Tag, Wallet, Banknote,
  Hash, ThumbsUp, Briefcase, UserCheck, Gift
} from 'lucide-react'
import { mockCustomerApi } from '../services/customerApi'
import { Customer, CustomerActivity, CustomerNote, Order } from '../types/customer'

const CustomerDetailsPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'overview')
  const [showNoteForm, setShowNoteForm] = useState(false)
  const [newNote, setNewNote] = useState('')

  // Fetch customer data
  const { data: customer, isLoading } = useQuery(
    ['customer', id],
    () => mockCustomerApi.getCustomer(id!),
    { enabled: !!id }
  )

  // Fetch customer activities
  const { data: activities } = useQuery(
    ['customer-activities', id],
    () => mockCustomerApi.getCustomerActivities(id!),
    { enabled: !!id }
  )

  // Fetch customer notes
  const { data: notes } = useQuery(
    ['customer-notes', id],
    () => mockCustomerApi.getCustomerNotes(id!),
    { enabled: !!id }
  )

  // Fetch orders for this customer
  const { data: ordersData, isLoading: isOrdersLoading } = useQuery(
    ['customer-orders', id],
    () => mockCustomerApi.getOrders({ search: '', status: [], payment_status: [], date_from: undefined, date_to: undefined, amount_min: undefined, amount_max: undefined }, 1, 100),
    { enabled: !!id }
  )

  useEffect(() => {
    setSearchParams({ tab: activeTab })
  }, [activeTab, setSearchParams])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center">
          <RefreshCw className="h-6 w-6 animate-spin text-gray-400 mr-2" />
          Loading customer details...
        </div>
      </div>
    )
  }

  if (!customer) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Customer Not Found</h2>
          <p className="text-gray-600 mb-4">The customer you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/customers')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Customers
          </button>
        </div>
      </div>
    )
  }

  // New merged tab structure
  const tabs = [
    { id: 'overview', label: 'Overview', icon: Eye },
    { id: 'account', label: 'Account Info', icon: FileText },
    { id: 'addresses', label: 'Addresses', icon: MapPin },
    { id: 'orders', label: 'Orders', icon: Package },
    { id: 'financial', label: 'Financial & Payment', icon: DollarSign },
    { id: 'reward', label: 'Reward', icon: Gift },
    { id: 'pricing', label: 'Pricing', icon: DollarSign },
    { id: 'system', label: 'System & Activity', icon: Activity },
    { id: 'documents', label: 'Documents', icon: FileText },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800'
      case 'Inactive': return 'bg-gray-100 text-gray-800'
      case 'Suspended': return 'bg-red-100 text-red-800'
      case 'Pending': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Diamond': return 'bg-purple-100 text-purple-800'
      case 'Platinum': return 'bg-gray-100 text-gray-800'
      case 'Gold': return 'bg-yellow-100 text-yellow-800'
      case 'Silver': return 'bg-blue-100 text-blue-800'
      case 'Bronze': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Helper for order status badge
  const getOrderStatusBadge = (status: string) => {
    const statusConfig: Record<string, { bg: string; text: string; icon: any }> = {
      'Pending': { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: Clock },
      'Processing': { bg: 'bg-blue-100', text: 'text-blue-800', icon: RefreshCw },
      'Shipped': { bg: 'bg-purple-100', text: 'text-purple-800', icon: Truck },
      'Delivered': { bg: 'bg-green-100', text: 'text-green-800', icon: CheckCircle },
      'Cancelled': { bg: 'bg-red-100', text: 'text-red-800', icon: XCircle },
      'Returned': { bg: 'bg-orange-100', text: 'text-orange-800', icon: AlertCircle },
      'Refunded': { bg: 'bg-gray-100', text: 'text-gray-800', icon: RefreshCw },
    }
    const config = statusConfig[status] || statusConfig['Pending']
    const Icon = config.icon
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        <Icon className="h-3 w-3 mr-1" />
        {status}
      </span>
    )
  }

  // Helper for payment status badge
  const getPaymentStatusBadge = (paymentStatus: string) => {
    const statusConfig: Record<string, { bg: string; text: string }> = {
      'Pending': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
      'Paid': { bg: 'bg-green-100', text: 'text-green-800' },
      'Partially Paid': { bg: 'bg-blue-100', text: 'text-blue-800' },
      'Failed': { bg: 'bg-red-100', text: 'text-red-800' },
      'Refunded': { bg: 'bg-gray-100', text: 'text-gray-800' },
    }
    const config = statusConfig[paymentStatus] || statusConfig['Pending']
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {paymentStatus}
      </span>
    )
  }

  // Helper for formatting currency
  const formatOrderCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  // Overview Tab: Key summary fields and metrics
  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Total Orders</p>
            <p className="text-2xl font-semibold text-gray-900 mt-1">{customer.total_orders}</p>
          </div>
          <span className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-blue-100">
            <Users className="h-6 w-6 text-blue-500" />
          </span>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Total Spent</p>
            <p className="text-2xl font-semibold text-gray-900 mt-1">{formatCurrency(customer.total_spent)}</p>
          </div>
          <span className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-purple-100">
            <DollarSign className="h-6 w-6 text-purple-500" />
          </span>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Avg Order Value</p>
            <p className="text-2xl font-semibold text-gray-900 mt-1">{formatCurrency(customer.average_order_value)}</p>
          </div>
          <span className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-orange-100">
            <TrendingUp className="h-6 w-6 text-orange-500" />
          </span>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Lifetime Value</p>
            <p className="text-2xl font-semibold text-gray-900 mt-1">{formatCurrency(customer.lifetime_value)}</p>
          </div>
          <span className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-yellow-100">
            <Star className="h-6 w-6 text-yellow-500" />
          </span>
        </div>
      </div>
      {/* Quick Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-3">
            <Calendar className="h-5 w-5 text-blue-500 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Status & Dates</h3>
          </div>
          <div className="space-y-2 text-sm text-gray-700">
            <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />Status: <span className="font-semibold">{customer.status}</span></div>
            <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-gray-400" />Created: {customer.created_date ? new Date(customer.created_date).toLocaleDateString() : new Date(customer.date_created).toLocaleDateString()}</div>
            <div className="flex items-center gap-2"><Edit className="h-4 w-4 text-gray-400" />Last Modified: {customer.last_modified_date ? new Date(customer.last_modified_date).toLocaleDateString() : ''}</div>
            <div className="flex items-center gap-2"><Activity className="h-4 w-4 text-gray-400" />Last Activity: {customer.last_activity_date ? new Date(customer.last_activity_date).toLocaleDateString() : ''}</div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-3">
            <Users className="h-5 w-5 text-blue-500 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Contact</h3>
          </div>
          <div className="space-y-2 text-sm text-gray-700">
            <div className="flex items-center gap-2"><Mail className="h-4 w-4 text-gray-400" />Email: {customer.email}</div>
            <div className="flex items-center gap-2"><Phone className="h-4 w-4 text-gray-400" />Phone: {customer.phone}</div>
            <div className="flex items-center gap-2"><Globe className="h-4 w-4 text-gray-400" />Website: {customer.website}</div>
            <div className="flex items-center gap-2"><Users className="h-4 w-4 text-gray-400" />Owner: {customer.account_owner_name}</div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-3">
            <DollarSign className="h-5 w-5 text-green-500 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Financial</h3>
          </div>
          <div className="space-y-2 text-sm text-gray-700">
            <div className="flex items-center gap-2"><DollarSign className="h-4 w-4 text-gray-400" />Annual Revenue: {formatCurrency(customer.annual_revenue)}</div>
            <div className="flex items-center gap-2"><CreditCard className="h-4 w-4 text-gray-400" />Credit Limit: {formatCurrency(customer.credit_limit)}</div>
            <div className="flex items-center gap-2"><CreditCard className="h-4 w-4 text-gray-400" />Credit Used: {formatCurrency(customer.credit_used)}</div>
            <div className="flex items-center gap-2"><AlertCircle className="h-4 w-4 text-gray-400" />Outstanding Balance: {typeof customer.outstanding_balance === 'number' ? formatCurrency(customer.outstanding_balance) : '-'}</div>
          </div>
        </div>
      </div>
    </div>
  )

  // Orders Tab: Table view matching screenshot
  const renderOrdersTab = () => {
    const customerOrders = ordersData?.orders.filter((order: Order) => order.customer_id === customer.id) || []
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-0 overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Order ID</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Customer</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Date</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Payment</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Method</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Total</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Items</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Ship To</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Channel</th>
              <th className="px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-100">
            {isOrdersLoading ? (
              Array.from({ length: 3 }).map((_, i) => (
                <tr key={i}>
                  {Array.from({ length: 11 }).map((_, j) => (
                    <td key={j} className="px-4 py-4">
                      <div className="h-4 bg-gray-100 rounded w-3/4 animate-pulse"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : customerOrders.length === 0 ? (
              <tr>
                <td colSpan={11} className="px-4 py-12 text-center">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                  <p className="text-gray-600">This customer has not placed any orders yet.</p>
                </td>
              </tr>
            ) : (
              customerOrders.map((order: Order) => (
                <tr key={order.id} className="hover:bg-gray-50 transition-colors">
                  {/* Order ID */}
                  <td className="px-4 py-4 text-sm font-semibold text-blue-700 cursor-pointer hover:underline whitespace-nowrap">
                    <a href={`/orders/${order.id}`} target="_blank" rel="noopener noreferrer">{order.order_number}</a>
                  </td>
                  {/* Customer */}
                  <td className="px-4 py-4 min-w-[160px]">
                    <div className="font-medium text-gray-900">{order.customer_name}</div>
                    <div className="text-xs text-gray-500">{customer.email}</div>
                  </td>
                  {/* Date */}
                  <td className="px-4 py-4 text-xs text-gray-900 whitespace-nowrap">
                    {order.order_date ? new Date(order.order_date).toLocaleString('en-US', { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' }) : '--'}
                  </td>
                  {/* Status */}
                  <td className="px-4 py-4">
                    {getOrderStatusBadge(order.status)}
                  </td>
                  {/* Payment */}
                  <td className="px-4 py-4">
                    {getPaymentStatusBadge(order.payment_status)}
                  </td>
                  {/* Method */}
                  <td className="px-4 py-4 text-xs text-gray-900">
                    <div className="flex items-center">
                      <CreditCard className="h-4 w-4 mr-1 text-gray-400" />
                      {order.payment_method}
                    </div>
                  </td>
                  {/* Total */}
                  <td className="px-4 py-4 text-sm font-bold text-gray-900 whitespace-nowrap">
                    {formatOrderCurrency(order.total_amount, order.currency)}
                  </td>
                  {/* Items */}
                  <td className="px-4 py-4 text-center text-sm text-gray-900">
                    {order.items.length}
                  </td>
                  {/* Ship To */}
                  <td className="px-4 py-4 text-xs text-gray-900 whitespace-nowrap">
                    {order.shipping_address.city}, {order.shipping_address.state}
                  </td>
                  {/* Channel */}
                  <td className="px-4 py-4 text-xs text-gray-900 whitespace-nowrap">
                    <span className="inline-flex items-center gap-1">
                      <Package className="h-4 w-4 text-gray-400" />
                      <span>Web</span>
                    </span>
                  </td>
                  {/* Actions */}
                  <td className="px-4 py-4 text-center">
                    <div className="flex items-center gap-2 justify-center">
                      <a href={`/orders/${order.id}`} target="_blank" rel="noopener noreferrer" className="text-gray-500 hover:text-blue-600">
                        <Eye className="h-5 w-5" />
                      </a>
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreHorizontal className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    )
  }

  // Account Info Tab: General, owner/manager, BC, form fields
  const renderAccountInfoTab = () => (
    <div className="space-y-6">
      {/* General Info */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <FileText className="h-5 w-5 text-blue-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">General Information</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><FileText className="h-4 w-4 text-gray-400" />Account Name: {customer.name}</div>
          <div className="flex items-center gap-2"><Hash className="h-4 w-4 text-gray-400" />Account Number: {customer.account_number || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Tag className="h-4 w-4 text-gray-400" />Account Type: {customer.type || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />Status: {customer.status || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Star className="h-4 w-4 text-gray-400" />Tier: {customer.tier || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><AlertCircle className="h-4 w-4 text-gray-400" />Customer Priority: {customer.customer_priority || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><ThumbsUp className="h-4 w-4 text-gray-400" />Rating: {customer.rating || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Briefcase className="h-4 w-4 text-gray-400" />Industry: {customer.industry || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Globe className="h-4 w-4 text-gray-400" />Website: {customer.website || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Hash className="h-4 w-4 text-gray-400" />Federal Tax ID: {customer.federal_tax_identification || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Search className="h-4 w-4 text-gray-400" />How did you hear about us?: {customer.how_did_you_hear_about_us || <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* Owner/Manager Info */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <Users className="h-5 w-5 text-green-600 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">Owner & Manager</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><Users className="h-4 w-4 text-gray-400" />Account Owner: {customer.account_owner_name || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Mail className="h-4 w-4 text-gray-400" />Account Owner Email: {customer.account_owner_email || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><UserCheck className="h-4 w-4 text-gray-400" />Account Manager: {customer.assigned_rep || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Mail className="h-4 w-4 text-gray-400" />Account Manager Email: {customer.account_manager_email || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Phone className="h-4 w-4 text-gray-400" />Account Manager Mobile: {customer.account_manager_mobile || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Briefcase className="h-4 w-4 text-gray-400" />Account Manager Division: {customer.account_manager_division || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-gray-400" />Account Manager Assigned Date/Time: {customer.account_manager_assigned_date_time ? new Date(customer.account_manager_assigned_date_time).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* BC Info */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <Globe className="h-5 w-5 text-indigo-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">BC Information</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><Users className="h-4 w-4 text-gray-400" />BC Customer Group: {customer.bc_customer_group || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Tag className="h-4 w-4 text-gray-400" />BC Group Name: {customer.bc_group_name || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Mail className="h-4 w-4 text-gray-400" />BC Email: {customer.bc_email || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><DollarSign className="h-4 w-4 text-gray-400" />BC Store Credit: {typeof customer.bc_store_credit === 'number' ? formatCurrency(customer.bc_store_credit) : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Hash className="h-4 w-4 text-gray-400" />BC CustomerId: {customer.bc_customerid || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Clock className="h-4 w-4 text-gray-400" />BC Modified Time: {customer.bc_modified_time ? new Date(customer.bc_modified_time).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Phone className="h-4 w-4 text-gray-400" />Cell/Mobile Number: {customer.cell_mobile_number || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><MessageSquare className="h-4 w-4 text-gray-400" />SMS Consent: {typeof customer.sms_consent === 'boolean' ? (customer.sms_consent ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* Form Fields */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <FileText className="h-5 w-5 text-gray-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">Form Fields</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />Read & Agree Midwest Terms: {typeof customer.read_agree_midwest_terms === 'boolean' ? (customer.read_agree_midwest_terms ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Briefcase className="h-4 w-4 text-gray-400" />Are you a Business: {typeof customer.are_you_a_business === 'boolean' ? (customer.are_you_a_business ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* Custom/Other Flags */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <Settings className="h-5 w-5 text-gray-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">Custom & Other Fields</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2">Abuser: {typeof customer.abuser === 'boolean' ? (customer.abuser ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2">Is Partner: {typeof customer.is_partner === 'boolean' ? (customer.is_partner ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2">Is Vendor: {typeof customer.is_vendor === 'boolean' ? (customer.is_vendor ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2">Is Customer Portal: {typeof customer.is_customer_portal === 'boolean' ? (customer.is_customer_portal ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2">Is Prospect: {typeof customer.is_prospect === 'boolean' ? (customer.is_prospect ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2">Is Deleted: {typeof customer.is_deleted === 'boolean' ? (customer.is_deleted ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
    </div>
  );

  // Financial & Payment Tab: Payment, credit, cash & carry, tax, etc.
  const renderFinancialTab = () => (
    <div className="space-y-6">
      {/* Payment & Credit */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <DollarSign className="h-5 w-5 text-blue-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">Payment & Credit</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><FileText className="h-4 w-4 text-gray-400" />Payment Term: {customer.payment_term || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><DollarSign className="h-4 w-4 text-gray-400" />Credit Limit: {formatCurrency(customer.credit_limit)}</div>
          <div className="flex items-center gap-2"><AlertCircle className="h-4 w-4 text-gray-400" />Unpaid Balance: {typeof customer.unpaid_balance === 'number' ? formatCurrency(customer.unpaid_balance) : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><AlertCircle className="h-4 w-4 text-gray-400" />Outstanding Balance: {typeof customer.outstanding_balance === 'number' ? formatCurrency(customer.outstanding_balance) : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><UserCheck className="h-4 w-4 text-gray-400" />Payment Term Approved By: {customer.payment_term_approved_by || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-gray-400" />Payment Term Approved Date & Time: {customer.payment_term_approved_date_time ? new Date(customer.payment_term_approved_date_time).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><FileText className="h-4 w-4 text-gray-400" />Payment Term Notes: {customer.payment_term_notes || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><AlertCircle className="h-4 w-4 text-gray-400" />Payment Term Issues: {customer.payment_term_issues || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><AlertCircle className="h-4 w-4 text-gray-400" />Payment Term Risk: {customer.payment_term_risk || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />Tax Exempt: {customer.tax_exempt ? 'Yes' : 'No'}</div>
          <div className="flex items-center gap-2"><DollarSign className="h-4 w-4 text-gray-400" />Currency: {customer.annual_revenue_currency || <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* Cash & Carry */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <Wallet className="h-5 w-5 text-yellow-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">Cash & Carry</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />Cash & Carry Check Approved? {customer.cash_and_carry_check_approved ? 'Yes' : 'No'}</div>
          <div className="flex items-center gap-2"><UserCheck className="h-4 w-4 text-gray-400" />Check Approved By: {customer.check_approved_by || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-gray-400" />Check Approved Date & Time: {customer.check_approved_date_time ? new Date(customer.check_approved_date_time).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><DollarSign className="h-4 w-4 text-gray-400" />Check Approved Amount Limit: {typeof customer.check_approved_amount_limit === 'number' ? formatCurrency(customer.check_approved_amount_limit) : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><FileText className="h-4 w-4 text-gray-400" />Noted for Check/CNC: {customer.noted_for_check_cnc || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />Does The Customer have account w/CTOS? {typeof customer.does_customer_have_account_with_ctos === 'boolean' ? (customer.does_customer_have_account_with_ctos ? 'Yes' : 'No') : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* Credit Card */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <CreditCard className="h-5 w-5 text-indigo-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">Credit Card</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />Customer Credit Card Fees Exempt {customer.customer_credit_card_fees_exempt ? 'Yes' : 'No'}</div>
          <div className="flex items-center gap-2"><UserCheck className="h-4 w-4 text-gray-400" />Credit Card Fees Exempt Approved By: {customer.credit_card_fees_exempt_approved_by || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-gray-400" />Credit Card Fees Exempt Approved Date: {customer.credit_card_fees_exempt_approved_date ? new Date(customer.credit_card_fees_exempt_approved_date).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* ACH */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <Banknote className="h-5 w-5 text-green-600 mr-2" />
          <h4 className="text-lg font-medium text-green-900">ACH</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />ACH/E-Check Approved: {customer.ach_echeck_approved ? 'Yes' : 'No'}</div>
          <div className="flex items-center gap-2"><UserCheck className="h-4 w-4 text-gray-400" />ACH/E-Check Approved By: {customer.ach_echeck_approved_by || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-gray-400" />ACH/E-Checked Approved Date & Time: {customer.ach_echecked_approved_date_time ? new Date(customer.ach_echecked_approved_date_time).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />ACH Form Received & Uploaded: {customer.ach_form_received_uploaded ? 'Yes' : 'No'}</div>
          <div className="flex items-center gap-2"><UserCheck className="h-4 w-4 text-gray-400" />ACH Form Uploaded By: {customer.ach_form_uploaded_by || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-gray-400" />ACH Form Received Uploaded Date: {customer.ach_form_received_uploaded_date ? new Date(customer.ach_form_received_uploaded_date).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* Net Terms */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <FileText className="h-5 w-5 text-purple-600 mr-2" />
          <h4 className="text-lg font-medium text-purple-900">Net Terms</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-2 text-sm text-gray-700">
          <div className="flex items-center gap-2"><CheckCircle className="h-4 w-4 text-gray-400" />Net Terms Received and Uploaded: {customer.net_terms_received_and_uploaded ? 'Yes' : 'No'}</div>
          <div className="flex items-center gap-2"><UserCheck className="h-4 w-4 text-gray-400" />Net Term Agreement Uploaded by: {customer.net_term_agreement_uploaded_by || <span className="text-gray-400">Not Set</span>}</div>
          <div className="flex items-center gap-2"><Calendar className="h-4 w-4 text-gray-400" />Net Terms Uploaded Date/Time: {customer.net_terms_uploaded_date_time ? new Date(customer.net_terms_uploaded_date_time).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
    </div>
  );

  // System & Activity Tab: System info, activity, notes, custom/other
  const renderSystemActivityTab = () => (
    <div className="space-y-6">
      {/* System Info */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <Activity className="h-5 w-5 text-blue-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">System Information</h4>
        </div>
        <div className="space-y-2 text-sm text-gray-700">
          <div>Attempt1 date capture: {customer.attempt1_date_capture ? new Date(customer.attempt1_date_capture).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
          <div>Created By: {customer.created_by || <span className="text-gray-400">Not Set</span>}</div>
          <div>Last Modified By: {customer.last_modified_by || <span className="text-gray-400">Not Set</span>}</div>
          <div>User Who last modified Notes: {customer.user_who_last_modified_notes || <span className="text-gray-400">Not Set</span>}</div>
          <div>Notes Last Modified Date/Time: {customer.notes_last_modified_date_time ? new Date(customer.notes_last_modified_date_time).toLocaleString() : <span className="text-gray-400">Not Set</span>}</div>
        </div>
      </div>
      {/* Activity & Notes */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center mb-4">
          <MessageSquare className="h-5 w-5 text-indigo-500 mr-2" />
          <h4 className="text-lg font-medium text-gray-900">Activity & Notes</h4>
        </div>
        {renderActivityTabNew()}
        {/* Customer Notes Section */}
        <div className="mt-8">
          <h4 className="text-md font-semibold text-gray-900 mb-2">Customer Notes</h4>
          {notes && notes.length > 0 ? (
            <ul className="divide-y divide-gray-200">
              {notes.map((note: CustomerNote) => (
                <li key={note.id} className="py-2">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-gray-800">{note.created_by}</span>
                    <span className="text-xs text-gray-400">{new Date(note.created_at).toLocaleString()}</span>
                  </div>
                  <p className="text-sm text-gray-700">{note.content}</p>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-sm text-gray-500 italic">No notes available.</p>
          )}
        </div>
      </div>
    </div>
  );

  // Addresses Tab: Add icons to headings
  const renderAddressesTabNew = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Billing Address */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center mb-2">
          <span className="inline-flex items-center justify-center h-7 w-7 rounded-full bg-blue-100 mr-2">
            <MapPin className="h-4 w-4 text-blue-500" />
          </span>
          <h3 className="text-lg font-medium text-gray-900">Billing Address</h3>
        </div>
        <div className="space-y-2 text-sm text-gray-700">
          <div>Name: {customer.billing_address.name}</div>
          <div>Company: {customer.billing_address.company}</div>
          <div>Address Line 1: {customer.billing_address.address_line_1}</div>
          {customer.billing_address.address_line_2 && <div>Address Line 2: {customer.billing_address.address_line_2}</div>}
          <div>City: {customer.billing_address.city}</div>
          <div>State: {customer.billing_address.state}</div>
          <div>Postal Code: {customer.billing_address.postal_code}</div>
          <div>Country: {customer.billing_address.country}</div>
        </div>
      </div>
      {/* Shipping Addresses */}
      {customer.shipping_addresses.map((address, index) => (
        <div key={address.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-2">
            <span className="inline-flex items-center justify-center h-7 w-7 rounded-full bg-green-100 mr-2">
              <MapPin className="h-4 w-4 text-green-500" />
            </span>
            <h3 className="text-lg font-medium text-gray-900">Shipping Address {index + 1}</h3>
          </div>
          <div className="space-y-2 text-sm text-gray-700">
            <div>Name: {address.name}</div>
            <div>Company: {address.company}</div>
            <div>Address Line 1: {address.address_line_1}</div>
            {address.address_line_2 && <div>Address Line 2: {address.address_line_2}</div>}
            <div>City: {address.city}</div>
            <div>State: {address.state}</div>
            <div>Postal Code: {address.postal_code}</div>
            <div>Country: {address.country}</div>
            {address.delivery_instructions && <div>Delivery Instructions: {address.delivery_instructions}</div>}
          </div>
        </div>
      ))}
    </div>
  )

  // Activity Tab
  const renderActivityTabNew = () => (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Activity Timeline</h3>
      </div>
      <div className="p-6">
        <div className="flow-root">
          <ul className="-mb-8">
            {activities?.map((activity, index) => (
              <li key={activity.id}>
                <div className="relative pb-8">
                  {index !== activities.length - 1 && (
                    <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                  )}
                  <div className="relative flex space-x-3">
                    <div>
                      <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                        activity.type === 'order_placed' ? 'bg-green-500' :
                        activity.type === 'payment_received' ? 'bg-blue-500' :
                        activity.type === 'contact_made' ? 'bg-yellow-500' :
                        activity.type === 'quote_sent' ? 'bg-purple-500' :
                        'bg-gray-500'
                      }`}>
                        {activity.type === 'order_placed' && <Package className="h-4 w-4 text-white" />}
                        {activity.type === 'payment_received' && <DollarSign className="h-4 w-4 text-white" />}
                        {activity.type === 'contact_made' && <Phone className="h-4 w-4 text-white" />}
                        {activity.type === 'quote_sent' && <FileText className="h-4 w-4 text-white" />}
                        {!['order_placed', 'payment_received', 'contact_made', 'quote_sent'].includes(activity.type) && (
                          <Activity className="h-4 w-4 text-white" />
                        )}
                      </span>
                    </div>
                    <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p className="text-sm text-gray-900">{activity.description}</p>
                        <p className="text-xs text-gray-500">by {activity.user}</p>
                      </div>
                      <div className="text-right text-sm whitespace-nowrap text-gray-500">
                        <time dateTime={activity.date}>
                          {new Date(activity.date).toLocaleDateString()} at {new Date(activity.date).toLocaleTimeString()}
                        </time>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
        {/* Support Tickets Section */}
        {customer.support_tickets && customer.support_tickets.length > 0 && (
          <div className="mt-8">
            <h4 className="text-md font-semibold text-gray-900 mb-2">Support Tickets</h4>
            <ul className="divide-y divide-gray-200">
              {customer.support_tickets.map((ticket) => (
                <li key={ticket.id} className="py-2 flex justify-between items-center">
                  <div>
                    <span className="font-medium text-gray-800">{ticket.subject}</span>
                    <span className="ml-2 text-xs text-gray-500">({ticket.status})</span>
                  </div>
                  <span className="text-xs text-gray-400">{new Date(ticket.created_at).toLocaleDateString()}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
        {/* Feedback Surveys Section */}
        {customer.feedback_surveys && customer.feedback_surveys.length > 0 && (
          <div className="mt-8">
            <h4 className="text-md font-semibold text-gray-900 mb-2">Feedback Surveys</h4>
            <ul className="divide-y divide-gray-200">
              {customer.feedback_surveys.map((survey) => (
                <li key={survey.id} className="py-2 flex flex-col">
                  <span className="font-medium text-gray-800">Score: {survey.score}</span>
                  {survey.comment && <span className="text-xs text-gray-500">"{survey.comment}"</span>}
                  <span className="text-xs text-gray-400">{new Date(survey.date).toLocaleDateString()}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/customers')}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div className="flex items-center">
                {/* Customer Logo/Avatar */}
                {customer.logo_url ? (
                  <img
                    src={customer.logo_url}
                    alt="Customer Logo"
                    className="h-12 w-12 rounded-full object-cover mr-4 border border-gray-200"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                    <Building className="h-6 w-6 text-gray-500" />
                  </div>
                )}
                <div>
                  <h1 className="text-2xl font-semibold text-gray-900">{customer.name}</h1>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(customer.status)}`}>
                      {customer.status}
                    </span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTierColor(customer.tier)}`}>
                      {customer.tier}
                    </span>
                    {/* Lifecycle Stage */}
                    {customer.lifecycle_stage && (
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-indigo-100 text-indigo-800">
                        {customer.lifecycle_stage}
                      </span>
                    )}
                    <span className="text-sm text-gray-500">ID: {customer.id}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="p-2 text-blue-500 hover:text-blue-700" title="Sync">
                <RefreshCw className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'account' && renderAccountInfoTab()}
        {activeTab === 'addresses' && renderAddressesTabNew()}
        {activeTab === 'orders' && renderOrdersTab()}
        {activeTab === 'financial' && renderFinancialTab()}
        {activeTab === 'reward' && (<div className="bg-white rounded-lg border border-gray-200 p-6">Reward tab coming soon.</div>)}
        {activeTab === 'pricing' && (<div className="bg-white rounded-lg border border-gray-200 p-6">Pricing tab coming soon.</div>)}
        {activeTab === 'system' && renderSystemActivityTab()}
        {activeTab === 'documents' && (<div className="bg-white rounded-lg border border-gray-200 p-6">Documents tab coming soon.</div>)}
      </div>
    </div>
  )
}

export default CustomerDetailsPage

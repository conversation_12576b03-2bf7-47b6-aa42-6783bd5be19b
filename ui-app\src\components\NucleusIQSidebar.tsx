import React, { useState, useRef, useEffect } from 'react';
import { 
  Bot, 
  Plus, 
  ChevronDown, 
  ChevronRight, 
  MoreVertical,
  Edit3,
  Trash2,
  Folder,
  FolderPlus,
  Search,
  X,
  Palette
} from 'lucide-react';
import { useChat } from '../contexts/ChatContext';
import ColorPicker from './ColorPicker';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

interface FolderItemProps {
  folder: {
    id: string;
    name: string;
    color?: string;
    createdAt: Date;
    updatedAt: Date;
  };
  conversations: any[];
  isExpanded: boolean;
  onToggle: () => void;
  onRename: (name: string) => void;
  onDelete: () => void;
  onColorChange: (color: string) => void;
  onConversationSelect: (conversationId: string) => void;
  onConversationDelete: (conversationId: string) => void;
  onConversationRename: (conversationId: string, name: string) => void;
  currentConversationId?: string;
  formatDate: (date: Date) => string;
  moveConversationToFolder: (conversationId: string, folderId?: string) => void;
  isHighlighted?: boolean;
}

interface ConversationItemProps {
  conversation: any;
  isActive: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onRename: (name: string) => void;
  folders: any[];
  moveConversationToFolder: (conversationId: string, folderId?: string) => void;
  formatDate: (date: Date) => string;
}

const FolderItem: React.FC<FolderItemProps> = ({
  folder,
  conversations,
  isExpanded,
  onToggle,
  onRename,
  onDelete,
  onColorChange,
  onConversationSelect,
  onConversationDelete,
  onConversationRename,
  currentConversationId,
  formatDate,
  moveConversationToFolder,
  isHighlighted = false
}) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [renameValue, setRenameValue] = useState(folder.name);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const contextMenuRef = useRef<HTMLDivElement>(null);

  const handleRename = () => {
    if (renameValue.trim() && renameValue !== folder.name) {
      onRename(renameValue.trim());
    }
    setIsRenaming(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setIsRenaming(false);
      setRenameValue(folder.name);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setShowContextMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const folderColor = folder.color || '#3B82F6';
  const conversationCount = conversations.length;

  return (
    <div className={`group ${isHighlighted ? 'bg-blue-50 border-l-4 border-blue-500' : ''}`}>
      {/* Folder Header */}
      <div className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          {/* Animated Chevron */}
          <button
            onClick={onToggle}
            className="p-1 hover:bg-gray-200 rounded transition-colors duration-200"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-500 transition-transform duration-200" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500 transition-transform duration-200" />
            )}
          </button>

          {/* Folder Icon with Color */}
          <div 
            className="p-1.5 rounded-md transition-all duration-200 hover:scale-110"
            style={{ 
              backgroundColor: `${folderColor}20`,
              border: `1px solid ${folderColor}40`
            }}
          >
            <Folder 
              className="h-4 w-4" 
              style={{ color: folderColor }}
            />
          </div>

          {/* Folder Name */}
          <div className="flex-1 min-w-0">
            {isRenaming ? (
              <input
                type="text"
                value={renameValue}
                onChange={(e) => setRenameValue(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleRename}
                className="w-full text-sm bg-white border border-blue-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
            ) : (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900 truncate">
                  {folder.name}
                </span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                  {conversationCount}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Context Menu Button */}
        <div className="relative" ref={contextMenuRef}>
          <button
            onClick={() => setShowContextMenu(!showContextMenu)}
            className="p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-all duration-200"
          >
            <MoreVertical className="h-4 w-4 text-gray-500" />
          </button>

          {/* Context Menu */}
          {showContextMenu && (
            <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 py-1">
              <button
                onClick={() => {
                  setShowColorPicker(true);
                  setShowContextMenu(false);
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                <Palette className="h-4 w-4" />
                <span>Change Color</span>
              </button>
              <button
                onClick={() => {
                  setIsRenaming(true);
                  setShowContextMenu(false);
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                <Edit3 className="h-4 w-4" />
                <span>Rename</span>
              </button>
              <button
                onClick={() => {
                  onDelete();
                  setShowContextMenu(false);
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
                <span>Delete</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Color Picker Modal */}
      {showColorPicker && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Change Folder Color</h3>
              <button
                onClick={() => setShowColorPicker(false)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <ColorPicker
              value={folderColor}
              onChange={(color) => {
                onColorChange(color);
                setShowColorPicker(false);
              }}
            />
          </div>
        </div>
      )}

      {/* Animated Conversations List */}
      <div 
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="pl-8 space-y-1">
          {conversations.map((conversation) => (
            <ConversationItem
              key={conversation.id}
              conversation={conversation}
              isActive={currentConversationId === conversation.id}
              onSelect={() => onConversationSelect(conversation.id)}
              onDelete={() => onConversationDelete(conversation.id)}
              onRename={(name) => onConversationRename(conversation.id, name)}
              folders={[]} // Not needed for conversations inside folders
              moveConversationToFolder={moveConversationToFolder}
              formatDate={formatDate}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  isActive,
  onSelect,
  onDelete,
  onRename,
  folders,
  moveConversationToFolder,
  formatDate
}) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [renameValue, setRenameValue] = useState(conversation.name);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const contextMenuRef = useRef<HTMLDivElement>(null);

  const handleRename = () => {
    if (renameValue.trim() && renameValue !== conversation.name) {
      onRename(renameValue.trim());
    }
    setIsRenaming(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setIsRenaming(false);
      setRenameValue(conversation.name);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setShowContextMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`group ${isActive ? 'bg-blue-100 border-l-2 border-blue-500' : 'hover:bg-gray-50'}`}>
      <div className="flex items-center justify-between px-3 py-2 rounded-lg transition-colors duration-200">
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <div className="w-2 h-2 rounded-full bg-gray-400 flex-shrink-0" />
          
          <div className="flex-1 min-w-0">
            {isRenaming ? (
              <input
                type="text"
                value={renameValue}
                onChange={(e) => setRenameValue(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleRename}
                className="w-full text-sm bg-white border border-blue-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
            ) : (
              <button
                onClick={onSelect}
                className="w-full text-left text-sm text-gray-700 hover:text-gray-900 truncate"
              >
                {conversation.name}
              </button>
            )}
          </div>
        </div>

        {/* Context Menu */}
        <div className="relative" ref={contextMenuRef}>
          <button
            onClick={() => setShowContextMenu(!showContextMenu)}
            className="p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-all duration-200"
          >
            <MoreVertical className="h-4 w-4 text-gray-500" />
          </button>

          {showContextMenu && (
            <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 py-1">
              <button
                onClick={() => {
                  setIsRenaming(true);
                  setShowContextMenu(false);
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                <Edit3 className="h-4 w-4" />
                <span>Rename</span>
              </button>
              <button
                onClick={() => {
                  onDelete();
                  setShowContextMenu(false);
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
                <span>Delete</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const NucleusIQSidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const { 
    conversations,
    folders,
    currentConversation,
    switchConversation,
    createNewConversation,
    deleteConversation,
    renameConversation,
    createFolder,
    renameFolder,
    deleteFolder,
    moveConversationToFolder,
  } = useChat();

  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [highlightedFolder, setHighlightedFolder] = useState<string | null>(null);

  // Group conversations by folders
  const conversationsByFolder = React.useMemo(() => {
    const grouped: Record<string, typeof conversations> = {};
    conversations.forEach(conv => {
      const key = conv.folderId || 'root';
      if (!grouped[key]) grouped[key] = [];
      grouped[key].push(conv);
    });
    return grouped;
  }, [conversations]);

  const rootConversations = conversationsByFolder['root'] || [];

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return `${days} days ago`;
    return date.toLocaleDateString();
  };

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      createFolder(newFolderName.trim());
      setNewFolderName('');
    }
    setIsCreatingFolder(false);
  };

  const handleFolderColorChange = (folderId: string, color: string) => {
    // This would need to be implemented in the ChatContext
    // For now, we'll just log it
    console.log('Change folder color:', folderId, color);
  };

  const toggleFolderExpansion = (folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId]
    }));
  };

  return (
    <div className={`${isOpen ? 'w-80' : 'w-0'} transition-all duration-300 bg-white border-r border-gray-100 shadow-xl flex flex-col rounded-l-2xl overflow-hidden`} style={{ minWidth: isOpen ? 320 : 0 }}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg">
              <Bot className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">NucleusIQ</h1>
              <p className="text-sm text-gray-600">Your AI Assistant</p>
            </div>
          </div>
          <button
            onClick={onToggle}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200"
          >
            <ChevronDown className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="p-4 border-b border-gray-200">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
          />
        </div>
      </div>

      {/* New Chat Button */}
      <div className="p-4">
        <button
          onClick={() => createNewConversation()}
          className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          <Plus className="h-4 w-4" />
          <span>New Chat</span>
        </button>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 space-y-4">
          {/* Folders */}
          {folders.map((folder) => (
            <FolderItem
              key={folder.id}
              folder={folder}
              conversations={conversationsByFolder[folder.id] || []}
              isExpanded={expandedFolders[folder.id] || false}
              onToggle={() => toggleFolderExpansion(folder.id)}
              onRename={(name) => renameFolder(folder.id, name)}
              onDelete={() => deleteFolder(folder.id)}
              onColorChange={(color) => handleFolderColorChange(folder.id, color)}
              onConversationSelect={switchConversation}
              onConversationDelete={deleteConversation}
              onConversationRename={renameConversation}
              currentConversationId={currentConversation?.id}
              formatDate={formatDate}
              moveConversationToFolder={moveConversationToFolder}
              isHighlighted={highlightedFolder === folder.id}
            />
          ))}

          {/* Root Conversations */}
          {rootConversations.length > 0 && (
            <div className="space-y-1">
              {rootConversations.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  isActive={currentConversation?.id === conversation.id}
                  onSelect={() => switchConversation(conversation.id)}
                  onDelete={() => deleteConversation(conversation.id)}
                  onRename={(name) => renameConversation(conversation.id, name)}
                  folders={folders}
                  moveConversationToFolder={moveConversationToFolder}
                  formatDate={formatDate}
                />
              ))}
            </div>
          )}

          {/* Create Folder Button */}
          {isCreatingFolder ? (
            <div className="p-3 border border-gray-200 rounded-lg bg-gray-50">
              <input
                type="text"
                placeholder="Folder name..."
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleCreateFolder();
                  } else if (e.key === 'Escape') {
                    setIsCreatingFolder(false);
                    setNewFolderName('');
                  }
                }}
                className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
              <div className="flex space-x-2 mt-2">
                <button
                  onClick={handleCreateFolder}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  Create
                </button>
                <button
                  onClick={() => {
                    setIsCreatingFolder(false);
                    setNewFolderName('');
                  }}
                  className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setIsCreatingFolder(true)}
              className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-all duration-200"
            >
              <FolderPlus className="h-4 w-4" />
              <span>Create Folder</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NucleusIQSidebar; 
export interface MediaItem {
  id: string;
  name: string;
  item_type: 'file' | 'folder';
  parent_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  size?: number;
  mime_type?: string;
  thumbnail_url?: string;
  download_url?: string;
  is_shared: boolean;
  shared_with?: string[];
  permissions?: MediaPermission[];
  metadata?: any;
  children?: MediaItem[];
}

export interface MediaPermission {
  user_id: string;
  permission: 'read' | 'write' | 'admin';
}

export interface CreateMediaItemRequest {
  name: string;
  item_type: 'file' | 'folder';
  parent_id?: string;
  is_shared?: boolean;
  shared_with?: string[];
  permissions?: MediaPermission[];
}

export interface UpdateMediaItemRequest {
  name?: string;
  parent_id?: string;
  is_shared?: boolean;
  shared_with?: string[];
  permissions?: MediaPermission[];
}

export interface MediaSearchRequest {
  query?: string;
  item_type?: 'file' | 'folder';
  parent_id?: string;
  mime_type?: string;
  is_shared?: boolean;
  created_by?: string;
  limit?: number;
  offset?: number;
}

export interface MediaShareRequest {
  user_ids: string[];
  permissions: ('read' | 'write' | 'admin')[];
}

export interface MediaUploadResponse {
  id: string;
  name: string;
  size: number;
  mime_type: string;
  upload_url: string;
  download_url: string;
  thumbnail_url?: string;
} 
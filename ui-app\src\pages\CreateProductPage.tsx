import { useState, useRef, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useMutation, useQuery } from 'react-query'
import {
  ArrowLeft,
  Save,
  Package,
  DollarSign,
  Truck,
  Eye,
  Search,
  Image as LucideImage,
  Settings,
  BarChart3,
  Star,
  Palette,
  Bold,
  Italic,
  Underline as UnderlineIcon,
  List,
  ListOrdered,
  Quote,
  Code,
  Heading,
  Link as LinkIcon,
  Image as ImageIcon,
  Table as TableIcon,
  Undo2,
  Redo2,
  Minus,
  Type,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Plus,
  XCircle,
  Edit
} from 'lucide-react'
import { mockProductApi } from '../services/productApi'
import { CreateProductRequest } from '../types/product'
import CustomSelect from '../components/CustomSelect'
import toast from 'react-hot-toast'
import MultiSelectDropdown from '../components/MultiSelectDropdown'
import React from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { Underline } from '@tiptap/extension-underline'
import { Link } from '@tiptap/extension-link'
import { Image } from '@tiptap/extension-image'
import { Table } from '@tiptap/extension-table'
import { TextAlign } from '@tiptap/extension-text-align'
import { TableRow } from '@tiptap/extension-table-row'
import { TableCell } from '@tiptap/extension-table-cell'
import { TableHeader } from '@tiptap/extension-table-header'
import FieldManagementModal from '../components/FieldManagementModal'
import CustomFieldRenderer from '../components/CustomFieldRenderer'
import CreateCustomFieldModal from '../components/CreateCustomFieldModal'
import SectionHeader from '../components/SectionHeader'
import ProductVariantModal from '../components/ProductVariantModal'

// Redeclare CustomField interface for local typing
interface CustomField {
  id?: string
  label: string
  key: string
  field_type: string
  resource_type: string
  namespace: string
  description?: string
  placeholder_text?: string
  help_text?: string
  is_required: boolean
  is_searchable: boolean
  is_filterable: boolean
  is_ai_enabled: boolean
  is_active?: boolean
  field_options?: any
  validation_rules?: any
}

// Sample tree for demo
const sampleTree = [
  { id: '1', name: 'New Arrivals' },
  {
    id: '2',
    name: 'Disposables',
    children: [
      { id: '2-1', name: '0% Nicotine' },
      { id: '2-2', name: '2% Nicotine' },
      { id: '2-3', name: '3% Nicotine' },
      { id: '2-4', name: '5% Nicotine' },
      { id: '2-5', name: 'Disposable Pods' },
      { id: '2-6', name: 'Disposable Starter Kit' },
      { id: '2-7', name: 'Disposable Hookah' },
      { id: '2-8', name: 'Synthetic Nic Disposables' },
      { id: '2-9', name: 'North Vape' },
      { id: '2-10', name: 'Pillow Talk' },
    ],
  },
  { id: '3', name: 'Promos' },
  { id: '4', name: 'Deals' },
  { id: '5', name: 'E-Liquids' },
];

const TiptapEditor = ({ value, onChange }: { value: string, onChange: (val: string) => void }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showHtml, setShowHtml] = useState(false);
  const [htmlValue, setHtmlValue] = useState(value);
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      Link,
      Image,
      Table,
      TableRow,
      TableCell,
      TableHeader,
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
    ],
    content: value,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
      setHtmlValue(editor.getHTML());
    },
  });

  useEffect(() => {
    setHtmlValue(value);
  }, [value]);

  // Focus editor when toggling from HTML view back to rich editor
  useEffect(() => {
    if (!showHtml && editor) {
      setTimeout(() => editor.commands.focus('end'), 0);
    }
  }, [showHtml, editor]);

  const setImage = (file: File) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        editor?.chain().focus().setImage({ src: reader.result }).run();
      }
    };
    reader.readAsDataURL(file);
  };

  const btn = (children: React.ReactNode, onClick: () => void, active = false, tooltip = '') => (
    <button
      type="button"
      onClick={onClick}
      className={`p-1.5 rounded-md transition-colors ${active ? 'bg-blue-50 text-blue-700 shadow-sm' : 'hover:bg-gray-200 text-gray-700'} focus:outline-none focus:ring-2 focus:ring-blue-300`}
      title={tooltip}
      tabIndex={-1}
    >
      {children}
    </button>
  );

  return (
    <div className="border border-blue-400 rounded-xl bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
      {/* Style override for ProseMirror content area */}
      <style>{`
        .ProseMirror {
          min-height: 100%;
          height: 100%;
          width: 100%;
          box-sizing: border-box;
          padding: 1rem;
          font-size: 1rem;
          font-family: inherit;
          line-height: 1.5;
          color: #222;
          background: transparent;
          outline: none;
          border: none;
          overflow-y: auto;
          word-break: break-word;
          caret-color: #2563eb;
        }
        .ProseMirror:focus {
          outline: none;
        }
        .ProseMirror p.is-editor-empty:first-child::before {
          content: attr(data-placeholder);
          color: #aaa;
          float: left;
          height: 0;
          pointer-events: none;
        }
        .ProseMirror table {
          border-collapse: collapse;
          width: 100%;
        }
        .ProseMirror th, .ProseMirror td {
          border: 1px solid #e5e7eb;
          padding: 0.5rem 0.75rem;
          min-width: 60px;
          min-height: 36px;
          vertical-align: top;
          background: #fff;
        }
        .ProseMirror th {
          background: #f3f4f6;
          font-weight: 600;
        }
        .ProseMirror img {
          max-width: 100%;
          height: auto;
          display: block;
          margin: 0.5rem 0;
        }
        .ProseMirror pre {
          background: #f3f4f6;
          padding: 0.75rem;
          border-radius: 0.5rem;
          font-size: 0.95em;
          overflow-x: auto;
        }
      `}</style>
      <div className="overflow-x-auto min-w-0">
        <div className="flex flex-nowrap items-center gap-2 w-full border-b bg-gray-50 rounded-t-xl shadow-sm">
          {/* Text style group */}
          <div className="flex gap-1 mr-2">
            {btn(<Bold size={18} />, () => editor?.chain().focus().toggleBold().run(), editor?.isActive('bold'), 'Bold (Ctrl+B)')}
            {btn(<Italic size={18} />, () => editor?.chain().focus().toggleItalic().run(), editor?.isActive('italic'), 'Italic (Ctrl+I)')}
            {btn(<UnderlineIcon size={18} />, () => editor?.chain().focus().toggleUnderline().run(), editor?.isActive('underline'), 'Underline (Ctrl+U)')}
            {btn(<Strikethrough size={18} />, () => editor?.chain().focus().toggleStrike().run(), editor?.isActive('strike'), 'Strikethrough')}
          </div>
          {/* Headings/paragraph */}
          <div className="flex gap-1 mr-2">
            {btn(<Heading size={18} />, () => editor?.chain().focus().toggleHeading({ level: 2 }).run(), editor?.isActive('heading', { level: 2 }), 'Heading 2')}
            {btn(<Type size={18} />, () => editor?.chain().focus().setParagraph().run(), editor?.isActive('paragraph'), 'Paragraph')}
          </div>
          {/* Lists, quote, code */}
          <div className="flex gap-1 mr-2">
            {btn(<List size={18} />, () => editor?.chain().focus().toggleBulletList().run(), editor?.isActive('bulletList'), 'Bullet List')}
            {btn(<ListOrdered size={18} />, () => editor?.chain().focus().toggleOrderedList().run(), editor?.isActive('orderedList'), 'Ordered List')}
            {btn(<Code size={18} />, () => editor?.chain().focus().toggleCodeBlock().run(), editor?.isActive('codeBlock'), 'Code Block')}
            {btn(<Minus size={18} />, () => editor?.chain().focus().setHorizontalRule().run(), false, 'Divider')}
          </div>
          {/* Insert table, image, link */}
          <div className="flex gap-1 mr-2">
            {btn(<TableIcon size={18} />, () => editor?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run(), editor?.isActive('table'), 'Insert Table')}
            {btn(<LinkIcon size={18} />, () => {
              const url = prompt('Enter URL');
              if (url) editor?.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
            }, editor?.isActive('link'), 'Insert Link')}
            {btn(<ImageIcon size={18} />, () => fileInputRef.current?.click(), false, 'Insert Image')}
            <input type="file" accept="image/*" ref={fileInputRef} style={{ display: 'none' }} onChange={e => { if (e.target.files && e.target.files[0]) setImage(e.target.files[0]); }} />
          </div>
          {/* Alignment */}
          <div className="flex gap-1 mr-2">
            {btn(<AlignLeft size={18} />, () => editor?.chain().focus().setTextAlign('left').run(), editor?.isActive({ textAlign: 'left' }), 'Align Left')}
            {btn(<AlignCenter size={18} />, () => editor?.chain().focus().setTextAlign('center').run(), editor?.isActive({ textAlign: 'center' }), 'Align Center')}
            {btn(<AlignRight size={18} />, () => editor?.chain().focus().setTextAlign('right').run(), editor?.isActive({ textAlign: 'right' }), 'Align Right')}
            {btn(<AlignJustify size={18} />, () => editor?.chain().focus().setTextAlign('justify').run(), editor?.isActive({ textAlign: 'justify' }), 'Justify')}
          </div>
          {/* Undo/redo */}
          <div className="flex gap-1 mr-2">
            {btn(<Undo2 size={18} />, () => editor?.chain().focus().undo().run(), false, 'Undo (Ctrl+Z)')}
            {btn(<Redo2 size={18} />, () => editor?.chain().focus().redo().run(), false, 'Redo (Ctrl+Y)')}
          </div>
          {/* Code/HTML toggle */}
          <div className="flex gap-1">
            {btn(<span className="font-mono text-base">{'</>'}</span>, () => setShowHtml(v => {
              if (!v && editor) setHtmlValue(editor.getHTML());
              if (v && editor) editor.commands.setContent(htmlValue);
              return !v;
            }), showHtml, 'Toggle HTML view')}
          </div>
        </div>
      </div>
      {showHtml ? (
        <textarea
          className="min-h-[210px] max-h-[210px] w-full px-4 py-3 bg-gray-50 rounded-b-xl border-0 focus:outline-none font-mono text-sm"
          value={htmlValue}
          onChange={e => setHtmlValue(e.target.value)}
          onBlur={() => editor?.commands.setContent(htmlValue)}
        />
      ) : (
        <div
          className="min-h-[210px] max-h-[210px] overflow-auto px-4 py-3 focus:outline-none bg-white rounded-b-xl cursor-text"
          onClick={() => editor?.commands.focus('end')}
          style={{height: 210}}
        >
          <EditorContent editor={editor} className="h-full" />
        </div>
      )}
    </div>
  );
};

const CreateProductPage = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditMode = Boolean(id)
  const [activeTab, setActiveTab] = useState('basic')
  const [showFieldModal, setShowFieldModal] = useState(false)
  const [fieldTab, setFieldTab] = useState<'system' | 'custom'>('system')
  const [attachedFields, setAttachedFields] = useState<CustomField[]>([])
  const [customFields, setCustomFields] = useState<CustomField[]>([])
  const [showCustomFieldModal, setShowCustomFieldModal] = useState(false)
  const [editingField, setEditingField] = useState<CustomField | null>(null)
  const [visitedTabs, setVisitedTabs] = useState<string[]>(['basic']);
  const [showUrlModal, setShowUrlModal] = useState(false);
  const [urlInput, setUrlInput] = useState('https://');
  const [urlError, setUrlError] = useState('');
  const [showVariantModal, setShowVariantModal] = useState(false);

  // Form state with all BigCommerce fields
  const [formData, setFormData] = useState<CreateProductRequest>({
    name: '',
    product_type: 'physical',
    sku: '',
    description: '',
    weight: 0,
    width: 0,
    depth: 0,
    height: 0,
    price: 0,
    cost_price: 0,
    retail_price: 0,
    sale_price: 0,
    map_price: 0,
    tax_class_id: 0,
    product_tax_code: '',
    brand_id: 0,
    brand_name: '',
    inventory_level: 0,
    inventory_warning_level: 0,
    inventory_tracking: 'none',
    fixed_cost_shipping_price: 0,
    is_free_shipping: false,
    is_visible: true,
    is_featured: false,
    warranty: '',
    bin_picking_number: '',
    layout_file: '',
    upc: '',
    search_keywords: '',
    availability_description: '',
    availability: 'available',
    gift_wrapping_options_type: 'any',
    sort_order: 0,
    condition: 'New',
    is_condition_shown: true,
    order_quantity_minimum: 1,
    order_quantity_maximum: 0,
    page_title: '',
    meta_description: '',
    preorder_release_date: '',
    preorder_message: '',
    is_preorder_only: false,
    is_price_hidden: false,
    price_hidden_label: '',
    open_graph_type: 'product',
    open_graph_title: '',
    open_graph_description: '',
    open_graph_use_meta_description: true,
    open_graph_use_product_name: true,
    open_graph_use_image: true,
    gtin: '',
    mpn: '',
    categories: [],
    images: [],
    videos: [],
    variants: [],
    custom_fields: [],
    bulk_pricing_rules: []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Get categories for dropdown
  const { data: categories = [] } = useQuery('categories', () => mockProductApi.getCategories())

  // Fetch product data when in edit mode
  const { data: productData, isLoading: isLoadingProduct } = useQuery(
    ['product', id],
    () => mockProductApi.getProduct(id!),
    {
      enabled: isEditMode,
      onSuccess: (data) => {
        // Populate form with existing product data
        setFormData(prev => ({
          ...prev,
          ...data,
          categories: Array.isArray(data.categories)
            ? data.categories.map((cat: any) => typeof cat === 'string' ? cat : cat.id)
            : []
        }))
      }
    }
  )

  const saveProductMutation = useMutation(
    (productData: CreateProductRequest) => {
      if (isEditMode) {
        return mockProductApi.updateProduct(id!, productData)
      } else {
        return mockProductApi.createProduct(productData)
      }
    },
    {
      onSuccess: () => {
        toast.success(isEditMode ? 'Product updated successfully!' : 'Product created successfully!')
        navigate('/products')
      },
      onError: (error: any) => {
        toast.error(isEditMode ? 'Failed to update product' : 'Failed to create product')
        console.error('Save product error:', error)
      }
    }
  )

  const handleInputChange = (field: keyof CreateProductRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required'
    }

    if (formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      toast.error('Please fix the errors in the form')
      return
    }

    saveProductMutation.mutate(formData)
  }

  const tabs = [
    { id: 'basic', label: 'Basic Information', icon: Package },
    { id: 'pricing', label: 'Pricing', icon: DollarSign },
    { id: 'inventory', label: 'Inventory', icon: BarChart3 },
    { id: 'shipping', label: 'Shipping', icon: Truck },
    { id: 'seo', label: 'SEO & URLs', icon: Search },
    { id: 'images', label: 'Images & Videos', icon: LucideImage },
    { id: 'variants', label: 'Variants', icon: Palette },
    { id: 'advanced', label: 'Advanced', icon: Settings }
  ]

  // Helper to convert flat categories to tree (if needed)
  function buildCategoryTree(flatCategories: Array<{id: string, name: string, parentId?: string}>) {
    const idToNode: Record<string, { id: string, name: string, parentId?: string, children: any[] }> = {};
    const tree: Array<{ id: string, name: string, parentId?: string, children: any[] }> = [];
    flatCategories.forEach(cat => {
      idToNode[cat.id] = { ...cat, children: [] };
    });
    flatCategories.forEach(cat => {
      if (cat.parentId && idToNode[cat.parentId]) {
        idToNode[cat.parentId].children.push(idToNode[cat.id]);
      } else {
        tree.push(idToNode[cat.id]);
      }
    });
    return tree;
  }

  const renderBasicSection = () => {
    return (
      <div className="space-y-6">
        {/* Product Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Name *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter product name"
          />
          {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
        </div>

        {/* Product Type and SKU side by side */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product Type
            </label>
            <CustomSelect
              options={[
                { value: 'physical', label: 'Physical Product' },
                { value: 'digital', label: 'Digital Product' }
              ]}
              value={formData.product_type}
              onChange={(value) => handleInputChange('product_type', value)}
              placeholder="Select product type"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              SKU
            </label>
            <input
              type="text"
              value={formData.sku}
              onChange={(e) => handleInputChange('sku', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter SKU"
            />
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <TiptapEditor
            value={formData.description || ''}
            onChange={value => handleInputChange('description', value)}
          />
        </div>

        {/* Categories as tag-based multi-select */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Categories
          </label>
          <MultiSelectDropdown
            options={categories.map((cat: any) => ({ value: cat.id, label: cat.name }))}
            value={formData.categories || []}
            onChange={vals => handleInputChange('categories', vals)}
            placeholder="Select categories..."
          />
        </div>

        {/* Brand Name and Condition in two columns */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Brand Name
            </label>
            <input
              type="text"
              value={formData.brand_name}
              onChange={(e) => handleInputChange('brand_name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter brand name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Condition
            </label>
            <CustomSelect
              options={[
                { value: 'New', label: 'New' },
                { value: 'Used', label: 'Used' },
                { value: 'Refurbished', label: 'Refurbished' }
              ]}
              value={formData.condition}
              onChange={(value) => handleInputChange('condition', value)}
              placeholder="Select condition"
            />
          </div>
        </div>
      </div>
    )
  }

  const renderPricingSection = () => {
    return (
      <div className="space-y-6">
        {/* Main Pricing */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Price *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.price === 0 ? '' : formData.price}
                onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                className={`w-full pl-7 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.price ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="0"
              />
            </div>
            {errors.price && <p className="mt-1 text-sm text-red-600">{errors.price}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost Price
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.cost_price === 0 ? '' : formData.cost_price}
                onChange={(e) => handleInputChange('cost_price', parseFloat(e.target.value) || 0)}
                className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
            </div>
          </div>
        </div>

        {/* Additional Pricing */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Retail Price (MSRP)
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.retail_price === 0 ? '' : formData.retail_price}
                onChange={(e) => handleInputChange('retail_price', parseFloat(e.target.value) || 0)}
                className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">Manufacturer's suggested retail price</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sale Price
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.sale_price === 0 ? '' : formData.sale_price}
                onChange={(e) => handleInputChange('sale_price', parseFloat(e.target.value) || 0)}
                className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">Leave blank if not on sale</p>
          </div>
        </div>

        {/* Bulk Pricing */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Bulk Pricing</h4>
          <div className="bg-white rounded-lg border border-gray-200 overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min Qty</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Qty</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{formData.bulk_pricing_rules?.some(r => r.type === 'percentage') ? 'Discount %' : 'Price per Unit'}</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price per Unit</th>
                  <th className="px-6 py-3"></th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {(formData.bulk_pricing_rules ?? []).map((rule, idx) => {
                  let type: 'fixed' | 'percentage' = 'fixed';
                  if (rule.type === 'percentage') type = 'percentage';
                  if (typeof rule.type === 'string' && rule.type !== 'fixed' && rule.type !== 'percentage') type = 'fixed';
                  const mainPrice = formData.price || 0;
                  let pricePerUnit = 0;
                  if (type === 'fixed') {
                    pricePerUnit = rule.amount || 0;
                  } else {
                    const discount = rule.amount || 0;
                    pricePerUnit = mainPrice - (mainPrice * discount / 100);
                  }
                  return (
                    <tr key={idx} className="hover:bg-gray-50 whitespace-nowrap">
                      <td className="px-6 py-4">
                        <input
                          type="number"
                          min={1}
                          value={rule.quantity_min || ''}
                          onChange={e => {
                            const newRules = [...(formData.bulk_pricing_rules ?? [])];
                            newRules[idx].quantity_min = parseInt(e.target.value) || 0;
                            handleInputChange('bulk_pricing_rules', newRules);
                          }}
                          className="w-20 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                          placeholder="Min"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <input
                          type="number"
                          min={rule.quantity_min || 1}
                          value={rule.quantity_max || ''}
                          onChange={e => {
                            const newRules = [...(formData.bulk_pricing_rules ?? [])];
                            newRules[idx].quantity_max = parseInt(e.target.value) || undefined;
                            handleInputChange('bulk_pricing_rules', newRules);
                          }}
                          className="w-20 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                          placeholder="Max"
                        />
                      </td>
                      <td className="px-6 py-4">
                        {type === 'fixed' ? (
                          <input
                            type="number"
                            min={0.01}
                            step={0.01}
                            value={rule.amount || ''}
                            onChange={e => {
                              const newRules = [...(formData.bulk_pricing_rules ?? [])];
                              newRules[idx].amount = parseFloat(e.target.value) || 0;
                              handleInputChange('bulk_pricing_rules', newRules);
                            }}
                            className="w-28 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                            placeholder="Price"
                          />
                        ) : (
                          <input
                            type="number"
                            min={0}
                            max={100}
                            step={0.01}
                            value={rule.amount || ''}
                            onChange={e => {
                              const newRules = [...(formData.bulk_pricing_rules ?? [])];
                              newRules[idx].amount = parseFloat(e.target.value) || 0;
                              handleInputChange('bulk_pricing_rules', newRules);
                            }}
                            className="w-20 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                            placeholder="%"
                          />
                        )}
                      </td>
                      <td className="px-6 py-4 font-semibold text-gray-900">
                        {type === 'fixed' ? (
                          <span>${pricePerUnit.toFixed(2)}</span>
                        ) : (
                          <span>${pricePerUnit.toFixed(2)}</span>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <button
                          type="button"
                          className="text-red-500 hover:underline text-sm"
                          onClick={() => {
                            const newRules = (formData.bulk_pricing_rules ?? []).filter((_, i) => i !== idx);
                            handleInputChange('bulk_pricing_rules', newRules);
                          }}
                        >
                          Remove
                        </button>
                      </td>
                    </tr>
                  );
                })}
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-right">
                    <button
                      type="button"
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-base font-medium shadow-sm transition-colors"
                      onClick={() => {
                        const newRule = { quantity_min: 1, quantity_max: undefined, amount: 0, type: 'fixed' };
                        const newRules = Array.isArray(formData.bulk_pricing_rules)
                          ? [...formData.bulk_pricing_rules, newRule]
                          : [newRule];
                        handleInputChange('bulk_pricing_rules', newRules);
                      }}
                    >
                      + Add Rule
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    )
  }

  const renderInventorySection = () => {
    return (
      <div className="space-y-6">
        {/* Inventory Tracking */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Inventory Tracking
          </label>
          <CustomSelect
            options={[
              { value: 'none', label: 'Don\'t track inventory' },
              { value: 'simple', label: 'Track inventory' },
              { value: 'variant', label: 'Track inventory by variant' }
            ]}
            value={formData.inventory_tracking}
            onChange={(value) => handleInputChange('inventory_tracking', value)}
            placeholder="Select inventory tracking"
          />
        </div>

        {/* Current Stock Level */}
        {formData.inventory_tracking !== 'none' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Stock Level
              </label>
              <input
                type="number"
                min="0"
                value={formData.inventory_level}
                onChange={(e) => handleInputChange('inventory_level', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Low Stock Warning Level
              </label>
              <input
                type="number"
                min="0"
                value={formData.inventory_warning_level}
                onChange={(e) => handleInputChange('inventory_warning_level', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
            </div>
          </div>
        )}

        {/* Order Quantity Limits */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Order Quantity Limits</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Order Quantity
              </label>
              <input
                type="number"
                min="1"
                value={formData.order_quantity_minimum}
                onChange={(e) => handleInputChange('order_quantity_minimum', parseInt(e.target.value) || 1)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Order Quantity
              </label>
              <input
                type="number"
                min="0"
                value={formData.order_quantity_maximum === 0 ? '' : formData.order_quantity_maximum}
                onChange={(e) => handleInputChange('order_quantity_maximum', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0"
              />
              <p className="mt-1 text-xs text-gray-500">Set to 0 for unlimited quantity</p>
            </div>
          </div>
        </div>

        {/* Bin Picking Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Bin Picking Number
          </label>
          <input
            type="text"
            value={formData.bin_picking_number}
            onChange={(e) => handleInputChange('bin_picking_number', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter bin picking number"
          />
          <p className="mt-1 text-xs text-gray-500">Used for warehouse management and order fulfillment</p>
        </div>
      </div>
    )
  }

  const renderShippingSection = () => {
    return (
      <div className="space-y-6">
        {/* Physical Product Dimensions */}
        {formData.product_type === 'physical' && (
          <>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Weight (lbs)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.weight === 0 ? '' : formData.weight}
                  onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Width (in)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.width === 0 ? '' : formData.width}
                  onChange={(e) => handleInputChange('width', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Height (in)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.height === 0 ? '' : formData.height}
                  onChange={(e) => handleInputChange('height', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Depth (in)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.depth === 0 ? '' : formData.depth}
                  onChange={(e) => handleInputChange('depth', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>
            </div>

            {/* Shipping Options */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Shipping Options</h4>

              <div className="space-y-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_free_shipping}
                    onChange={(e) => handleInputChange('is_free_shipping', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <Truck className="h-4 w-4 ml-2 mr-1 text-gray-500" />
                  <span className="text-sm text-gray-700">Free shipping</span>
                </label>

                {!formData.is_free_shipping && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fixed Shipping Cost
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">$</span>
                      </div>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.fixed_cost_shipping_price}
                        onChange={(e) => handleInputChange('fixed_cost_shipping_price', parseFloat(e.target.value) || 0)}
                        className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="0.00"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">Leave blank to use calculated shipping</p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Warranty */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Warranty Information
          </label>
          <textarea
            rows={3}
            value={formData.warranty}
            onChange={(e) => handleInputChange('warranty', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter warranty information"
          />
        </div>
      </div>
    )
  }

  const renderSeoSection = () => {
    return (
      <div className="space-y-6">
        {/* Page Title & Meta */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Page Title
          </label>
          <input
            type="text"
            value={formData.page_title}
            onChange={(e) => handleInputChange('page_title', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter page title (leave blank to use product name)"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Meta Description
          </label>
          <textarea
            rows={3}
            value={formData.meta_description}
            onChange={(e) => handleInputChange('meta_description', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter meta description for search engines"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Search Keywords
          </label>
          <input
            type="text"
            value={formData.search_keywords}
            onChange={(e) => handleInputChange('search_keywords', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter search keywords (comma separated)"
          />
        </div>

        {/* Product Identifiers */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Product Identifiers</h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                UPC
              </label>
              <input
                type="text"
                value={formData.upc}
                onChange={(e) => handleInputChange('upc', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter UPC"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                MPN
              </label>
              <input
                type="text"
                value={formData.mpn}
                onChange={(e) => handleInputChange('mpn', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter MPN"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                GTIN
              </label>
              <input
                type="text"
                value={formData.gtin}
                onChange={(e) => handleInputChange('gtin', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter GTIN"
              />
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderImagesSection = () => {
    return (
      <div className="space-y-6">
        {/* Product Images */}
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
          <LucideImage className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Add Product Images</h3>
          <p className="text-gray-600 mb-4">
            Upload images to showcase your product. First image will be the main product image.
          </p>
          <div className="flex justify-center space-x-3">
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Upload Images
            </button>
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              onClick={() => setShowUrlModal(true)}
            >
              Add from URL
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-3">
            Supported formats: JPG, PNG, GIF, WebP. Max size: 10MB per image.
          </p>
        </div>

        {/* Product Videos */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Product Videos</h4>
          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
            <div className="flex justify-center mb-3">
              <div className="bg-gray-100 p-3 rounded-full">
                <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
                </svg>
              </div>
            </div>
            <h4 className="text-md font-medium text-gray-900 mb-2">Add Product Videos</h4>
            <p className="text-gray-600 mb-4">Add YouTube or Vimeo videos to showcase your product</p>
            <button
              type="button"
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Add Video URL
            </button>
          </div>
        </div>

        {(formData.images && formData.images.length > 0) && (
          <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
            {formData.images.map((img, idx) => {
              const url = img.image_url;
              const isImage = /\.(jpe?g|png|gif|webp|bmp|svg)$/i.test(url) || url.match(/^https?:\/\/.+\.(jpe?g|png|gif|webp|bmp|svg)(\?.*)?$/i);
              return (
                <div key={url + idx} className="relative group border rounded-lg p-2 bg-white flex flex-col items-center justify-center">
                  {isImage ? (
                    <img src={url} alt="Product" className="w-full h-32 object-contain rounded mb-2 bg-gray-50" onError={e => { e.currentTarget.style.display = 'none'; }} />
                  ) : (
                    <div className="flex flex-col items-center justify-center h-32 w-full mb-2">
                      <LucideImage className="w-10 h-10 text-gray-400 mb-1" />
                      <span className="text-xs text-gray-500 break-all text-center">Media Link</span>
                    </div>
                  )}
                  <a href={url} target="_blank" rel="noopener noreferrer" className="text-xs text-blue-600 hover:underline break-all mb-2">{url.length > 40 ? url.slice(0, 37) + '...' : url}</a>
                  <button
                    className="absolute top-2 right-2 bg-white rounded-full p-1 shadow hover:bg-gray-100"
                    onClick={() => setFormData(prev => ({ ...prev, images: prev.images?.filter((_, i) => i !== idx) }))}
                    type="button"
                  >
                    <XCircle className="w-5 h-5 text-gray-400 hover:text-red-500" />
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>
    )
  }

  const renderVariantsSection = () => {
    const hasVariants = formData.variants && formData.variants.length > 0;
    
    return (
      <div className="space-y-6">
        {/* Variant Options */}
        {!hasVariants ? (
          <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
            <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Product Variants</h3>
            <p className="text-gray-600 mb-4">
              Create variants for different sizes, colors, materials, or other options
            </p>
            <button
              type="button"
              onClick={() => setShowVariantModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Add Variant Option
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Product Variants</h3>
                <p className="text-sm text-gray-600">
                  {formData.variants?.length || 0} variants configured
                  {formData.variants && formData.variants.some(v => v.purchasing_disabled) && (
                    <span className="ml-2 text-red-500">
                      • {formData.variants.filter(v => v.purchasing_disabled).length} disabled
                    </span>
                  )}
                </p>
              </div>
              <button
                type="button"
                onClick={() => setShowVariantModal(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Variants
              </button>
            </div>
            
            {/* Variants Summary */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">Variant Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {formData.variants?.slice(0, 6).map((variant, index) => (
                  <div key={index} className={`bg-white rounded border p-3 ${variant.purchasing_disabled ? 'opacity-60 border-gray-300' : 'border-gray-200'}`}>
                    <div className="text-sm text-gray-900 mb-1">
                      {variant.option_values?.map((opt, i) => (
                        <span key={i} className="inline-block mr-1">
                          <span className="font-medium">{opt.option_display_name}:</span> {opt.label}
                        </span>
                      ))}
                    </div>
                    <div className="text-xs text-gray-500">
                      SKU: {variant.sku || 'N/A'} • Price: ${variant.price || 0} • Stock: {variant.inventory_level || 0}
                      {variant.purchasing_disabled && (
                        <span className="ml-2 text-red-500 font-medium">• Disabled</span>
                      )}
                    </div>
                  </div>
                ))}
                {(formData.variants?.length || 0) > 6 && (
                  <div className="bg-white rounded border p-3 flex items-center justify-center">
                    <span className="text-sm text-gray-500">
                      +{(formData.variants?.length || 0) - 6} more variants
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  const renderAdvancedSection = () => {
    return (
      <div className="space-y-6">
        {/* Product Availability */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4">Product Availability</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Availability
              </label>
              <CustomSelect
                options={[
                  { value: 'available', label: 'Available' },
                  { value: 'disabled', label: 'Disabled' },
                  { value: 'preorder', label: 'Preorder' }
                ]}
                value={formData.availability}
                onChange={(value) => handleInputChange('availability', value)}
                placeholder="Select availability"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Availability Description
              </label>
              <input
                type="text"
                value={formData.availability_description}
                onChange={(e) => handleInputChange('availability_description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Usually ships in 24 hours"
              />
            </div>
          </div>
        </div>

        {/* Preorder Settings */}
        {formData.availability === 'preorder' && (
          <div className="border-t pt-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Preorder Settings</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Release Date
                </label>
                <input
                  type="date"
                  value={formData.preorder_release_date}
                  onChange={(e) => handleInputChange('preorder_release_date', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preorder Message
                </label>
                <input
                  type="text"
                  value={formData.preorder_message}
                  onChange={(e) => handleInputChange('preorder_message', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Expected to ship on..."
                />
              </div>
            </div>
          </div>
        )}

        {/* Purchase Options */}
        <div className="border-t pt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Purchase Options</h4>

          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_price_hidden}
                onChange={(e) => handleInputChange('is_price_hidden', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Hide price from customers</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_preorder_only}
                onChange={(e) => handleInputChange('is_preorder_only', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Preorder only</span>
            </label>
          </div>
        </div>

        {/* Custom Fields */}
        <div className="border-t pt-6">
          <CustomFieldRenderer
            fields={attachedFields.filter(f => f.is_active === true && typeof f.id === 'string') as any}
            values={formData}
            onChange={handleInputChange as (key: string, value: any) => void}
            resourceType="products"
            showFieldManagement={true}
            onAddField={handleAddField}
            onRemoveField={handleRemoveField}
          />
          <FieldManagementModal
            isOpen={showFieldModal}
            onClose={closeFieldModal}
            resourceType="products"
            activeFields={attachedFields}
            onFieldsChange={(fields: any) => setAttachedFields(fields)}
            onCreateNewField={() => setFieldTab('custom')}
          />
        </div>
      </div>
    )
  }

  // Add scroll functionality
  const sectionRefs = useRef<Record<string, HTMLElement | null>>({})

  const scrollToSection = (sectionId: string) => {
    setActiveTab(sectionId);
    setVisitedTabs((prev) => prev.includes(sectionId) ? prev : [...prev, sectionId]);
    const element = sectionRefs.current[sectionId];
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Track active section on scroll
  useEffect(() => {
    const handleScroll = () => {
      const sections = Object.keys(sectionRefs.current);
      const scrollPosition = window.scrollY + 200;
      for (const sectionId of sections) {
        const element = sectionRefs.current[sectionId];
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveTab(sectionId);
            setVisitedTabs((prev) => prev.includes(sectionId) ? prev : [...prev, sectionId]);
            break;
          }
        }
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handler to add a system field
  const handleAddSystemField = (field: CustomField) => {
    if (!attachedFields.some(f => f.id === field.id)) {
      setAttachedFields([...attachedFields, field])
    }
  }

  // Handler to add a custom field
  const handleAddCustomField = (field: CustomField) => {
    setCustomFields([...customFields, field])
    setAttachedFields([...attachedFields, field])
  }

  // Handler to remove a field
  const handleRemoveField = (fieldId: string) => {
    setAttachedFields(attachedFields.filter(f => f.id !== fieldId))
    setCustomFields(customFields.filter(f => f.id !== fieldId))
  }

  // Handler to open modal
  const openFieldModal = () => setShowFieldModal(true)
  const closeFieldModal = () => setShowFieldModal(false)

  const handleAddField = () => {
    setEditingField(null)
    setShowCustomFieldModal(true)
  }

  const handleSaveCustomField = (field: CustomField) => {
    // Only add to this page's custom fields
    const newField = {
      ...field,
      id: (field.id ?? Date.now().toString()),
      resource_type: 'products',
      namespace: 'local',
      is_active: true // ensure required property
    }
    setCustomFields(prev => [...prev, newField])
    setAttachedFields(prev => [...prev, newField])
    setShowCustomFieldModal(false)
    setEditingField(null)
  }

  // Utility to check required fields for each tab (only tabs with * are required)
  const tabStatus = (tabId: string) => {
    if (!visitedTabs.includes(tabId)) return 'untouched'; // gray by default until visited
    switch (tabId) {
      case 'basic':
        // Product Name *
        return formData.name.trim() ? 'complete' : 'error';
      case 'pricing':
        // Price *
        return formData.price > 0 ? 'complete' : 'error';
      default:
        // All other tabs have no required fields
        return 'complete';
    }
  };

  // Show loading state when fetching product data in edit mode
  if (isEditMode && isLoadingProduct) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading product data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex bg-white">
      {/* Sticky Left Navigation */}
      <div className="w-80 sticky top-16 bg-white border-r border-gray-200">
        <div className="p-6" style={{ position: 'fixed' }}>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Sections</h3>
          <nav className="space-y-2">
            {tabs.map((tab, index) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              const status = tabStatus(tab.id);
              return (
                <button
                  key={tab.id}
                  onClick={() => scrollToSection(tab.id)}
                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200
                    ${isActive && status === 'error' ? 'bg-red-50 text-red-700' :
                      isActive ? 'bg-blue-50 text-blue-700' :
                      status === 'error' ? 'bg-red-50 text-red-700 hover:bg-red-100' :
                      status === 'complete' ? 'bg-green-50 text-green-700 hover:bg-green-100' :
                      'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}
                  `}
                >
                  {status === 'error' ? (
                    <XCircle className={`h-5 w-5 mr-3 flex-shrink-0 text-red-600`} />
                  ) : (
                    <Icon className={`h-5 w-5 mr-3 flex-shrink-0 ${isActive ? 'text-blue-600' : status === 'complete' ? 'text-green-600' : 'text-gray-400'}`} />
                  )}
                  <div className="flex-1">
                    <div className={`font-medium ${status === 'error' ? 'text-red-700' : ''}`}>{tab.label}</div>
                    <div className={`text-xs mt-1 ${status === 'error' ? 'text-red-500' : 'text-gray-500'}`}>
                      {tab.id === 'basic' && 'Name, description, category'}
                      {tab.id === 'pricing' && 'Price, cost, tax settings'}
                      {tab.id === 'inventory' && 'Stock levels, tracking'}
                      {tab.id === 'shipping' && 'Weight, dimensions'}
                      {tab.id === 'seo' && 'Meta tags, URLs'}
                      {tab.id === 'images' && 'Photos, videos'}
                      {tab.id === 'variants' && 'Options, variations'}
                      {tab.id === 'advanced' && 'Custom fields, settings'}
                    </div>
                  </div>
                  {status === 'error' && (
                    <div className="h-2 w-2 bg-red-500 rounded-full flex-shrink-0"></div>
                  )}
                  {status === 'complete' && !isActive && (
                    <div className="h-2 w-2 bg-green-500 rounded-full flex-shrink-0"></div>
                  )}
                  {isActive && status !== 'error' && (
                    <div className="h-2 w-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                  )}
                </button>
              );
            })}
          </nav>

          {/* Progress Indicator */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{Math.round((tabs.findIndex(t => t.id === activeTab) + 1) / tabs.length * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(tabs.findIndex(t => t.id === activeTab) + 1) / tabs.length * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className="flex-1 min-h-screen bg-white">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <SectionHeader
            icon={<Package className="h-7 w-7" />}
            title={isEditMode ? 'Edit Product' : 'Create Product'}
            subtitle={isEditMode ? 'Update product details and save changes.' : 'Fill in the details to add a new product.'}
            actions={
              <button
                type="submit"
                form="product-form"
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
              >
                <Save className="h-4 w-4" />
                {isEditMode ? 'Save Changes' : 'Create Product'}
              </button>
            }
          />
          <form onSubmit={handleSubmit} className="space-y-12">
            {/* Basic Information Section */}
            <section
              ref={(el) => sectionRefs.current['basic'] = el}
              id="basic"
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
            >
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <Package className="h-5 w-5 text-blue-600 mr-2" />
                  Basic Information
                </h2>
                <p className="text-gray-600 mt-1">Essential product details and categorization</p>
              </div>
              {renderBasicSection()}
            </section>

            {/* Pricing Section */}
            <section
              ref={(el) => sectionRefs.current['pricing'] = el}
              id="pricing"
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
            >
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <DollarSign className="h-5 w-5 text-blue-600 mr-2" />
                  Pricing
                </h2>
                <p className="text-gray-600 mt-1">Set pricing, costs, and display options</p>
              </div>
              {renderPricingSection()}
            </section>

            {/* Inventory Section */}
            <section
              ref={(el) => sectionRefs.current['inventory'] = el}
              id="inventory"
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
            >
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <BarChart3 className="h-5 w-5 text-blue-600 mr-2" />
                  Inventory
                </h2>
                <p className="text-gray-600 mt-1">Manage stock levels and availability</p>
              </div>
              {renderInventorySection()}
            </section>

            {/* Shipping Section */}
            <section
              ref={(el) => sectionRefs.current['shipping'] = el}
              id="shipping"
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
            >
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <Truck className="h-5 w-5 text-blue-600 mr-2" />
                  Shipping
                </h2>
                <p className="text-gray-600 mt-1">Configure shipping options and dimensions</p>
              </div>
              {renderShippingSection()}
            </section>

            {/* SEO Section */}
            <section
              ref={(el) => sectionRefs.current['seo'] = el}
              id="seo"
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
            >
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <Search className="h-5 w-5 text-blue-600 mr-2" />
                  SEO & URLs
                </h2>
                <p className="text-gray-600 mt-1">Optimize for search engines and social media</p>
              </div>
              {renderSeoSection()}
            </section>

            {/* Images Section */}
            <section
              ref={(el) => sectionRefs.current['images'] = el}
              id="images"
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
            >
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <LucideImage className="h-5 w-5 text-blue-600 mr-2" />
                  Images & Videos
                </h2>
                <p className="text-gray-600 mt-1">Upload product media and galleries</p>
              </div>
              {renderImagesSection()}
            </section>

            {/* Variants Section */}
            <section
              ref={(el) => sectionRefs.current['variants'] = el}
              id="variants"
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
            >
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <Palette className="h-5 w-5 text-blue-600 mr-2" />
                  Variants
                </h2>
                <p className="text-gray-600 mt-1">Create product options and variations</p>
              </div>
              {renderVariantsSection()}
            </section>

            {/* Advanced Section */}
            <section
              ref={(el) => sectionRefs.current['advanced'] = el}
              id="advanced"
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-8"
            >
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                  <Settings className="h-5 w-5 text-blue-600 mr-2" />
                  Advanced
                </h2>
                <p className="text-gray-600 mt-1">Additional settings and configurations</p>
              </div>
              {renderAdvancedSection()}
            </section>
          </form>
        </div>
      </div>
      <CreateCustomFieldModal
        isOpen={showCustomFieldModal}
        onClose={() => {
          setShowCustomFieldModal(false)
          setEditingField(null)
        }}
        onSave={handleSaveCustomField}
        editingField={editingField}
      />
      {showUrlModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md relative">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
              onClick={() => { setShowUrlModal(false); setUrlInput(''); setUrlError(''); }}
            >
              <XCircle className="w-6 h-6" />
            </button>
            <h3 className="text-lg font-semibold mb-2">Add Image or Media URLs</h3>
            <p className="text-sm text-gray-500 mb-4">Paste one or more URLs (one per line). Supports direct image links, Google Drive, WeTransfer, etc.</p>
            <input
              type="text"
              className="w-full border border-gray-300 rounded-md px-3 py-2 mb-2"
              placeholder="https://..."
              value={urlInput}
              onChange={e => {
                // Prevent newlines and ensure it always starts with https://
                let val = e.target.value.replace(/\n/g, '');
                if (!val.startsWith('https://')) val = 'https://';
                setUrlInput(val);
              }}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
            />
            {urlError && <div className="text-red-500 text-sm mb-2">{urlError}</div>}
            <div className="flex justify-end gap-2">
              <button
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-md"
                onClick={() => { setShowUrlModal(false); setUrlInput(''); setUrlError(''); }}
              >Cancel</button>
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={() => {
                  const url = urlInput.trim();
                  if (!/^https:\/\//.test(url) || url.length <= 'https://'.length) {
                    setUrlError('Please enter a valid URL that starts with https://');
                    return;
                  }
                  setFormData(prev => ({
                    ...prev,
                    images: [
                      ...(prev.images || []),
                      {
                        image_url: url,
                        is_thumbnail: false,
                        sort_order: prev.images?.length || 0,
                        description: '',
                      },
                    ],
                  }));
                  setShowUrlModal(false);
                  setUrlInput('https://');
                  setUrlError('');
                }}
              >Add URLs</button>
            </div>
          </div>
        </div>
      )}
      
      {/* Product Variant Modal */}
      <ProductVariantModal
        isOpen={showVariantModal}
        onClose={() => setShowVariantModal(false)}
        variants={formData.variants || []}
        onSave={(variants) => {
          handleInputChange('variants', variants)
          setShowVariantModal(false)
        }}
      />
    </div>
  )
}

export default CreateProductPage

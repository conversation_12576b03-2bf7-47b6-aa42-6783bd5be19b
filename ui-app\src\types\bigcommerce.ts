// BigCommerce Order Types based on API specification

export interface BigCommerceAddress {
  first_name: string
  last_name: string
  company: string
  street_1: string
  street_2: string
  city: string
  state: string
  zip: string
  country: string
  country_iso2: string
  phone: string
  email: string
  form_fields: Array<{
    name: string
    value: string
  }>
}

export interface BigCommerceFee {
  id: number
  type: string
  display_name_customer: string
  display_name_merchant: string
  source: string
  base_cost: string
  cost_exc_tax: string
  cost_inc_tax: string
  cost_tax: string
  tax_class_id: number | null
}

export interface BigCommerceOrder {
  id: number
  customer_id: number
  date_created: string
  date_modified: string
  date_shipped: string
  status_id: number
  status: string
  subtotal_ex_tax: string
  subtotal_inc_tax: string
  subtotal_tax: string
  base_shipping_cost: string
  shipping_cost_ex_tax: string
  shipping_cost_inc_tax: string
  shipping_cost_tax: string
  shipping_cost_tax_class_id: number
  base_handling_cost: string
  handling_cost_ex_tax: string
  handling_cost_inc_tax: string
  handling_cost_tax: string
  handling_cost_tax_class_id: number
  base_wrapping_cost: string
  wrapping_cost_ex_tax: string
  wrapping_cost_inc_tax: string
  wrapping_cost_tax: string
  wrapping_cost_tax_class_id: number
  total_ex_tax: string
  total_inc_tax: string
  total_tax: string
  is_tax_inclusive_pricing: boolean
  items_total: number
  items_shipped: number
  payment_method: string
  payment_provider_id: string
  payment_status: string
  refunded_amount: string
  order_is_digital: boolean
  store_credit_amount: string
  gift_certificate_amount: string
  ip_address: string
  ip_address_v6: string
  geoip_country: string
  geoip_country_iso2: string
  currency_id: number
  currency_code: string
  currency_exchange_rate: string
  default_currency_id: number
  default_currency_code: string
  staff_notes: string
  customer_message: string
  discount_amount: string
  coupon_discount: string
  shipping_address_count: number
  ebay_order_id: string
  cart_id: string
  billing_address: BigCommerceAddress
  is_email_opt_in: boolean
  credit_card_type: string | null
  order_source: string
  channel_id: number
  external_source: string | null
  products: {
    url: string
    resource: string
  }
  shipping_addresses: {
    url: string
    resource: string
  }
  coupons: {
    url: string
    resource: string
  }
  external_id: string | null
  external_merchant_id: string | null
  tax_provider_id: string
  store_default_currency_code: string
  store_default_to_transactional_exchange_rate: string
  custom_status: string
  customer_locale: string
  external_order_id: string
  fees: BigCommerceFee[]
}

export interface BigCommerceProduct {
  id: number
  order_id: number
  product_id: number
  variant_id: number
  order_address_id: number
  name: string
  name_customer: string
  name_merchant: string
  sku: string
  upc: string
  type: string
  base_price: string
  price_ex_tax: string
  price_inc_tax: string
  price_tax: string
  base_total: string
  total_ex_tax: string
  total_inc_tax: string
  total_tax: string
  weight: string
  width: string
  height: string
  depth: string
  quantity: number
  base_cost_price: string
  cost_price_inc_tax: string
  cost_price_ex_tax: string
  cost_price_tax: string
  is_refunded: boolean
  quantity_refunded: number
  refund_amount: string
  return_id: number
  wrapping_name: string
  base_wrapping_cost: string
  wrapping_cost_ex_tax: string
  wrapping_cost_inc_tax: string
  wrapping_cost_tax: string
  wrapping_message: string
  quantity_shipped: number
  event_name: string | null
  event_date: string | null
  fixed_shipping_cost: string
  ebay_item_id: string
  ebay_transaction_id: string
  option_set_id: number | null
  parent_order_product_id: number | null
  is_bundled_product: boolean
  bin_picking_number: string
  applied_discounts: Array<{
    id: string
    amount: string
  }>
  product_options: Array<{
    id: number
    option_id: number
    order_product_id: number
    product_option_id: number
    display_name: string
    display_name_customer: string
    display_name_merchant: string
    display_value: string
    display_value_customer: string
    display_value_merchant: string
    value: string
    type: string
    name: string
    display_style: string
  }>
  configurable_fields: Array<{
    name: string
    value: string
  }>
}

export interface BigCommerceShippingAddress extends BigCommerceAddress {
  id: number
  order_id: number
  items_total: number
  items_shipped: number
  base_cost: string
  cost_ex_tax: string
  cost_inc_tax: string
  cost_tax: string
  cost_tax_class_id: number
  base_handling_cost: string
  handling_cost_ex_tax: string
  handling_cost_inc_tax: string
  handling_cost_tax: string
  handling_cost_tax_class_id: number
  shipping_method: string
  shipping_zone_id: number
  shipping_zone_name: string
}

import React from 'react';
import { MessageCircle, X, Minimize2, Maximize2 } from 'lucide-react';
import { useChat } from '../contexts/ChatContext';

const ChatBubble: React.FC = () => {
  const { 
    isChatOpen, 
    isChatExpanded, 
    toggleChat, 
    expandChat, 
    collapseChat,
    isLoading 
  } = useChat();

  if (!isChatOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={toggleChat}
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
          title="Open Chat Assistant"
        >
          <MessageCircle className="h-6 w-6" />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="bg-white rounded-lg shadow-2xl border border-gray-200 w-80 max-h-96 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="font-medium text-gray-900">Nuclues Assistant</span>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={isChatExpanded ? collapseChat : expandChat}
              className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
              title={isChatExpanded ? "Collapse" : "Expand"}
            >
              {isChatExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </button>
            <button
              onClick={toggleChat}
              className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
              title="Close"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Chat Content */}
        <div className="flex-1 overflow-hidden">
          <ChatWindow />
        </div>

        {/* Loading indicator */}
        {isLoading && (
          <div className="p-3 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>Assistant is typing...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Chat Window Component
const ChatWindow: React.FC = () => {
  const { currentConversation, sendMessage, isLoading } = useChat();
  const [inputValue, setInputValue] = React.useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;
    
    await sendMessage(inputValue);
    setInputValue('');
  };

  const messages = currentConversation?.messages || [];

  return (
    <div className="flex flex-col h-full">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 max-h-64">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 text-sm">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p>Ask me anything about your Nuclues platform!</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                {message.content}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Type your message..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={!inputValue.trim() || isLoading}
            className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Send
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatBubble; 
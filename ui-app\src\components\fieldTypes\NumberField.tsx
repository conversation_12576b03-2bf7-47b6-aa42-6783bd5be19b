import React from 'react'
import { FieldProps } from './index'

const NumberField: React.FC<FieldProps> = ({
  id,
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  className = '',
  error,
  validation
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    
    if (newValue === '') {
      onChange('')
      return
    }
    
    const numValue = parseFloat(newValue)
    
    if (isNaN(numValue)) {
      return // Don't update if not a valid number
    }
    
    // Apply validation if provided
    if (validation) {
      if (validation.min !== undefined && numValue < validation.min) {
        return // Don't update if below minimum
      }
      if (validation.max !== undefined && numValue > validation.max) {
        return // Don't update if above maximum
      }
    }
    
    onChange(numValue)
  }

  const baseClasses = "w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
  const errorClasses = error ? "border-red-300 focus:ring-red-500 focus:border-red-500" : "border-gray-300"
  const disabledClasses = disabled ? "bg-gray-100 cursor-not-allowed" : ""

  return (
    <div className="space-y-1">
      <input
        id={id}
        type="number"
        value={value || ''}
        onChange={handleChange}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        min={validation?.min}
        max={validation?.max}
        step="any"
        className={`${baseClasses} ${errorClasses} ${disabledClasses} ${className}`}
      />
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      {(validation?.min !== undefined || validation?.max !== undefined) && (
        <p className="text-xs text-gray-500">
          {validation.min !== undefined && validation.max !== undefined
            ? `Range: ${validation.min} - ${validation.max}`
            : validation.min !== undefined
            ? `Minimum: ${validation.min}`
            : `Maximum: ${validation.max}`
          }
        </p>
      )}
    </div>
  )
}

export default NumberField

import React from 'react';

export interface StructuredData {
  type: 'customer' | 'product' | 'order' | 'revenue' | 'list';
  title: string;
  subtitle?: string;
  data: any;
  actions?: Array<{
    label: string;
    action: string;
    icon?: string;
  }>;
}

interface StructuredChatResponseProps {
  data: StructuredData;
}

const StructuredChatResponse: React.FC<StructuredChatResponseProps> = ({ data }) => {
  return (
    <div className="mt-3 p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
      <h3 className="font-semibold text-gray-900 mb-2">{data.title}</h3>
      {data.subtitle && <p className="text-sm text-gray-600 mb-3">{data.subtitle}</p>}
      
      <div className="text-sm text-gray-700">
        {data.type === 'customer' && (
          <div>
            <p><strong>Name:</strong> {data.data.name}</p>
            <p><strong>Company:</strong> {data.data.company}</p>
            <p><strong>Total Orders:</strong> {data.data.totalOrders}</p>
            <p><strong>Total Revenue:</strong> ${data.data.totalRevenue?.toLocaleString()}</p>
          </div>
        )}
        
        {data.type === 'product' && (
          <div>
            <p><strong>Product:</strong> {data.data.name}</p>
            <p><strong>Category:</strong> {data.data.category}</p>
            <p><strong>Units Sold:</strong> {data.data.unitsSold}</p>
            <p><strong>Revenue:</strong> ${data.data.revenue?.toLocaleString()}</p>
          </div>
        )}
        
        {data.type === 'order' && (
          <div>
            <p><strong>Order ID:</strong> {data.data.id}</p>
            <p><strong>Customer:</strong> {data.data.customer}</p>
            <p><strong>Amount:</strong> ${data.data.amount?.toLocaleString()}</p>
            <p><strong>Status:</strong> {data.data.status}</p>
          </div>
        )}
        
        {data.type === 'list' && (
          <div>
            {Array.isArray(data.data) && data.data.map((item: any, index: number) => (
              <div key={index} className="mb-2 p-2 bg-gray-50 rounded">
                <p><strong>{item.name}</strong></p>
                {item.company && <p className="text-xs text-gray-600">{item.company}</p>}
                {item.revenue && <p className="text-xs text-gray-600">Revenue: ${item.revenue.toLocaleString()}</p>}
              </div>
            ))}
          </div>
        )}
      </div>
      
      {data.actions && data.actions.length > 0 && (
        <div className="mt-3 flex gap-2">
          {data.actions.map((action, index) => (
            <button
              key={index}
              className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
              onClick={() => console.log('Action:', action.action)}
            >
              {action.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default StructuredChatResponse; 
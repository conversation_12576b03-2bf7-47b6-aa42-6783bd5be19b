export { default as <PERSON>Field } from './TextField'
export { default as <PERSON><PERSON><PERSON> } from './NumberField'
export { default as SingleSelectField } from './SingleSelectField'
export { default as MultiSelectField } from './MultiSelectField'

// For now, we'll use TextField for these types with different input types
export { default as TextareaField } from './TextField'
export { default as EmailField } from './TextField'
export { default as UrlField } from './TextField'
export { default as DateField } from './TextField'
export { default as BooleanField } from './TextField'

export interface FieldProps {
  id: string
  value: any
  onChange: (value: any) => void
  placeholder?: string
  required?: boolean
  disabled?: boolean
  className?: string
  error?: string
  options?: Array<{ value: string; label: string }>
  validation?: {
    min?: number
    max?: number
    minLength?: number
    maxLength?: number
    pattern?: string
  }
}

export interface FieldTypeConfig {
  component: React.ComponentType<FieldProps>
  defaultValue: any
  validation?: (value: any, rules?: any) => string | null
  formatValue?: (value: any) => any
  parseValue?: (value: any) => any
}

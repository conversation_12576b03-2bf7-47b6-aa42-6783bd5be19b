import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Settings, 
  Trash2, 
  MoreHorizontal, 
  TrendingUp, 
  Calendar,
  Check,
  X,
  AlertCircle,
  RefreshCw,
  Download,
  Filter,
  Share2,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { productSharingApi, SharedProduct } from '../services/productSharingApi';
import { vendorApi, Vendor } from '../services/vendorApi';

interface SharedProductsTableProps {
  onRefresh?: () => void;
  onShareProduct?: (product: SharedProduct) => void;
}

const SharedProductsTable: React.FC<SharedProductsTableProps> = ({ onRefresh, onShareProduct }) => {
  const [sharedProducts, setSharedProducts] = useState<SharedProduct[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVendor, setSelectedVendor] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const [productsPerPage] = useState(20);

  useEffect(() => {
    loadData();
  }, [currentPage, searchTerm, selectedVendor, selectedStatus]);

  const loadData = async () => {
    setLoading(true);
    try {
      const [sharedResult, vendorsResult] = await Promise.all([
        productSharingApi.getSharedProducts({
          page: currentPage,
          limit: productsPerPage,
          search: searchTerm || undefined,
          vendor_id: selectedVendor || undefined,
          is_active: selectedStatus === 'active' ? true : selectedStatus === 'inactive' ? false : undefined
        }),
        vendorApi.getVendors()
      ]);
      setSharedProducts(sharedResult.shared_products);
      setTotalPages(sharedResult.total_pages);
      setTotalProducts(sharedResult.total);
      setVendors(vendorsResult.vendors);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUnshare = async (productId: string, vendorId: string) => {
    if (!confirm('Are you sure you want to unshare this product?')) {
      return;
    }

    try {
      await productSharingApi.unshareProduct({ product_id: productId, vendor_id: vendorId });
      setSharedProducts(prev => prev.filter(sp => !(sp.product_id === productId && sp.vendor_id === vendorId)));
      alert('Product unshared successfully');
      // Refresh data to update pagination
      loadData();
    } catch (error) {
      console.error('Failed to unshare product:', error);
      alert('Failed to unshare product');
    }
  };

  const handleBulkUnshare = async () => {
    if (selectedProducts.length === 0) {
      alert('Please select products to unshare');
      return;
    }

    if (!confirm(`Are you sure you want to unshare ${selectedProducts.length} product(s)?`)) {
      return;
    }

    try {
      const requests = selectedProducts.map(id => {
        const [productId, vendorId] = id.split('_');
        return { product_id: productId, vendor_id: vendorId };
      });

      const results = await productSharingApi.bulkUnshareProducts(requests);
      const successCount = results.filter(r => r.success).length;
      
      if (successCount > 0) {
        setSharedProducts(prev => prev.filter(sp => 
          !selectedProducts.includes(`${sp.product_id}_${sp.vendor_id}`)
        ));
        setSelectedProducts([]);
        alert(`Successfully unshared ${successCount} product(s)`);
        // Refresh data to update pagination
        loadData();
      } else {
        alert('Failed to unshare any products');
      }
    } catch (error) {
      console.error('Failed to bulk unshare products:', error);
      alert('Failed to unshare products');
    }
  };

  const handleShareProduct = (product: SharedProduct) => {
    if (onShareProduct) {
      onShareProduct(product);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedProducts([]); // Clear selection when changing pages
  };

  const getStatusBadge = (isActive: boolean, expiresAt?: string) => {
    if (!isActive) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <X className="h-3 w-3 mr-1" />
          Inactive
        </span>
      );
    }

    if (expiresAt && new Date(expiresAt) < new Date()) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          <AlertCircle className="h-3 w-3 mr-1" />
          Expired
        </span>
      );
    }

    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <Check className="h-3 w-3 mr-1" />
        Active
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          />
        </div>
        <select
          value={selectedVendor}
          onChange={(e) => setSelectedVendor(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm"
        >
          <option value="">All Vendors</option>
          {vendors.map(vendor => (
            <option key={vendor.id} value={vendor.id}>
              {vendor.organization_name}
            </option>
          ))}
        </select>
        <select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm"
        >
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
        <div className="flex space-x-2">
          <button
            onClick={loadData}
            className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button className="flex items-center px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedProducts.length} product(s) selected
            </span>
            <button
              onClick={handleBulkUnshare}
              className="px-3 py-1 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Unshare Selected
            </button>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedProducts(sharedProducts.map(p => `${p.product_id}_${p.vendor_id}`));
                      } else {
                        setSelectedProducts([]);
                      }
                    }}
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shared Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sharedProducts.map((product) => (
                <tr key={`${product.product_id}_${product.vendor_id}`} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      checked={selectedProducts.includes(`${product.product_id}_${product.vendor_id}`)}
                      onChange={(e) => {
                        const id = `${product.product_id}_${product.vendor_id}`;
                        if (e.target.checked) {
                          setSelectedProducts(prev => [...prev, id]);
                        } else {
                          setSelectedProducts(prev => prev.filter(p => p !== id));
                        }
                      }}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{product.product_name}</div>
                    <div className="text-sm text-gray-500">{product.product_sku}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{product.vendor_name}</div>
                    <div className="text-sm text-gray-500">
                      {product.permissions.pricing_access ? 'Pricing Access' : 'No Pricing'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center">
                        <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                        <span>{product.total_sales} sales</span>
                      </div>
                      <div className="text-xs text-gray-500">{formatCurrency(product.revenue_generated)}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(product.is_active, product.expires_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {formatDate(product.shared_at)}
                    </div>
                    {product.expires_at && (
                      <div className="text-xs text-gray-500">
                        Expires: {formatDate(product.expires_at)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => handleShareProduct(product)}
                        className="text-blue-600 hover:text-blue-900"
                        title="Share with more vendors"
                      >
                        <Share2 className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <Settings className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleUnshare(product.product_id, product.vendor_id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{((currentPage - 1) * productsPerPage) + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * productsPerPage, totalProducts)}
                </span>{' '}
                of <span className="font-medium">{totalProducts}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeft className="h-5 w-5" />
                </button>
                
                {getPageNumbers().map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      page === currentPage
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <ChevronRight className="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {sharedProducts.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No shared products found</p>
        </div>
      )}
    </div>
  );
};

export default SharedProductsTable; 
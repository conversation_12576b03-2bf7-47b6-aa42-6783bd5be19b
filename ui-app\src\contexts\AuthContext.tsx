import { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import {
  User,
  Organization,
  Workspace,
  Role,
  Permission,
  UserContext,
  UserMembership
} from '../types/auth'
import {
  mockUsers,
  mockOrganizations,
  mockWorkspaces,
  mockRoles,
  mockUserMemberships
} from '../data/mockAuthData'

// Auth State
interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  user: User | null
  currentOrganization: Organization | null
  currentWorkspace: Workspace | null
  currentRole: Role | null
  availableOrganizations: Organization[]
  availableWorkspaces: Workspace[]
  permissions: Permission[]
  memberships: UserMembership[]
  error: string | null
}

// Auth Actions
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: UserContext }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'SWITCH_WORKSPACE'; payload: { organizationId: string; workspaceId: string } }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'CLEAR_ERROR' }

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  currentOrganization: null,
  currentWorkspace: null,
  currentRole: null,
  availableOrganizations: [],
  availableWorkspaces: [],
  permissions: [],
  memberships: [],
  error: null
}

// Auth reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null
      }

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        isLoading: false,
        user: action.payload.user,
        currentOrganization: action.payload.currentOrganization,
        currentWorkspace: action.payload.currentWorkspace,
        currentRole: action.payload.currentRole,
        availableOrganizations: action.payload.availableOrganizations,
        availableWorkspaces: action.payload.availableWorkspaces,
        permissions: action.payload.permissions,
        memberships: action.payload.memberships,
        error: null
      }

    case 'AUTH_ERROR':
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        currentOrganization: null,
        currentWorkspace: null,
        currentRole: null,
        availableOrganizations: [],
        availableWorkspaces: [],
        permissions: [],
        memberships: [],
        error: action.payload
      }

    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false
      }

    case 'SWITCH_WORKSPACE':
      const { organizationId, workspaceId } = action.payload
      const newOrganization = state.availableOrganizations.find(org => org.id === organizationId)
      const newWorkspace = state.availableWorkspaces.find(ws => ws.id === workspaceId)
      const newMembership = state.memberships.find(
        m => m.organizationId === organizationId && m.workspaceId === workspaceId
      )
      const newRole = newMembership ? mockRoles.find(r => r.id === newMembership.roleId) : null

      if (!newOrganization || !newWorkspace || !newRole) {
        return {
          ...state,
          error: 'Invalid workspace selection'
        }
      }

      return {
        ...state,
        currentOrganization: newOrganization,
        currentWorkspace: newWorkspace,
        currentRole: newRole,
        permissions: newRole.permissions,
        error: null
      }

    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null
      }

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      }

    default:
      return state
  }
}

// Auth Context
interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  switchWorkspace: (organizationId: string, workspaceId: string) => void
  updateUser: (updates: Partial<User>) => void
  clearError: () => void
  hasPermission: (resource: string, action: string, scope?: string) => boolean
  hasRole: (roleId: string) => boolean
  canAccessWorkspace: (workspaceId: string) => boolean
  canAccessOrganization: (organizationId: string) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth Provider
interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Mock login function
  const login = async (email: string, _password: string) => {
    dispatch({ type: 'AUTH_START' })

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Find user by email
      const user = mockUsers.find(u => u.email === email)
      if (!user) {
        throw new Error('Invalid credentials')
      }

      // Get user memberships
      const userMemberships = mockUserMemberships.filter(m => m.userId === user.id)
      if (userMemberships.length === 0) {
        throw new Error('No workspace access')
      }

      // Get available organizations and workspaces
      const availableOrganizations = mockOrganizations.filter(org =>
        userMemberships.some(m => m.organizationId === org.id)
      )
      const availableWorkspaces = mockWorkspaces.filter(ws =>
        userMemberships.some(m => m.workspaceId === ws.id)
      )

      // Set default workspace (first one)
      const defaultMembership = userMemberships[0]
      const currentOrganization = availableOrganizations.find(
        org => org.id === defaultMembership.organizationId
      )!
      const currentWorkspace = availableWorkspaces.find(
        ws => ws.id === defaultMembership.workspaceId
      )!
      const currentRole = mockRoles.find(r => r.id === defaultMembership.roleId)!

      const userContext: UserContext = {
        user,
        currentOrganization,
        currentWorkspace,
        currentRole,
        availableOrganizations,
        availableWorkspaces,
        permissions: currentRole.permissions,
        memberships: userMemberships,
        sessionId: 'session_' + Date.now(),
        loginAt: new Date().toISOString(),
        lastActivityAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString() // 8 hours
      }

      dispatch({ type: 'AUTH_SUCCESS', payload: userContext })

      // Store session in localStorage
      localStorage.setItem('nuclues_session', JSON.stringify({
        userId: user.id,
        sessionId: userContext.sessionId,
        expiresAt: userContext.expiresAt
      }))

    } catch (error) {
      dispatch({ type: 'AUTH_ERROR', payload: error instanceof Error ? error.message : 'Login failed' })
    }
  }

  // Logout function
  const logout = () => {
    localStorage.removeItem('nuclues_session')
    dispatch({ type: 'LOGOUT' })
  }

  // Switch workspace function
  const switchWorkspace = (organizationId: string, workspaceId: string) => {
    dispatch({ type: 'SWITCH_WORKSPACE', payload: { organizationId, workspaceId } })

    // Update localStorage with new workspace
    const session = localStorage.getItem('nuclues_session')
    if (session) {
      const sessionData = JSON.parse(session)
      sessionData.currentWorkspaceId = workspaceId
      sessionData.currentOrganizationId = organizationId
      localStorage.setItem('nuclues_session', JSON.stringify(sessionData))
    }
  }

  // Update user function
  const updateUser = (updates: Partial<User>) => {
    dispatch({ type: 'UPDATE_USER', payload: updates })
  }

  // Clear error function
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' })
  }

  // Permission checking functions
  const hasPermission = (resource: string, action: string, scope?: string): boolean => {
    if (!state.isAuthenticated || !state.permissions) return false

    return state.permissions.some(permission =>
      permission.resource === resource &&
      permission.action === action &&
      (!scope || permission.scope === scope)
    )
  }

  const hasRole = (roleId: string): boolean => {
    return state.currentRole?.id === roleId
  }

  const canAccessWorkspace = (workspaceId: string): boolean => {
    return state.memberships.some(m => m.workspaceId === workspaceId && m.status === 'active')
  }

  const canAccessOrganization = (organizationId: string): boolean => {
    return state.memberships.some(m => m.organizationId === organizationId && m.status === 'active')
  }

  // Auto-login on app start (check for existing session)
  useEffect(() => {
    const checkSession = async () => {
      const session = localStorage.getItem('nuclues_session')
      if (!session) {
        // No session found - just set loading to false, don't show error
        dispatch({ type: 'LOGOUT' })
        return
      }

      try {
        const sessionData = JSON.parse(session)
        const now = new Date()
        const expiresAt = new Date(sessionData.expiresAt)

        if (now > expiresAt) {
          localStorage.removeItem('nuclues_session')
          dispatch({ type: 'LOGOUT' })
          return
        }

        // Auto-login with stored session - but avoid infinite loop
        const user = mockUsers.find(u => u.id === sessionData.userId)
        if (user && !state.isAuthenticated) {
          // Directly set auth state instead of calling login to avoid loop
          const userMemberships = mockUserMemberships.filter(m => m.userId === user.id)
          if (userMemberships.length === 0) {
            throw new Error('No workspace access')
          }

          const availableOrganizations = mockOrganizations.filter(org =>
            userMemberships.some(m => m.organizationId === org.id)
          )
          const availableWorkspaces = mockWorkspaces.filter(ws =>
            userMemberships.some(m => m.workspaceId === ws.id)
          )

          const defaultMembership = userMemberships[0]
          const currentOrganization = availableOrganizations.find(
            org => org.id === defaultMembership.organizationId
          )!
          const currentWorkspace = availableWorkspaces.find(
            ws => ws.id === defaultMembership.workspaceId
          )!
          const currentRole = mockRoles.find(r => r.id === defaultMembership.roleId)!

          const userContext: UserContext = {
            user,
            currentOrganization,
            currentWorkspace,
            currentRole,
            availableOrganizations,
            availableWorkspaces,
            permissions: currentRole.permissions,
            memberships: userMemberships,
            sessionId: sessionData.sessionId,
            loginAt: new Date().toISOString(),
            lastActivityAt: new Date().toISOString(),
            expiresAt: sessionData.expiresAt
          }

          dispatch({ type: 'AUTH_SUCCESS', payload: userContext })
        } else if (!user) {
          throw new Error('User not found')
        }
      } catch (error) {
        localStorage.removeItem('nuclues_session')
        dispatch({ type: 'LOGOUT' })
      }
    }

    checkSession()
  }, [])

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    switchWorkspace,
    updateUser,
    clearError,
    hasPermission,
    hasRole,
    canAccessWorkspace,
    canAccessOrganization
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext

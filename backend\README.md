# Hello World FastAPI Backend

A simple FastAPI application that provides "Hello World" endpoints for the Full-stack web application.

## Features

- FastAPI framework with automatic API documentation
- CORS middleware configured for frontend integration
- Multiple Hello World endpoints
- Health check endpoint
- Docker support
- UV package manager support

## API Endpoints

- `GET /` - Root hello world endpoint
- `GET /api` - API root with endpoint information
- `GET /api/hello` - Hello world with framework details
- `GET /api/hello/{name}` - Personalized hello message
- `GET /api/health` - Health check endpoint

## Quick Start

### Using UV (Recommended)

1. Install UV if you haven't already:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Install dependencies:
```bash
uv pip install -r requirements.txt
```

3. Run the application:
```bash
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Using pip

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
python main.py
```

Or using uvicorn directly:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Using Docker

1. Build the Docker image:
```bash
docker build -t hello-world-api .
```

2. Run the container:
```bash
docker run -p 8000:8000 hello-world-api
```

## Environment Variables

Copy `.env.example` to `.env` and modify as needed:

```bash
cp .env.example .env
```

Available environment variables:
- `HOST` - Server host (default: 0.0.0.0)
- `PORT` - Server port (default: 8000)
- `ENVIRONMENT` - Environment name (default: development)
- `CORS_ORIGINS` - Allowed CORS origins
- `LOG_LEVEL` - Logging level (default: info)

## API Documentation

Once the server is running, you can access:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Testing the API

Test the endpoints using curl:

```bash
# Root endpoint
curl http://localhost:8000/

# API root
curl http://localhost:8000/api

# Hello endpoint
curl http://localhost:8000/api/hello

# Personalized hello
curl http://localhost:8000/api/hello/John

# Health check
curl http://localhost:8000/api/health
```

## Frontend Integration

This API is configured to work with the frontend running on:
- http://localhost:5173 (Vite dev server)
- http://localhost:3000 (Alternative frontend port)

The frontend proxy configuration should point to http://localhost:8000 for API requests.

## Development

### Code Formatting

Install development dependencies:
```bash
uv pip install -e ".[dev]"
```

Format code:
```bash
black .
isort .
```

Lint code:
```bash
flake8 .
```

### Testing

Run tests:
```bash
pytest
```

## Project Structure

```
backend/
├── main.py              # FastAPI application
├── requirements.txt     # Python dependencies
├── pyproject.toml      # UV/pip configuration
├── Dockerfile          # Docker configuration
├── .env.example        # Environment variables template
└── README.md           # This file
```

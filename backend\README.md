# PIM API - Product Information Management Backend

A production-ready FastAPI application for Product Information Management (PIM) with PostgreSQL, SQLAlchemy, and Alembic migrations.

## Features

- **FastAPI Framework** with automatic API documentation
- **PostgreSQL Database** with SQLAlchemy ORM
- **Database Migrations** with Alembic
- **Production-ready** with proper error handling, validation, and logging
- **CORS Middleware** configured for frontend integration
- **Comprehensive Product Management** with full CRUD operations
- **Advanced Filtering & Pagination** for product listings
- **Database Health Checks** and monitoring
- **Docker Support** for containerization
- **UV Package Manager** support

## API Endpoints

### Product Management
- `GET /api/products` - List products with filtering, pagination, and sorting
- `GET /api/products/{id}` - Get specific product details
- `POST /api/products` - Create new product
- `PUT /api/products/{id}` - Update existing product
- `DELETE /api/products/{id}` - Delete product
- `POST /api/products/{id}/sync` - Sync product with external platforms

### System
- `GET /` - API information
- `GET /api/health` - Health check with database status

## Quick Start

### Prerequisites

1. **PostgreSQL Database** - Install and run PostgreSQL
2. **Python 3.8+** - Required for the application
3. **UV Package Manager** (recommended) or pip

### Database Setup

1. Create a PostgreSQL database:
```sql
CREATE DATABASE pim_db;
CREATE USER postgres WITH PASSWORD 'password';
GRANT ALL PRIVILEGES ON DATABASE pim_db TO postgres;
```

2. Update environment variables:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

### Using UV (Recommended)

1. Install UV if you haven't already:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Install dependencies:
```bash
uv pip install -r requirements.txt
```

3. Setup database:
```bash
python setup_db.py
```

4. Run database migrations (optional):
```bash
alembic upgrade head
```

5. Run the application:
```bash
python main.py
```

### Using pip

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Setup database:
```bash
python setup_db.py
```

4. Run the application:
```bash
python main.py
```

### Using Docker

1. Build the Docker image:
```bash
docker build -t pim-api .
```

2. Run with Docker Compose (includes PostgreSQL):
```bash
docker-compose up
```

## Environment Variables

Copy `.env.example` to `.env` and modify as needed:

```bash
cp .env.example .env
```

Available environment variables:
- `HOST` - Server host (default: 0.0.0.0)
- `PORT` - Server port (default: 8000)
- `ENVIRONMENT` - Environment name (default: development)
- `CORS_ORIGINS` - Allowed CORS origins
- `LOG_LEVEL` - Logging level (default: info)

## API Documentation

Once the server is running, you can access:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Testing the API

Test the endpoints using curl:

```bash
# Root endpoint
curl http://localhost:8000/

# API root
curl http://localhost:8000/api

# Hello endpoint
curl http://localhost:8000/api/hello

# Personalized hello
curl http://localhost:8000/api/hello/John

# Health check
curl http://localhost:8000/api/health
```

## Frontend Integration

This API is configured to work with the frontend running on:
- http://localhost:5173 (Vite dev server)
- http://localhost:3000 (Alternative frontend port)

The frontend proxy configuration should point to http://localhost:8000 for API requests.

## Development

### Code Formatting

Install development dependencies:
```bash
uv pip install -e ".[dev]"
```

Format code:
```bash
black .
isort .
```

Lint code:
```bash
flake8 .
```

### Testing

Run tests:
```bash
pytest
```

## Project Structure

```
backend/
├── main.py              # FastAPI application
├── requirements.txt     # Python dependencies
├── pyproject.toml      # UV/pip configuration
├── Dockerfile          # Docker configuration
├── .env.example        # Environment variables template
└── README.md           # This file
```

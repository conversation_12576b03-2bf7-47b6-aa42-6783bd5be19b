import { useState } from 'react'
import { Settings, X } from 'lucide-react'

interface Column {
  key: string
  label: string
}

interface ColumnSettingsProps {
  columns: Column[]
  visibleColumns: Record<string, boolean>
  onColumnToggle: (columnKey: string, visible: boolean) => void
  className?: string
}

const ColumnSettings = ({ 
  columns, 
  visibleColumns, 
  onColumnToggle, 
  className = '' 
}: ColumnSettingsProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleToggle = (columnKey: string, checked: boolean) => {
    onColumnToggle(columnKey, checked)
  }

  const handleReset = () => {
    // Reset to default - show all columns
    columns.forEach(column => {
      onColumnToggle(column.key, true)
    })
  }

  const visibleCount = Object.values(visibleColumns).filter(Boolean).length

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors flex items-center"
      >
        <Settings className="h-4 w-4 mr-2" />
        Columns
        <span className="ml-2 px-2 py-0.5 bg-gray-100 text-gray-600 text-xs rounded-full">
          {visibleCount}
        </span>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-2 w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-sm font-medium text-gray-900">Show Columns</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleReset}
                  className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
                >
                  Reset
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Column List */}
            <div className="p-4 space-y-3 max-h-64 overflow-y-auto">
              {columns.map((column) => (
                <label 
                  key={column.key} 
                  className="flex items-center justify-between cursor-pointer group"
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={visibleColumns[column.key] || false}
                      onChange={(e) => handleToggle(column.key, e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-3 text-sm text-gray-700 group-hover:text-gray-900 transition-colors">
                      {column.label}
                    </span>
                  </div>
                  
                  {/* Optional: Show if column is required */}
                  {column.key === 'name' && (
                    <span className="text-xs text-gray-400">Required</span>
                  )}
                </label>
              ))}
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>{visibleCount} of {columns.length} columns visible</span>
                <span>Drag to reorder (coming soon)</span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default ColumnSettings

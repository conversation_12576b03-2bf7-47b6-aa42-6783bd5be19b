import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Folder,
  File,
  Upload,
  Plus,
  Search,
  Grid,
  List,
  MoreVertical,
  Share2,
  Download,
  Trash2,
  Edit,
  Home,
  FolderPlus,
  Image,
  FileText,
  Video,
  Music,
  Archive,
  Code,
  ChevronRight,
  ChevronDown,
  Star,
  Clock,
  HardDrive,
  Users,
  Settings,
} from 'lucide-react';
import toast from 'react-hot-toast';
import { mediaApi } from '../services/mediaApi';
import { MediaItem } from '../types/media';

interface FolderTreeItem extends MediaItem {
  isExpanded?: boolean;
  children?: FolderTreeItem[];
}

const MediaPage: React.FC = () => {
  const navigate = useNavigate();
  const [items, setItems] = useState<MediaItem[]>([]);
  const [folderTree, setFolderTree] = useState<FolderTreeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [currentFolder, setCurrentFolder] = useState<string>('root');
  const [breadcrumbs, setBreadcrumbs] = useState<{ id: string; name: string }[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showCreateFolderModal, setShowCreateFolderModal] = useState(false);
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([]);

  // Load items and folder tree when component mounts
  useEffect(() => {
    loadItems();
    loadFolderTree();
  }, [currentFolder]);

  const loadItems = async () => {
    try {
      setLoading(true);
      const data = await mediaApi.getItems({ parent_id: currentFolder });
      setItems(data);
    } catch (error) {
      toast.error('Failed to load media items');
    } finally {
      setLoading(false);
    }
  };

  const loadFolderTree = async () => {
    try {
      const folders = await mediaApi.getItemsByType('folder');
      const buildTree = (items: MediaItem[], parentId: string | null = null): FolderTreeItem[] => {
        return items
          .filter(item => item.parent_id === parentId)
          .map(item => ({
            ...item,
            isExpanded: false,
            children: buildTree(items, item.id)
          }));
      };
      setFolderTree(buildTree(folders));
    } catch (error) {
      console.error('Failed to load folder tree:', error);
    }
  };

  const handleItemClick = (item: MediaItem) => {
    if (item.item_type === 'folder') {
      setCurrentFolder(item.id);
      setBreadcrumbs([...breadcrumbs, { id: item.id, name: item.name }]);
      setSelectedItems([]);
    } else {
      window.open(mediaApi.getDownloadUrl(item.id), '_blank');
    }
  };

  const handleFolderTreeClick = (folder: FolderTreeItem) => {
    setCurrentFolder(folder.id);
    setBreadcrumbs([{ id: folder.id, name: folder.name }]);
    setSelectedItems([]);
  };

  const toggleFolderExpansion = (folderId: string) => {
    setFolderTree(prev => {
      const updateFolder = (folders: FolderTreeItem[]): FolderTreeItem[] => {
        return folders.map(folder => {
          if (folder.id === folderId) {
            return { ...folder, isExpanded: !folder.isExpanded };
          }
          if (folder.children) {
            return { ...folder, children: updateFolder(folder.children) };
          }
          return folder;
        });
      };
      return updateFolder(prev);
    });
  };

  const handleBreadcrumbClick = (index: number) => {
    const newBreadcrumbs = breadcrumbs.slice(0, index + 1);
    setBreadcrumbs(newBreadcrumbs);
    setCurrentFolder(newBreadcrumbs[newBreadcrumbs.length - 1]?.id || 'root');
    setSelectedItems([]);
  };

  const handleCreateFolder = async () => {
    const name = prompt('Enter folder name:');
    if (!name) return;

    try {
      await mediaApi.createFolder({
        name,
        item_type: 'folder',
        parent_id: currentFolder,
      });
      toast.success('Folder created successfully');
      loadItems();
      loadFolderTree();
    } catch (error) {
      toast.error('Failed to create folder');
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    for (const file of Array.from(files)) {
      try {
        setUploadingFiles(prev => [...prev, file.name]);
        
        // Create file record
        const fileRecord = await mediaApi.createFile({
          name: file.name,
          item_type: 'file',
          parent_id: currentFolder,
        });

        // Upload file content
        await mediaApi.uploadFile(fileRecord.id, file);
        
        setUploadingFiles(prev => prev.filter(name => name !== file.name));
        toast.success(`${file.name} uploaded successfully`);
      } catch (error) {
        setUploadingFiles(prev => prev.filter(name => name !== file.name));
        toast.error(`Failed to upload ${file.name}`);
      }
    }

    loadItems();
  };

  const handleDeleteItems = async () => {
    if (selectedItems.length === 0) return;

    const confirmed = confirm(`Are you sure you want to delete ${selectedItems.length} item(s)?`);
    if (!confirmed) return;

    try {
      await Promise.all(selectedItems.map(id => mediaApi.deleteItem(id)));
      toast.success(`${selectedItems.length} item(s) deleted successfully`);
      setSelectedItems([]);
      loadItems();
      loadFolderTree();
    } catch (error) {
      toast.error('Failed to delete items');
    }
  };

  const getFileIcon = (mimeType?: string) => {
    if (!mimeType) return <File className="w-6 h-6" />;
    
    if (mimeType.startsWith('image/')) return <Image className="w-6 h-6" />;
    if (mimeType.startsWith('video/')) return <Video className="w-6 h-6" />;
    if (mimeType.startsWith('audio/')) return <Music className="w-6 h-6" />;
    if (mimeType.includes('pdf')) return <FileText className="w-6 h-6" />;
    if (mimeType.includes('zip') || mimeType.includes('rar')) return <Archive className="w-6 h-6" />;
    if (mimeType.includes('json') || mimeType.includes('xml')) return <Code className="w-6 h-6" />;
    
    return <File className="w-6 h-6" />;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // Only show items (files and folders) that are direct children of the current folder
  const visibleItems = items.filter(item => item.parent_id === currentFolder);
  const filteredItems = visibleItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Recursive function to render the complete folder tree in the sidebar
  const renderSidebarFolderTree = (folders: MediaItem[], parentId: string | null = 'root', level = 0) => {
    return folders
      .filter(folder => folder.item_type === 'folder' && folder.parent_id === parentId)
      .map(folder => (
        <div key={folder.id} style={{ paddingLeft: `${level * 16}px` }} className="py-1">
          <div
            className={`flex items-center cursor-pointer hover:bg-gray-100 rounded px-2 py-1 ${currentFolder === folder.id ? 'bg-blue-100' : ''}`}
            onClick={() => {
              setCurrentFolder(folder.id);
              setBreadcrumbs([{ id: folder.id, name: folder.name }]);
              setSelectedItems([]);
            }}
          >
            <Folder className="w-4 h-4 mr-2 text-blue-500" />
            <span className="truncate">{folder.name}</span>
          </div>
          {renderSidebarFolderTree(folders, folder.id, level + 1)}
        </div>
      ));
  };

  const renderFileGrid = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
      {filteredItems.map((item) => (
        <div
          key={item.id}
          className={`group relative bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer ${
            selectedItems.includes(item.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''
          }`}
          onClick={() => handleItemClick(item)}
        >
          <input
            type="checkbox"
            checked={selectedItems.includes(item.id)}
            onChange={(e) => {
              e.stopPropagation();
              if (e.target.checked) {
                setSelectedItems([...selectedItems, item.id]);
              } else {
                setSelectedItems(selectedItems.filter(id => id !== item.id));
              }
            }}
            className="absolute top-2 left-2 z-10"
          />

          <div className="flex flex-col items-center text-center">
            <div className="relative mb-2">
              {item.item_type === 'folder' ? (
                <Folder className="w-12 h-12 text-blue-500" />
              ) : (
                getFileIcon(item.mime_type)
              )}
              {uploadingFiles.includes(item.name) && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>
            
            <div className="w-full">
              <p className="text-sm font-medium text-gray-900 truncate" title={item.name}>
                {item.name}
              </p>
              {item.item_type === 'file' && (
                <p className="text-xs text-gray-500">
                  {formatFileSize(item.size)}
                </p>
              )}
              <p className="text-xs text-gray-400">
                {new Date(item.updated_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderFileList = () => (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Size
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Modified
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredItems.map((item) => (
              <tr
                key={item.id}
                className={`hover:bg-gray-50 cursor-pointer ${
                  selectedItems.includes(item.id) ? 'bg-blue-50' : ''
                }`}
                onClick={() => handleItemClick(item)}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(item.id)}
                      onChange={(e) => {
                        e.stopPropagation();
                        if (e.target.checked) {
                          setSelectedItems([...selectedItems, item.id]);
                        } else {
                          setSelectedItems(selectedItems.filter(id => id !== item.id));
                        }
                      }}
                      className="mr-3"
                    />
                    <div className="flex items-center">
                      {item.item_type === 'folder' ? (
                        <Folder className="w-5 h-5 text-blue-500 mr-3" />
                      ) : (
                        <div className="mr-3">{getFileIcon(item.mime_type)}</div>
                      )}
                      <span className="text-sm font-medium text-gray-900">{item.name}</span>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {item.item_type === 'folder' ? 'Folder' : item.mime_type || 'File'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {item.item_type === 'folder' ? '-' : formatFileSize(item.size)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(item.updated_at).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div className="flex space-x-2">
                    {item.item_type === 'file' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(mediaApi.getDownloadUrl(item.id), '_blank');
                        }}
                        className="text-gray-400 hover:text-gray-600"
                        title="Download"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedItems([item.id]);
                      }}
                      className="text-gray-400 hover:text-gray-600"
                      title="More"
                    >
                      <MoreVertical className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="-m-6 min-h-screen bg-gray-50">
      <div className="flex">
        {/* Left Sidebar - Folder Tree */}
        <div className="w-72 bg-white border-r border-gray-200 min-h-screen">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center">
              <HardDrive className="h-7 w-7 text-blue-600 mr-3" />
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Media</h2>
                <p className="text-sm text-gray-500">Manage your files and folders</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="p-4 border-b border-gray-100">
            <div className="space-y-2">
              <button
                onClick={() => {
                  setCurrentFolder('root');
                  setBreadcrumbs([]);
                  setSelectedItems([]);
                }}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentFolder === 'root' ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Home className="w-4 h-4 mr-3" />
                My Drive
              </button>
              <button
                onClick={() => setShowCreateFolderModal(true)}
                className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <FolderPlus className="w-4 h-4 mr-3" />
                New Folder
              </button>
              <label className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer">
                <Upload className="w-4 h-4 mr-3" />
                Upload Files
                <input
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          </div>

          {/* Folder Tree */}
          <div className="p-4">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              FOLDERS
            </h3>
            <div className="space-y-1">
              {renderSidebarFolderTree(items)}
            </div>
          </div>
        </div>

        {/* Right Content Area */}
        <div className="flex-1">
          <div className="p-8">
            {/* Header with Breadcrumbs and Actions */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      setCurrentFolder('root');
                      setBreadcrumbs([]);
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  >
                    <Home className="w-5 h-5" />
                  </button>
                  {breadcrumbs.map((crumb, index) => (
                    <React.Fragment key={crumb.id}>
                      <span className="text-gray-400">/</span>
                      <button
                        onClick={() => handleBreadcrumbClick(index)}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        {crumb.name}
                      </button>
                    </React.Fragment>
                  ))}
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowCreateFolderModal(true)}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <FolderPlus className="w-4 h-4" />
                    <span>New Folder</span>
                  </button>
                  <label className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer">
                    <Upload className="w-4 h-4" />
                    <span>Upload</span>
                    <input
                      type="file"
                      multiple
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>

              {/* Toolbar */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search files and folders..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg ${
                      viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg ${
                      viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>

                  {selectedItems.length > 0 && (
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={handleDeleteItems}
                        className="flex items-center space-x-1 px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                      >
                        <Trash2 className="w-3 h-3" />
                        <span>Delete</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Content */}
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div>
                {viewMode === 'grid' ? renderFileGrid() : renderFileList()}

                {!loading && filteredItems.length === 0 && (
                  <div className="text-center py-12">
                    <Folder className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No files or folders</h3>
                    <p className="text-gray-500 mb-4">
                      {searchQuery ? 'No items match your search.' : 'Get started by creating a folder or uploading files.'}
                    </p>
                    <div className="flex items-center justify-center space-x-4">
                      <button
                        onClick={handleCreateFolder}
                        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                      >
                        <FolderPlus className="w-4 h-4" />
                        <span>Create Folder</span>
                      </button>
                      <label className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer">
                        <Upload className="w-4 h-4" />
                        <span>Upload Files</span>
                        <input
                          type="file"
                          multiple
                          onChange={handleFileUpload}
                          className="hidden"
                        />
                      </label>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaPage; 
import {
  Product,
  ProductListItem,
  ProductListResult,
  ProductCategory,
  CreateProductRequest,
  UpdateProductRequest,
  ProductQueryParams
} from '../types/product'

// Simulate network delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// Mock data
const mockProductData: ProductListItem[] = [
  {
    id: '1',
    name: 'Wireless Bluetooth Headphones Pro',
    sku: 'WBH-PRO-001',
    price: 199.99,
    inventory_level: 45,
    is_visible: true,
    is_featured: true,
    sync_status: 'synced',
    last_sync_at: '2024-01-15T14:30:00Z',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-15T14:30:00Z'
  },
  {
    id: '2',
    name: 'Smart Fitness Watch Series 5',
    sku: 'SFW-S5-002',
    price: 299.99,
    inventory_level: 23,
    is_visible: true,
    is_featured: false,
    sync_status: 'pending',
    last_sync_at: '2024-01-14T12:15:00Z',
    created_at: '2024-01-02T10:00:00Z',
    updated_at: '2024-01-14T12:15:00Z'
  },
  {
    id: '3',
    name: 'Organic Cotton T-Shirt Premium',
    sku: 'OCT-PREM-003',
    price: 39.99,
    inventory_level: 0,
    is_visible: false,
    is_featured: false,
    sync_status: 'error',
    last_sync_at: '2024-01-13T16:45:00Z',
    created_at: '2024-01-03T10:00:00Z',
    updated_at: '2024-01-13T16:45:00Z'
  }
]

const mockCategories: ProductCategory[] = [
  {
    id: '1',
    name: 'Electronics',
    description: 'Electronic devices and accessories',
    sort_order: 1,
    is_visible: true,
    created_at: '2023-06-01T10:00:00Z'
  },
  {
    id: '2',
    name: 'Clothing',
    description: 'Apparel and fashion items',
    sort_order: 2,
    is_visible: true,
    created_at: '2023-06-01T10:00:00Z'
  },
  {
    id: '3',
    name: 'Food & Beverages',
    description: 'Food and drink products',
    sort_order: 3,
    is_visible: true,
    created_at: '2023-06-01T10:00:00Z'
  }
]

export const productApi = {
  // Get all products with filtering and pagination
  getProducts: async (params?: ProductQueryParams): Promise<ProductListResult> => {
    await delay()
    
    let filteredProducts = [...mockProductData]

    // Apply filters
    if (params?.search) {
      const search = params.search.toLowerCase()
      filteredProducts = filteredProducts.filter((p: ProductListItem) =>
        p.name.toLowerCase().includes(search) ||
        p.sku?.toLowerCase().includes(search)
      )
    }

    if (params?.is_visible !== undefined) {
      filteredProducts = filteredProducts.filter((p: ProductListItem) => p.is_visible === params.is_visible)
    }

    if (params?.is_featured !== undefined) {
      filteredProducts = filteredProducts.filter((p: ProductListItem) => p.is_featured === params.is_featured)
    }

    if (params?.sync_status) {
      filteredProducts = filteredProducts.filter((p: ProductListItem) => p.sync_status === params.sync_status)
    }

    // Apply sorting
    if (params?.sort_by) {
      const sortBy = params.sort_by as keyof ProductListItem
      const sortOrder = params.sort_order === 'ASC' ? 1 : -1

      filteredProducts.sort((a, b) => {
        const aVal = a[sortBy]
        const bVal = b[sortBy]

        if (typeof aVal === 'string' && typeof bVal === 'string') {
          return aVal.localeCompare(bVal) * sortOrder
        }

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return (aVal - bVal) * sortOrder
        }

        return 0
      })
    }

    // Apply pagination
    const page = params?.page || 1
    const limit = params?.limit || 20
    const offset = (page - 1) * limit
    const paginatedProducts = filteredProducts.slice(offset, offset + limit)

    return {
      products: paginatedProducts,
      total: filteredProducts.length,
      page,
      limit,
      total_pages: Math.ceil(filteredProducts.length / limit)
    }
  },

  // Get a single product by ID
  getProduct: async (id: string): Promise<Product> => {
    await delay()
    const product = mockProductData.find((p: ProductListItem) => p.id === id)
    if (!product) {
      throw new Error('Product not found')
    }
    
    // Convert ProductListItem to full Product
    const fullProduct: Product = {
      ...product,
      product_type: 'physical',
      description: 'This is a sample product description.',
      weight: 1.5,
      width: 10,
      depth: 8,
      height: 3,
      cost_price: product.price * 0.6,
      retail_price: product.price * 1.2,
      inventory_tracking: 'simple',
      is_free_shipping: false,
      warranty: '1 year manufacturer warranty',
      availability: 'available',
      gift_wrapping_options_type: 'any',
      condition: 'New',
      is_condition_shown: true,
      view_count: Math.floor(Math.random() * 1000),
      is_preorder_only: false,
      is_price_hidden: false,
      open_graph_type: 'product',
      open_graph_use_meta_description: true,
      open_graph_use_product_name: true,
      open_graph_use_image: true,
      total_sold: Math.floor(Math.random() * 100),
      categories: [],
      images: [],
      videos: [],
      variants: [],
      custom_fields: [],
      bulk_pricing_rules: []
    }
    
    return fullProduct
  },

  // Create a new product
  createProduct: async (product: CreateProductRequest): Promise<Product> => {
    await delay()
    const newProduct: Product = {
      id: `prod_${Date.now()}`,
      ...product,
      view_count: 0,
      total_sold: 0,
      sync_status: 'not_synced',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      categories: [],
      images: [],
      videos: [],
      variants: [],
      custom_fields: [],
      bulk_pricing_rules: []
    }
    mockProductData.push({
      id: newProduct.id,
      name: newProduct.name,
      sku: newProduct.sku,
      price: newProduct.price,
      inventory_level: newProduct.inventory_level,
      is_visible: newProduct.is_visible,
      is_featured: newProduct.is_featured,
      sync_status: newProduct.sync_status,
      created_at: newProduct.created_at,
      updated_at: newProduct.updated_at
    })
    return newProduct
  },

  // Update an existing product
  updateProduct: async (id: string, updates: UpdateProductRequest): Promise<Product> => {
    await delay()
    const productIndex = mockProductData.findIndex((p: ProductListItem) => p.id === id)
    if (productIndex === -1) {
      throw new Error('Product not found')
    }
    
    mockProductData[productIndex] = {
      ...mockProductData[productIndex],
      ...updates,
      updated_at: new Date().toISOString()
    }
    
    return await productApi.getProduct(id)
  },

  // Delete a product
  deleteProduct: async (id: string): Promise<void> => {
    await delay()
    const productIndex = mockProductData.findIndex((p: ProductListItem) => p.id === id)
    if (productIndex === -1) {
      throw new Error('Product not found')
    }
    mockProductData.splice(productIndex, 1)
  },

  // Sync product with external platform
  syncProduct: async (id: string): Promise<{ success: boolean; message: string }> => {
    await delay(2000) // Simulate longer sync time
    const product = mockProductData.find((p: ProductListItem) => p.id === id)
    if (!product) {
      throw new Error('Product not found')
    }
    
    // Simulate sync success
    return { success: true, message: 'Product synced successfully' }
  },

  // Get product categories
  getCategories: async (): Promise<ProductCategory[]> => {
    await delay()
    return mockCategories
  },

  // Bulk operations
  bulkDelete: async (productIds: string[]): Promise<{ success: boolean; deleted: number }> => {
    await delay(1000)
    let deleted = 0
    for (const id of productIds) {
      const index = mockProductData.findIndex((p: ProductListItem) => p.id === id)
      if (index !== -1) {
        mockProductData.splice(index, 1)
        deleted++
      }
    }
    return { success: true, deleted }
  },

  bulkSync: async (productIds: string[]): Promise<{ success: boolean; synced: number; message: string }> => {
    await delay(3000) // Simulate longer bulk sync time
    const synced = Math.min(productIds.length, Math.floor(Math.random() * productIds.length) + 1)
    return { success: true, synced, message: `Successfully synced ${synced} products` }
  }
}

// Mock API service for development (keeping for backward compatibility)
export const mockProductApi = {
  getProducts: async (params?: ProductQueryParams): Promise<ProductListResult> => {
    return productApi.getProducts(params)
  },

  getProduct: async (id: string): Promise<Product> => {
    return productApi.getProduct(id)
  },

  createProduct: async (product: CreateProductRequest): Promise<Product> => {
    return productApi.createProduct(product)
  },

  updateProduct: async (id: string, updates: UpdateProductRequest): Promise<Product> => {
    return productApi.updateProduct(id, updates)
  },

  deleteProduct: async (id: string): Promise<void> => {
    return productApi.deleteProduct(id)
  },

  syncProduct: async (id: string): Promise<{ success: boolean; message: string }> => {
    return productApi.syncProduct(id)
  },

  getCategories: async (): Promise<ProductCategory[]> => {
    return productApi.getCategories()
  },

  bulkDelete: async (productIds: string[]): Promise<{ success: boolean; deleted: number }> => {
    return productApi.bulkDelete(productIds)
  },

  bulkSync: async (productIds: string[]): Promise<{ success: boolean; synced: number; message: string }> => {
    return productApi.bulkSync(productIds)
  }
}

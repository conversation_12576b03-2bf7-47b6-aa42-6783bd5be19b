import { useState } from 'react'
import { 
  Play, 
  Pause, 
  MoreVertical, 
  Clock, 
  Calendar,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Edit,
  Trash2,
  Copy
} from 'lucide-react'

interface Job {
  id: string
  name: string
  description: string
  app: string
  status: 'running' | 'paused' | 'error' | 'completed'
  schedule: string
  lastRun?: string
  nextRun?: string
  duration?: string
  successRate?: number
  totalRuns?: number
}

interface JobsListProps {
  jobs: Job[]
  onToggleJob: (jobId: string, action: 'play' | 'pause') => void
  onEditJob: (jobId: string) => void
  onDeleteJob: (jobId: string) => void
  onDuplicateJob: (jobId: string) => void
}

const JobsList = ({ jobs, onToggleJob, onEditJob, onDeleteJob, onDuplicateJob }: JobsListProps) => {
  const [selectedJob, setSelectedJob] = useState<string | null>(null)
  const [showDropdown, setShowDropdown] = useState<string | null>(null)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Play className="h-4 w-4 text-green-500" />
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded-full" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'paused':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'completed':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600'
    if (rate >= 80) return 'text-yellow-600'
    return 'text-red-600'
  }

  const handleDropdownToggle = (jobId: string) => {
    setShowDropdown(showDropdown === jobId ? null : jobId)
  }

  const handleAction = (action: string, jobId: string) => {
    setShowDropdown(null)
    switch (action) {
      case 'edit':
        onEditJob(jobId)
        break
      case 'delete':
        onDeleteJob(jobId)
        break
      case 'duplicate':
        onDuplicateJob(jobId)
        break
    }
  }

  return (
    <div className="space-y-4">
      {jobs.map((job) => (
        <div 
          key={job.id} 
          className={`card hover:shadow-md transition-all duration-200 ${
            selectedJob === job.id ? 'ring-2 ring-primary-500' : ''
          }`}
          onClick={() => setSelectedJob(selectedJob === job.id ? null : job.id)}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {getStatusIcon(job.status)}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-medium text-gray-900 truncate">
                    {job.name}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">{job.description}</p>
                  <div className="flex items-center space-x-4 mt-2">
                    <span className="text-xs text-gray-400">App: {job.app}</span>
                    <span className={`text-xs font-medium px-2 py-1 rounded-full border ${getStatusColor(job.status)}`}>
                      {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 ml-4">
              {/* Quick Action Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onToggleJob(job.id, job.status === 'running' ? 'pause' : 'play')
                }}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  job.status === 'running'
                    ? 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200'
                    : 'bg-green-100 text-green-600 hover:bg-green-200'
                }`}
                title={job.status === 'running' ? 'Pause Job' : 'Start Job'}
              >
                {job.status === 'running' ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </button>
              
              {/* More Actions Dropdown */}
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDropdownToggle(job.id)
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                >
                  <MoreVertical className="h-4 w-4" />
                </button>
                
                {showDropdown === job.id && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                    <div className="py-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleAction('edit', job.id)
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Job
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleAction('duplicate', job.id)
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleAction('delete', job.id)
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Expanded Details */}
          {selectedJob === job.id && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-900 flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    Schedule
                  </h4>
                  <p className="text-sm text-gray-600">{job.schedule}</p>
                  <p className="text-xs text-gray-500">Next: {job.nextRun}</p>
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-900 flex items-center">
                    <Activity className="h-4 w-4 mr-1" />
                    Performance
                  </h4>
                  <p className="text-sm text-gray-600">
                    Last run: {job.lastRun} ({job.duration})
                  </p>
                  {job.successRate !== undefined && (
                    <p className={`text-xs font-medium ${getSuccessRateColor(job.successRate)}`}>
                      Success rate: {job.successRate}% ({job.totalRuns} runs)
                    </p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-900 flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Status
                  </h4>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(job.status)}
                    <span className="text-sm text-gray-600">
                      {job.status === 'running' ? 'Currently running' : 
                       job.status === 'paused' ? 'Manually paused' :
                       job.status === 'error' ? 'Last run failed' :
                       'Completed successfully'}
                    </span>
                  </div>
                  {job.status === 'error' && (
                    <div className="flex items-center text-xs text-red-600">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Check logs for details
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      ))}
      
      {jobs.length === 0 && (
        <div className="text-center py-12">
          <Clock className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No jobs configured</h3>
          <p className="mt-1 text-sm text-gray-500">
            Create your first sync job to get started.
          </p>
        </div>
      )}
    </div>
  )
}

export default JobsList

{"hash": "677f5cd0", "browserHash": "a13fe73c", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5fdcf169", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "4f2c0e98", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3843341d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "318b8c26", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "e43f5a89", "needsInterop": false}, "@tiptap/extension-image": {"src": "../../@tiptap/extension-image/dist/index.js", "file": "@tiptap_extension-image.js", "fileHash": "efbd31db", "needsInterop": false}, "@tiptap/extension-link": {"src": "../../@tiptap/extension-link/dist/index.js", "file": "@tiptap_extension-link.js", "fileHash": "5b790fd4", "needsInterop": false}, "@tiptap/extension-table": {"src": "../../@tiptap/extension-table/dist/index.js", "file": "@tiptap_extension-table.js", "fileHash": "a31ccc06", "needsInterop": false}, "@tiptap/extension-table-cell": {"src": "../../@tiptap/extension-table-cell/dist/index.js", "file": "@tiptap_extension-table-cell.js", "fileHash": "cb5abba9", "needsInterop": false}, "@tiptap/extension-table-header": {"src": "../../@tiptap/extension-table-header/dist/index.js", "file": "@tiptap_extension-table-header.js", "fileHash": "a7523fdb", "needsInterop": false}, "@tiptap/extension-table-row": {"src": "../../@tiptap/extension-table-row/dist/index.js", "file": "@tiptap_extension-table-row.js", "fileHash": "b688422c", "needsInterop": false}, "@tiptap/extension-text-align": {"src": "../../@tiptap/extension-text-align/dist/index.js", "file": "@tiptap_extension-text-align.js", "fileHash": "1699ca56", "needsInterop": false}, "@tiptap/extension-underline": {"src": "../../@tiptap/extension-underline/dist/index.js", "file": "@tiptap_extension-underline.js", "fileHash": "6c8ac4a5", "needsInterop": false}, "@tiptap/react": {"src": "../../@tiptap/react/dist/index.js", "file": "@tiptap_react.js", "fileHash": "6f148d2c", "needsInterop": false}, "@tiptap/starter-kit": {"src": "../../@tiptap/starter-kit/dist/index.js", "file": "@tiptap_starter-kit.js", "fileHash": "949b98fe", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "a06b8693", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "80ba3012", "needsInterop": false}, "msw": {"src": "../../msw/lib/core/index.mjs", "file": "msw.js", "fileHash": "9614d81d", "needsInterop": false}, "msw/browser": {"src": "../../msw/lib/browser/index.mjs", "file": "msw_browser.js", "fileHash": "21fc19ac", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "6e5e98ab", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "3d9578be", "needsInterop": false}, "react-query": {"src": "../../react-query/es/index.js", "file": "react-query.js", "fileHash": "28aa9a52", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "1287e308", "needsInterop": false}, "uuid": {"src": "../../uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "5b0c94a7", "needsInterop": false}}, "chunks": {"graphql-QUIK33JG": {"file": "graphql-QUIK33JG.js"}, "chunk-CLL3IW5R": {"file": "chunk-CLL3IW5R.js"}, "chunk-ZAOE3R7A": {"file": "chunk-ZAOE3R7A.js"}, "chunk-6UPLEOQD": {"file": "chunk-6UPLEOQD.js"}, "chunk-KKDYDOAG": {"file": "chunk-KKDYDOAG.js"}, "chunk-6VRH24VT": {"file": "chunk-6VRH24VT.js"}, "chunk-JVDJUXGV": {"file": "chunk-JVDJUXGV.js"}, "chunk-73FTDTAA": {"file": "chunk-73FTDTAA.js"}, "chunk-6G6A32TE": {"file": "chunk-6G6A32TE.js"}, "chunk-IGLCBFGR": {"file": "chunk-IGLCBFGR.js"}}}
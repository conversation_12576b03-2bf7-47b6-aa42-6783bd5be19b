import { useState } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  ArrowLeft, Package, User, CreditCard, Truck, FileText,
  Printer, Download, Edit, Clock, CheckCircle, XCircle,
  Mail, Phone, MapPin, DollarSign, Plus, Eye, MoreHorizontal,
  Copy, ExternalLink, Upload, Calendar, Building, UserCheck,
  Clipboard, AlertCircle, TrendingUp, Archive, Settings,
  Receipt, Tag, Globe, Shield
} from 'lucide-react'

// Mock Shopify order data
const mockShopifyOrder = {
  id: *********,
  admin_graphql_api_id: "gid://shopify/Order/*********",
  app_id: null,
  browser_ip: "0.0.0.0",
  buyer_accepts_marketing: false,
  cancel_reason: null,
  cancelled_at: null,
  cart_token: "68778783ad298f1c80c3bafcddeea02f",
  checkout_id: 901414060,
  checkout_token: "1ce5a3a0bfb1c53b505c5bcf424bc5d4",
  closed_at: null,
  confirmed: true,
  contact_email: "<EMAIL>",
  created_at: "2008-01-10T11:00:00-05:00",
  currency: "USD",
  current_subtotal_price: "195.67",
  current_subtotal_price_set: {
    shop_money: {
      amount: "195.67",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "195.67",
      currency_code: "USD"
    }
  },
  current_total_discounts: "3.33",
  current_total_discounts_set: {
    shop_money: {
      amount: "3.33",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "3.33",
      currency_code: "USD"
    }
  },
  current_total_duties_set: null,
  current_total_price: "199.65",
  current_total_price_set: {
    shop_money: {
      amount: "199.65",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "199.65",
      currency_code: "USD"
    }
  },
  current_total_tax: "3.98",
  current_total_tax_set: {
    shop_money: {
      amount: "3.98",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "3.98",
      currency_code: "USD"
    }
  },
  customer_locale: null,
  device_id: null,
  discount_codes: [],
  email: "<EMAIL>",
  estimated_taxes: false,
  financial_status: "partially_refunded",
  fulfillment_status: null,
  gateway: "authorize_net",
  landing_site: "http://www.example.com?source=abc",
  landing_site_ref: "abc",
  location_id: null,
  name: "#1001",
  note: "",
  note_attributes: [
    {
      name: "custom engraving",
      value: "Happy Birthday"
    },
    {
      name: "colour",
      value: "green"
    }
  ],
  number: 1,
  order_number: 1001,
  order_status_url: "https://apple.myshopify.com/*********/orders/b1946ac92492d2347c6235b4d2611184/authenticate?key=imasecretipod",
  original_total_duties_set: null,
  payment_gateway_names: [
    "bogus"
  ],
  phone: "+557734881234",
  presentment_currency: "USD",
  processed_at: "2008-01-10T11:00:00-05:00",
  processing_method: "direct",
  reference: "fhwdgads",
  referring_site: "http://www.otherexample.com",
  source_identifier: "fhwdgads",
  source_name: "web",
  source_url: null,
  subtotal_price: "597.00",
  subtotal_price_set: {
    shop_money: {
      amount: "597.00",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "597.00",
      currency_code: "USD"
    }
  },
  tags: "",
  tax_lines: [
    {
      price: "11.94",
      rate: 0.06,
      title: "State Tax",
      price_set: {
        shop_money: {
          amount: "11.94",
          currency_code: "USD"
        },
        presentment_money: {
          amount: "11.94",
          currency_code: "USD"
        }
      }
    }
  ],
  taxes_included: false,
  test: false,
  token: "b1946ac92492d2347c6235b4d2611184",
  total_discounts: "0.00",
  total_discounts_set: {
    shop_money: {
      amount: "0.00",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "0.00",
      currency_code: "USD"
    }
  },
  total_line_items_price: "597.00",
  total_line_items_price_set: {
    shop_money: {
      amount: "597.00",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "597.00",
      currency_code: "USD"
    }
  },
  total_outstanding: "0.00",
  total_price: "608.94",
  total_price_set: {
    shop_money: {
      amount: "608.94",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "608.94",
      currency_code: "USD"
    }
  },
  total_price_usd: "608.94",
  total_shipping_price_set: {
    shop_money: {
      amount: "0.00",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "0.00",
      currency_code: "USD"
    }
  },
  total_tax: "11.94",
  total_tax_set: {
    shop_money: {
      amount: "11.94",
      currency_code: "USD"
    },
    presentment_money: {
      amount: "11.94",
      currency_code: "USD"
    }
  },
  total_tip_received: "0.00",
  total_weight: 0,
  updated_at: "2008-01-10T11:00:00-05:00",
  user_id: null,
  billing_address: {
    first_name: "Bob",
    address1: "Chestnut Street 92",
    phone: "+1**************",
    city: "Louisville",
    zip: "40202",
    province: "Kentucky",
    country: "United States",
    last_name: "Norman",
    address2: "",
    company: null,
    latitude: 45.41634,
    longitude: -75.6868,
    name: "Bob Norman",
    country_code: "US",
    province_code: "KY"
  },
  customer: {
    id: *********,
    email: "<EMAIL>",
    accepts_marketing: false,
    created_at: "2021-12-31T19:00:00-05:00",
    updated_at: "2021-12-31T19:00:00-05:00",
    first_name: "Jon",
    last_name: "Snow",
    orders_count: 1,
    state: "disabled",
    total_spent: "0.00",
    last_order_id: *********,
    note: null,
    verified_email: true,
    multipass_identifier: null,
    tax_exempt: false,
    phone: "+16136120707",
    tags: "",
    last_order_name: "#1001",
    currency: "USD",
    accepts_marketing_updated_at: "2005-06-12T11:57:11-04:00",
    marketing_opt_in_level: null,
    tax_exemptions: [],
    admin_graphql_api_id: "gid://shopify/Customer/*********",
    default_address: {
      id: *********,
      customer_id: *********,
      first_name: null,
      last_name: null,
      company: null,
      address1: "Chestnut Street 92",
      address2: "",
      city: "Louisville",
      province: "Kentucky",
      country: "United States",
      zip: "40202",
      phone: "+1**************",
      name: "",
      province_code: "KY",
      country_code: "US",
      country_name: "United States",
      default: true
    }
  },
  discount_applications: [],
  fulfillments: [],
  line_items: [
    {
      id: *********,
      admin_graphql_api_id: "gid://shopify/LineItem/*********",
      fulfillable_quantity: 0,
      fulfillment_service: "manual",
      fulfillment_status: null,
      gift_card: false,
      grams: 200,
      name: "IPod Nano - 8gb - green",
      origin_location: {
        id: *********,
        country_code: "DE",
        province_code: "",
        name: "Apple",
        address1: "Muster Str. 1",
        address2: "",
        city: "Musterhausen",
        zip: "12345"
      },
      price: "199.00",
      price_set: {
        shop_money: {
          amount: "199.00",
          currency_code: "USD"
        },
        presentment_money: {
          amount: "199.00",
          currency_code: "USD"
        }
      },
      product_exists: true,
      product_id: *********,
      properties: [
        {
          name: "Custom Engraving Front",
          value: "Happy Birthday"
        },
        {
          name: "Custom Engraving Back",
          value: "Merry Christmas"
        }
      ],
      quantity: 1,
      requires_shipping: true,
      sku: "IPOD2008GREEN",
      taxable: true,
      title: "IPod Nano - 8gb",
      total_discount: "0.00",
      total_discount_set: {
        shop_money: {
          amount: "0.00",
          currency_code: "USD"
        },
        presentment_money: {
          amount: "0.00",
          currency_code: "USD"
        }
      },
      variant_id: 39072856,
      variant_inventory_management: "shopify",
      variant_title: "green",
      vendor: null,
      tax_lines: [
        {
          channel_liable: null,
          price: "3.98",
          price_set: {
            shop_money: {
              amount: "3.98",
              currency_code: "USD"
            },
            presentment_money: {
              amount: "3.98",
              currency_code: "USD"
            }
          },
          rate: 0.06,
          title: "State Tax"
        }
      ],
      duties: [],
      discount_allocations: []
    }
  ],
  payment_terms: null,
  refunds: [],
  shipping_address: {
    first_name: "Bob",
    address1: "Chestnut Street 92",
    phone: "+1**************",
    city: "Louisville",
    zip: "40202",
    province: "Kentucky",
    country: "United States",
    last_name: "Norman",
    address2: "",
    company: null,
    latitude: 45.41634,
    longitude: -75.6868,
    name: "Bob Norman",
    country_code: "US",
    province_code: "KY"
  },
  shipping_lines: []
}

const ShopifyOrderDetailsPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')

  // Mock query - in real app this would fetch from Shopify API
  const { data: order, isLoading } = useQuery(
    ['shopify-order', id],
    () => Promise.resolve(mockShopifyOrder),
    {
      enabled: !!id
    }
  )

  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(num)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'partially_refunded':
        return 'bg-yellow-100 text-yellow-800'
      case 'pending':
        return 'bg-blue-100 text-blue-800'
      case 'refunded':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Package },
    { id: 'products', name: 'Line Items', icon: Tag },
    { id: 'customer', name: 'Customer', icon: User },
    { id: 'payment', name: 'Payment', icon: CreditCard },
    { id: 'shipping', name: 'Shipping', icon: Truck },
    { id: 'notes', name: 'Notes & Tags', icon: FileText }
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Shopify order...</p>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Order Not Found</h2>
          <p className="text-gray-600 mb-4">The Shopify order you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/orders/shopify')}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/orders/shopify')}
                className="inline-flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Shopify Orders
              </button>
              <div className="h-6 border-l border-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  🛍️ Shopify Order {order.name}
                </h1>
                <p className="text-sm text-gray-500 mt-1">
                  Created {formatDate(order.created_at)}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(order.financial_status)}`}>
                {order.financial_status?.replace('_', ' ').toUpperCase()}
              </span>
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Printer className="h-4 w-4 mr-2" />
                Print
              </button>
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
              <button className="inline-flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm font-medium">
                <Edit className="h-4 w-4 mr-2" />
                Edit Order
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Order Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <DollarSign className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Price</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(order.total_price)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Package className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Line Items</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {order.line_items.length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CreditCard className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Financial Status</p>
                    <p className="text-lg font-semibold text-gray-900 capitalize">
                      {order.financial_status?.replace('_', ' ')}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Truck className="h-8 w-8 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Fulfillment Status</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {order.fulfillment_status || 'Unfulfilled'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Details Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Order Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Order Information</h3>
                </div>
                <div className="px-6 py-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Order ID</label>
                      <p className="text-sm text-gray-900">{order.id}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Order Number</label>
                      <p className="text-sm text-gray-900">{order.order_number}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Order Name</label>
                      <p className="text-sm text-gray-900">{order.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Token</label>
                      <p className="text-sm text-gray-900 font-mono text-xs">{order.token}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Created At</label>
                      <p className="text-sm text-gray-900">{formatDate(order.created_at)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Updated At</label>
                      <p className="text-sm text-gray-900">{formatDate(order.updated_at)}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Processed At</label>
                      <p className="text-sm text-gray-900">{formatDate(order.processed_at)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Source Name</label>
                      <p className="text-sm text-gray-900">{order.source_name}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Currency</label>
                      <p className="text-sm text-gray-900">{order.currency}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Test Order</label>
                      <p className="text-sm text-gray-900">{order.test ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                  {order.reference && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Reference</label>
                      <p className="text-sm text-gray-900">{order.reference}</p>
                    </div>
                  )}
                  {order.cart_token && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Cart Token</label>
                      <p className="text-sm text-gray-900 font-mono text-xs">{order.cart_token}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Customer Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Customer Information</h3>
                </div>
                <div className="px-6 py-4 space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Customer Name</label>
                    <p className="text-sm text-gray-900">
                      {order.customer.first_name} {order.customer.last_name}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <p className="text-sm text-gray-900">{order.customer.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Phone</label>
                    <p className="text-sm text-gray-900">{order.customer.phone || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Customer ID</label>
                    <p className="text-sm text-gray-900">{order.customer.id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Orders Count</label>
                    <p className="text-sm text-gray-900">{order.customer.orders_count}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Total Spent</label>
                    <p className="text-sm text-gray-900">{formatCurrency(order.customer.total_spent)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Accepts Marketing</label>
                    <p className="text-sm text-gray-900">{order.customer.accepts_marketing ? 'Yes' : 'No'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Verified Email</label>
                    <p className="text-sm text-gray-900">{order.customer.verified_email ? 'Yes' : 'No'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Tax Exempt</label>
                    <p className="text-sm text-gray-900">{order.customer.tax_exempt ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ShopifyOrderDetailsPage

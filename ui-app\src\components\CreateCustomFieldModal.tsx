import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react'
import toast from 'react-hot-toast'

interface CustomField {
  id?: string
  label: string
  key: string
  field_type: string
  resource_type: string
  namespace: string
  description?: string
  placeholder_text?: string
  help_text?: string
  is_required: boolean
  is_searchable: boolean
  is_filterable: boolean
  is_ai_enabled: boolean
  field_options?: any
  validation_rules?: any
}

interface CreateCustomFieldModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (field: CustomField) => void
  editingField?: CustomField | null
  resourceType?: string
}

const CreateCustomFieldModal: React.FC<CreateCustomFieldModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingField,
  resourceType
}) => {
  const [formData, setFormData] = useState<CustomField>({
    label: '',
    key: '',
    field_type: 'text',
    resource_type: resourceType || 'products',
    namespace: 'global',
    description: '',
    placeholder_text: '',
    help_text: '',
    is_required: false,
    is_searchable: false,
    is_filterable: false,
    is_ai_enabled: false,
    field_options: {},
    validation_rules: {}
  })

  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [options, setOptions] = useState<{ value: string; label: string }[]>([])

  useEffect(() => {
    if (editingField) {
      setFormData({ ...editingField, resource_type: resourceType || editingField.resource_type })
      if (
        editingField.field_type === 'single_select' ||
        editingField.field_type === 'multi_select'
      ) {
        setOptions(editingField.field_options?.options || [])
      } else {
        setOptions([])
      }
    } else {
      setFormData({
        label: '',
        key: '',
        field_type: 'text',
        resource_type: resourceType || 'products',
        namespace: 'global',
        description: '',
        placeholder_text: '',
        help_text: '',
        is_required: false,
        is_searchable: false,
        is_filterable: false,
        is_ai_enabled: false,
        field_options: {},
        validation_rules: {}
      })
      setOptions([])
    }
    setErrors({})
  }, [editingField, isOpen, resourceType])

  const fieldTypes = [
    { value: 'text', label: 'Text - Single line text input' },
    { value: 'textarea', label: 'Textarea - Multi-line text input' },
    { value: 'number', label: 'Number - Numeric input' },
    { value: 'single_select', label: 'Single Select - Dropdown selection' },
    { value: 'multi_select', label: 'Multi Select - Multiple selections' },
    { value: 'date', label: 'Date - Date picker' },
    { value: 'boolean', label: 'Boolean - True/false toggle' },
    { value: 'url', label: 'URL - Website link input' },
    { value: 'email', label: 'Email - Email address input' }
  ]

  const resourceTypes = [
    { value: 'products', label: 'Products' },
    { value: 'customers', label: 'Customers' },
    { value: 'orders', label: 'Orders' },
    { value: 'companies', label: 'Companies' },
    { value: 'brands', label: 'Brands' },
    { value: 'categories', label: 'Categories' },
    { value: 'invoices', label: 'Invoices' },
    { value: 'quotes', label: 'Quotes' },
    { value: 'users', label: 'Users' },
    { value: 'pages', label: 'Pages' },
    { value: 'global', label: 'Global' }
  ]

  const generateKey = (label: string) => {
    return label
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .replace(/^_+|_+$/g, '')
  }

  const handleLabelChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      label: value,
      key: !editingField ? generateKey(value) : prev.key
    }))
  }

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!formData.label.trim()) {
      newErrors.label = 'Label is required'
    }

    if (!formData.key.trim()) {
      newErrors.key = 'Key is required'
    } else if (!/^[a-z0-9_]+$/.test(formData.key)) {
      newErrors.key = 'Key must contain only lowercase letters, numbers, and underscores'
    }

    if (!formData.field_type) {
      newErrors.field_type = 'Field type is required'
    }

    if (!formData.resource_type) {
      newErrors.resource_type = 'Resource type is required'
    }

    if ((formData.field_type === 'single_select' || formData.field_type === 'multi_select')) {
      if (!options.length) {
        newErrors.options = 'At least one option is required'
      } else if (options.some(opt => !opt.label.trim())) {
        newErrors.options = 'Option labels cannot be empty'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    let fieldToSave = { ...formData }
    if (
      formData.field_type === 'single_select' ||
      formData.field_type === 'multi_select'
    ) {
      fieldToSave.field_options = { options }
    } else {
      fieldToSave.field_options = {}
    }
    onSave(fieldToSave)
    toast.success(editingField ? 'Custom field updated successfully' : 'Custom field created successfully')
    onClose()
  }

  const handleInputChange = (field: keyof CustomField, value: any) => {
    if (field === 'resource_type' && resourceType) return
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {editingField ? 'Edit Meta Field' : 'Create Meta Field'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Label */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Label <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.label}
                  onChange={(e) => handleLabelChange(e.target.value)}
                  placeholder="e.g., Care Instructions"
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.label ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {errors.label && <p className="text-red-500 text-sm mt-1">{errors.label}</p>}
              </div>

              {/* Key */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Key <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.key}
                  onChange={(e) => handleInputChange('key', e.target.value)}
                  placeholder="e.g., care_instructions"
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.key ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                <p className="text-xs text-gray-500 mt-1">Lowercase letters, numbers, and underscores only</p>
                {errors.key && <p className="text-red-500 text-sm mt-1">{errors.key}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {/* Field Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Field Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.field_type}
                  onChange={(e) => handleInputChange('field_type', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.field_type ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  {fieldTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                {errors.field_type && <p className="text-red-500 text-sm mt-1">{errors.field_type}</p>}
              </div>

              {/* Resource Type */}
              {!resourceType ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Resource Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.resource_type}
                    onChange={(e) => handleInputChange('resource_type', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.resource_type ? 'border-red-300' : 'border-gray-300'
                    }`}
                  >
                    {resourceTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  {formData.resource_type !== 'global' && (
                    <p className="text-xs text-gray-500 mt-1">
                      This field will be available for {formData.resource_type} forms only
                    </p>
                  )}
                  {errors.resource_type && <p className="text-red-500 text-sm mt-1">{errors.resource_type}</p>}
                </div>
              ) : null}
            </div>

            {/* Namespace */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Namespace <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.namespace}
                onChange={(e) => handleInputChange('namespace', e.target.value)}
                placeholder="global"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Groups related fields together</p>
            </div>
          </div>

          {/* Additional Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>

            {/* Description */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe what this field is used for..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Placeholder Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Placeholder Text
                </label>
                <input
                  type="text"
                  value={formData.placeholder_text}
                  onChange={(e) => handleInputChange('placeholder_text', e.target.value)}
                  placeholder="e.g., Enter care instructions..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Help Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Help Text
                </label>
                <input
                  type="text"
                  value={formData.help_text}
                  onChange={(e) => handleInputChange('help_text', e.target.value)}
                  placeholder="Additional help for users..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Field Options */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Field Options</h3>

            <div className="space-y-4">
              {/* Required Field */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-900">Required Field</label>
                  <p className="text-sm text-gray-500">This field must be filled out</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.is_required}
                    onChange={(e) => handleInputChange('is_required', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Searchable */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-900">Searchable</label>
                  <p className="text-sm text-gray-500">Include this field in search results</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.is_searchable}
                    onChange={(e) => handleInputChange('is_searchable', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Filterable */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-900">Filterable</label>
                  <p className="text-sm text-gray-500">Allow filtering by this field</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.is_filterable}
                    onChange={(e) => handleInputChange('is_filterable', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* AI Enabled */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-900">AI Enabled</label>
                  <Sparkles className="h-4 w-4 text-purple-500" />
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500 mb-2">Use AI to help generate content for this field</p>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.is_ai_enabled}
                      onChange={(e) => handleInputChange('is_ai_enabled', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Options Editor for Select Types */}
          {['single_select', 'multi_select'].includes(formData.field_type) && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Options <span className="text-red-500">*</span>
              </label>
              {options.map((opt, idx) => (
                <div key={idx} className="flex items-center mb-2">
                  <input
                    className="border rounded px-2 py-1 mr-2 flex-1"
                    value={opt.label}
                    onChange={e => {
                      const newOptions = [...options]
                      newOptions[idx].label = e.target.value
                      newOptions[idx].value = e.target.value
                        .toLowerCase()
                        .replace(/[^a-z0-9_]/g, '_')
                        .replace(/_+/g, '_')
                        .replace(/^_+|_+$/g, '')
                      setOptions(newOptions)
                    }}
                    placeholder="Option label"
                  />
                  <button
                    type="button"
                    className="text-red-500 ml-2"
                    onClick={() => setOptions(options.filter((_, i) => i !== idx))}
                    aria-label="Delete option"
                  >
                    Delete
                  </button>
                </div>
              ))}
              <button
                type="button"
                className="mt-2 px-3 py-1 bg-blue-500 text-white rounded"
                onClick={() => setOptions([...options, { value: '', label: '' }])}
              >
                + Add Option
              </button>
              {errors.options && <p className="text-red-500 text-xs mt-1">{errors.options}</p>}
            </div>
          )}

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {editingField ? 'Update Field' : 'Create Field'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CreateCustomFieldModal

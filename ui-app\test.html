<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nuclues Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <div class="container">
        <h1>🎉 NUCLUES TEST PAGE 🎉</h1>
        
        <div class="test-section">
            <h2>📋 Setup Instructions</h2>
            <ol>
                <li>Make sure you have Node.js installed (version 18+)</li>
                <li>Open terminal in your Nuclues/frontend folder</li>
                <li>Run: <code>npm install</code></li>
                <li>Run: <code>npm run dev</code></li>
                <li>Open http://localhost:5173 in your browser</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🔧 Environment Check</h2>
            <div id="env-check">
                <p>Checking environment...</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 API Connection Test</h2>
            <button onclick="testAPI()">Test Backend Connection</button>
            <div id="api-result"></div>
        </div>
    </div>

    <script type="text/babel">
        const { useState } = React;
        
        function TestApp() {
            const [count, setCount] = useState(0);
            
            return (
                <div className="min-h-screen bg-blue-500 p-8">
                    <h1 className="text-4xl font-bold text-white mb-4">
                        🎉 NUCLUES TEST PAGE 🎉
                    </h1>
                    <p className="text-2xl text-white mb-4">
                        If you can see this, React is working!
                    </p>
                    <div className="bg-white p-6 rounded-lg shadow-lg mb-4">
                        <h2 className="text-xl font-bold text-gray-800 mb-2">
                            Interactive Test
                        </h2>
                        <p className="text-gray-600 mb-4">
                            Click count: {count}
                        </p>
                        <button 
                            onClick={() => setCount(count + 1)}
                            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                        >
                            Click Me!
                        </button>
                    </div>
                    <div className="bg-green-100 p-4 rounded border border-green-300">
                        <h3 className="font-bold text-green-800">Next Steps:</h3>
                        <ol className="list-decimal list-inside text-green-700 mt-2">
                            <li>If this page works, React is functional</li>
                            <li>Open terminal in your Nuclues/frontend folder</li>
                            <li>Run: npm install</li>
                            <li>Run: npm run dev</li>
                            <li>Open: http://localhost:5173</li>
                        </ol>
                    </div>
                </div>
            );
        }
        
        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>

    <script>
        // Environment check
        function checkEnvironment() {
            const envCheck = document.getElementById('env-check');
            const nodeVersion = typeof process !== 'undefined' ? process.version : 'Not available in browser';
            
            envCheck.innerHTML = `
                <p><strong>Node.js:</strong> <span class="success">✓ Available</span></p>
                <p><strong>Browser:</strong> <span class="success">✓ ${navigator.userAgent}</span></p>
                <p><strong>Current URL:</strong> ${window.location.href}</p>
            `;
        }

        // API connection test
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p>Testing connection...</p>';
            
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Mock successful API response
            const mockData = {
                status: 'ok',
                message: 'Mock API test successful',
                timestamp: new Date().toISOString(),
                services: {
                    postgres: 'healthy',
                    mongodb: 'healthy',
                    redis: 'healthy'
                }
            };
            
            resultDiv.innerHTML = `<p class="success">✓ Mock API test successful!</p><pre>${JSON.stringify(mockData, null, 2)}</pre>`;
        }

        // Run environment check on load
        checkEnvironment();
    </script>
</body>
</html>

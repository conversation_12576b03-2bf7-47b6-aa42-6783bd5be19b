import React from 'react';

interface PageHeaderProps {
  title: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, icon, children, className = '' }) => (
  <div className={`mb-4 ${className}`}>
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 w-full">
      <div className="flex items-center gap-3 min-w-0">
        {icon && <span className="h-8 w-8 flex items-center justify-center text-blue-600">{icon}</span>}
        <h1 className="text-2xl font-bold text-gray-900 whitespace-nowrap">{title}</h1>
      </div>
      {children && <div className="flex-shrink-0 mt-2 sm:mt-0">{children}</div>}
    </div>
  </div>
);

export default PageHeader; 
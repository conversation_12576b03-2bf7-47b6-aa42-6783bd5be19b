import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  ArrowLeft, Package, User, CreditCard, Truck, FileText,
  Printer, Download, Edit, Clock, CheckCircle, XCircle,
  Mail, Phone, MapPin, DollarSign, Plus, Eye, MoreHorizontal,
  Copy, ExternalLink, Upload, Calendar, Building, UserCheck,
  Clipboard, AlertCircle, TrendingUp, Archive, Settings,
  Receipt, Tag, Globe, Shield
} from 'lucide-react'
import { BigCommerceOrder } from '../types/bigcommerce'

// Mock BigCommerce order data
const mockBigCommerceOrder: BigCommerceOrder = {
  id: 218,
  customer_id: 11,
  date_created: "Tue, 05 Mar 2019 21:40:11 +0000",
  date_modified: "Mon, 11 Mar 2019 15:17:25 +0000",
  date_shipped: "",
  status_id: 7,
  status: "Awaiting Payment",
  subtotal_ex_tax: "62.6793",
  subtotal_inc_tax: "67.8400",
  subtotal_tax: "4.4000",
  base_shipping_cost: "12.0000",
  shipping_cost_ex_tax: "11.0900",
  shipping_cost_inc_tax: "12.0000",
  shipping_cost_tax: "0.9100",
  shipping_cost_tax_class_id: 0,
  base_handling_cost: "0.0000",
  handling_cost_ex_tax: "0.0000",
  handling_cost_inc_tax: "0.0000",
  handling_cost_tax: "0.0000",
  handling_cost_tax_class_id: 0,
  base_wrapping_cost: "0.0000",
  wrapping_cost_ex_tax: "0.0000",
  wrapping_cost_inc_tax: "0.0000",
  wrapping_cost_tax: "0.0000",
  wrapping_cost_tax_class_id: 0,
  total_ex_tax: "64.5300",
  total_inc_tax: "69.8400",
  total_tax: "5.3100",
  is_tax_inclusive_pricing: false,
  items_total: 4,
  items_shipped: 0,
  payment_method: "Cash",
  payment_provider_id: "",
  payment_status: "authorized",
  refunded_amount: "0.0000",
  order_is_digital: false,
  store_credit_amount: "0.0000",
  gift_certificate_amount: "0.0000",
  ip_address: "",
  ip_address_v6: "",
  geoip_country: "",
  geoip_country_iso2: "",
  currency_id: 1,
  currency_code: "USD",
  currency_exchange_rate: "1.**********",
  default_currency_id: 1,
  default_currency_code: "USD",
  staff_notes: "",
  customer_message: "",
  discount_amount: "5.0000",
  coupon_discount: "5.0000",
  shipping_address_count: 1,
  ebay_order_id: "0",
  cart_id: "7e48f7ef-2e88-4817-aea4-b0ed01490114",
  billing_address: {
    first_name: "Jane",
    last_name: "Doe",
    company: "",
    street_1: "555 East Street",
    street_2: "",
    city: "Austin",
    state: "Texas",
    zip: "78108",
    country: "United States",
    country_iso2: "US",
    phone: "**********",
    email: "<EMAIL>",
    form_fields: [
      {
        name: "Delivery Instructions",
        value: "Leave in backyard"
      }
    ]
  },
  is_email_opt_in: false,
  credit_card_type: null,
  order_source: "external",
  channel_id: 1,
  external_source: null,
  products: {
    url: "https://api.bigcommerce.com/stores/{store_hash}/v2/orders/218/products",
    resource: "/orders/218/products"
  },
  shipping_addresses: {
    url: "https://api.bigcommerce.com/stores/{store_hash}/v2/orders/218/shippingaddresses",
    resource: "/orders/218/shippingaddresses"
  },
  coupons: {
    url: "https://api.bigcommerce.com/stores/{store_hash}/v2/orders/218/coupons",
    resource: "/orders/218/coupons"
  },
  external_id: null,
  external_merchant_id: null,
  tax_provider_id: "BasicTaxProvider",
  store_default_currency_code: "",
  store_default_to_transactional_exchange_rate: "1.**********",
  custom_status: "Awaiting Payment",
  customer_locale: "en",
  external_order_id: "external-order-id",
  fees: [
    {
      id: 1,
      type: "custom_fee",
      display_name_customer: "A Fee",
      display_name_merchant: "A Fee",
      source: "AA",
      base_cost: "12.3000",
      cost_exc_tax: "12.3000",
      cost_inc_tax: "12.3000",
      cost_tax: "0.0000",
      tax_class_id: 22
    },
    {
      id: 2,
      type: "custom_fee",
      display_name_customer: "B Fee",
      display_name_merchant: "B Fee",
      source: "AA",
      base_cost: "4.0000",
      cost_ex_tax: "4.0000",
      cost_inc_tax: "12.0000",
      cost_tax: "8.0000",
      tax_class_id: null
    }
  ]
}

const BigCommerceOrderDetailsPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')

  // Mock query - in real app this would fetch from BigCommerce API
  const { data: order, isLoading } = useQuery(
    ['bigcommerce-order', id],
    () => Promise.resolve(mockBigCommerceOrder),
    {
      enabled: !!id
    }
  )

  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(num)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'awaiting payment':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'shipped':
        return 'bg-blue-100 text-blue-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Package },
    { id: 'products', name: 'Products', icon: Tag },
    { id: 'payment', name: 'Payment', icon: CreditCard },
    { id: 'shipping', name: 'Shipping', icon: Truck },
    { id: 'fees', name: 'Fees & Taxes', icon: Receipt },
    { id: 'notes', name: 'Notes', icon: FileText }
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading BigCommerce order...</p>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Order Not Found</h2>
          <p className="text-gray-600 mb-4">The BigCommerce order you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/orders/bigcommerce')}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/orders/bigcommerce')}
                className="inline-flex items-center text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to BigCommerce Orders
              </button>
              <div className="h-6 border-l border-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  🛒 BigCommerce Order #{order.id}
                </h1>
                <p className="text-sm text-gray-500 mt-1">
                  Created {formatDate(order.date_created)}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(order.status)}`}>
                {order.status}
              </span>
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Printer className="h-4 w-4 mr-2" />
                Print
              </button>
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
              <button className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                <Edit className="h-4 w-4 mr-2" />
                Edit Order
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Order Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <DollarSign className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total (Inc Tax)</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(order.total_inc_tax)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Package className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Items</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {order.items_total}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CreditCard className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Payment Status</p>
                    <p className="text-lg font-semibold text-gray-900 capitalize">
                      {order.payment_status}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Truck className="h-8 w-8 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Items Shipped</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {order.items_shipped}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Details Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Order Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Order Information</h3>
                </div>
                <div className="px-6 py-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Order ID</label>
                      <p className="text-sm text-gray-900">{order.id}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Customer ID</label>
                      <p className="text-sm text-gray-900">{order.customer_id}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Date Created</label>
                      <p className="text-sm text-gray-900">{formatDate(order.date_created)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Date Modified</label>
                      <p className="text-sm text-gray-900">{formatDate(order.date_modified)}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Status</label>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Order Source</label>
                      <p className="text-sm text-gray-900 capitalize">{order.order_source}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Currency</label>
                      <p className="text-sm text-gray-900">{order.currency_code}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Channel ID</label>
                      <p className="text-sm text-gray-900">{order.channel_id}</p>
                    </div>
                  </div>
                  {order.external_order_id && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">External Order ID</label>
                      <p className="text-sm text-gray-900">{order.external_order_id}</p>
                    </div>
                  )}
                  {order.cart_id && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Cart ID</label>
                      <p className="text-sm text-gray-900 font-mono text-xs">{order.cart_id}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Customer & Billing Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Customer & Billing</h3>
                </div>
                <div className="px-6 py-4 space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Customer Name</label>
                    <p className="text-sm text-gray-900">
                      {order.billing_address.first_name} {order.billing_address.last_name}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email</label>
                    <p className="text-sm text-gray-900">{order.billing_address.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Phone</label>
                    <p className="text-sm text-gray-900">{order.billing_address.phone}</p>
                  </div>
                  {order.billing_address.company && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Company</label>
                      <p className="text-sm text-gray-900">{order.billing_address.company}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-500">Billing Address</label>
                    <div className="text-sm text-gray-900">
                      <p>{order.billing_address.street_1}</p>
                      {order.billing_address.street_2 && <p>{order.billing_address.street_2}</p>}
                      <p>{order.billing_address.city}, {order.billing_address.state} {order.billing_address.zip}</p>
                      <p>{order.billing_address.country}</p>
                    </div>
                  </div>
                  {order.billing_address.form_fields.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Additional Information</label>
                      <div className="space-y-2">
                        {order.billing_address.form_fields.map((field, index) => (
                          <div key={index}>
                            <span className="text-xs font-medium text-gray-500">{field.name}:</span>
                            <p className="text-sm text-gray-900">{field.value}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payment' && (
          <div className="space-y-8">
            {/* Payment Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Payment Details</h3>
              </div>
              <div className="px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Payment Method</label>
                    <p className="text-sm text-gray-900">{order.payment_method}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Payment Status</label>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      order.payment_status === 'authorized' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {order.payment_status}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Payment Provider ID</label>
                    <p className="text-sm text-gray-900">{order.payment_provider_id || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Credit Card Type</label>
                    <p className="text-sm text-gray-900">{order.credit_card_type || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Refunded Amount</label>
                    <p className="text-sm text-gray-900">{formatCurrency(order.refunded_amount)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Store Credit Used</label>
                    <p className="text-sm text-gray-900">{formatCurrency(order.store_credit_amount)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Totals Breakdown */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Order Totals Breakdown</h3>
              </div>
              <div className="px-6 py-4">
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Subtotal (Ex Tax)</span>
                    <span className="text-sm text-gray-900">{formatCurrency(order.subtotal_ex_tax)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Subtotal (Inc Tax)</span>
                    <span className="text-sm text-gray-900">{formatCurrency(order.subtotal_inc_tax)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Subtotal Tax</span>
                    <span className="text-sm text-gray-900">{formatCurrency(order.subtotal_tax)}</span>
                  </div>
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Shipping (Ex Tax)</span>
                      <span className="text-sm text-gray-900">{formatCurrency(order.shipping_cost_ex_tax)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Shipping (Inc Tax)</span>
                      <span className="text-sm text-gray-900">{formatCurrency(order.shipping_cost_inc_tax)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Shipping Tax</span>
                      <span className="text-sm text-gray-900">{formatCurrency(order.shipping_cost_tax)}</span>
                    </div>
                  </div>
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Handling Cost</span>
                      <span className="text-sm text-gray-900">{formatCurrency(order.handling_cost_inc_tax)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Wrapping Cost</span>
                      <span className="text-sm text-gray-900">{formatCurrency(order.wrapping_cost_inc_tax)}</span>
                    </div>
                  </div>
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Discount Amount</span>
                      <span className="text-sm text-red-600">-{formatCurrency(order.discount_amount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Coupon Discount</span>
                      <span className="text-sm text-red-600">-{formatCurrency(order.coupon_discount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Gift Certificate</span>
                      <span className="text-sm text-red-600">-{formatCurrency(order.gift_certificate_amount)}</span>
                    </div>
                  </div>
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Tax</span>
                      <span className="text-sm text-gray-900">{formatCurrency(order.total_tax)}</span>
                    </div>
                    <div className="flex justify-between font-semibold text-lg">
                      <span className="text-gray-900">Total (Inc Tax)</span>
                      <span className="text-gray-900">{formatCurrency(order.total_inc_tax)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Currency Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Currency Information</h3>
              </div>
              <div className="px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Order Currency</label>
                    <p className="text-sm text-gray-900">{order.currency_code}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Exchange Rate</label>
                    <p className="text-sm text-gray-900">{order.currency_exchange_rate}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Default Currency</label>
                    <p className="text-sm text-gray-900">{order.default_currency_code}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Tax Inclusive Pricing</label>
                    <p className="text-sm text-gray-900">{order.is_tax_inclusive_pricing ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'products' && (
          <div className="space-y-8">
            {/* Products Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Products API Information</h3>
              </div>
              <div className="px-6 py-4">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Products API URL</label>
                    <p className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">{order.products.url}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Resource Path</label>
                    <p className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">{order.products.resource}</p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Fetch Products
                    </button>
                    <span className="text-sm text-gray-500">Click to load product details from BigCommerce API</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Items Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Order Items Summary</h3>
              </div>
              <div className="px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{order.items_total}</div>
                    <div className="text-sm text-gray-500">Total Items</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{order.items_shipped}</div>
                    <div className="text-sm text-gray-500">Items Shipped</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{order.items_total - order.items_shipped}</div>
                    <div className="text-sm text-gray-500">Items Pending</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Placeholder for Product List */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Product Details</h3>
              </div>
              <div className="px-6 py-8 text-center">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Product Details Not Loaded</h3>
                <p className="text-gray-600 mb-4">
                  Product details need to be fetched from the BigCommerce API using the products endpoint.
                </p>
                <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Load Product Details
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'shipping' && (
          <div className="space-y-8">
            {/* Shipping Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Shipping Summary</h3>
              </div>
              <div className="px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Shipping Addresses</label>
                    <p className="text-2xl font-semibold text-gray-900">{order.shipping_address_count}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Items Shipped</label>
                    <p className="text-2xl font-semibold text-gray-900">{order.items_shipped}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Shipping Cost (Inc Tax)</label>
                    <p className="text-2xl font-semibold text-gray-900">{formatCurrency(order.shipping_cost_inc_tax)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Date Shipped</label>
                    <p className="text-sm text-gray-900">{order.date_shipped || 'Not shipped'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Shipping Cost Breakdown */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Shipping Cost Breakdown</h3>
              </div>
              <div className="px-6 py-4">
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Base Shipping Cost</span>
                    <span className="text-sm text-gray-900">{formatCurrency(order.base_shipping_cost)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Shipping Cost (Ex Tax)</span>
                    <span className="text-sm text-gray-900">{formatCurrency(order.shipping_cost_ex_tax)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Shipping Cost (Inc Tax)</span>
                    <span className="text-sm text-gray-900">{formatCurrency(order.shipping_cost_inc_tax)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Shipping Tax</span>
                    <span className="text-sm text-gray-900">{formatCurrency(order.shipping_cost_tax)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Shipping Tax Class ID</span>
                    <span className="text-sm text-gray-900">{order.shipping_cost_tax_class_id}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Shipping Addresses API */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Shipping Addresses API</h3>
              </div>
              <div className="px-6 py-4">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Shipping Addresses API URL</label>
                    <p className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">{order.shipping_addresses.url}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Resource Path</label>
                    <p className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">{order.shipping_addresses.resource}</p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Fetch Shipping Addresses
                    </button>
                    <span className="text-sm text-gray-500">Click to load detailed shipping addresses from BigCommerce API</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Placeholder for Shipping Addresses */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Detailed Shipping Addresses</h3>
              </div>
              <div className="px-6 py-8 text-center">
                <Truck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Shipping Addresses Not Loaded</h3>
                <p className="text-gray-600 mb-4">
                  Detailed shipping addresses need to be fetched from the BigCommerce API.
                </p>
                <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Load Shipping Addresses
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'fees' && (
          <div className="space-y-8">
            {/* Fees */}
            {order.fees.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Custom Fees</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fee Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Base Cost</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost (Ex Tax)</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost (Inc Tax)</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax Class ID</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {order.fees.map((fee) => (
                        <tr key={fee.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div>
                              <p className="font-medium">{fee.display_name_merchant}</p>
                              <p className="text-gray-500 text-xs">Customer: {fee.display_name_customer}</p>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{fee.type}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(fee.base_cost)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(fee.cost_ex_tax)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(fee.cost_inc_tax)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(fee.cost_tax)}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{fee.tax_class_id || 'N/A'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Tax Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Tax Information</h3>
              </div>
              <div className="px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Tax Provider</label>
                    <p className="text-sm text-gray-900">{order.tax_provider_id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Total Tax Amount</label>
                    <p className="text-sm text-gray-900">{formatCurrency(order.total_tax)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Shipping Tax Class ID</label>
                    <p className="text-sm text-gray-900">{order.shipping_cost_tax_class_id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Handling Tax Class ID</label>
                    <p className="text-sm text-gray-900">{order.handling_cost_tax_class_id}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'notes' && (
          <div className="space-y-8">
            {/* Staff Notes */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Staff Notes</h3>
              </div>
              <div className="px-6 py-4">
                {order.staff_notes ? (
                  <p className="text-sm text-gray-900">{order.staff_notes}</p>
                ) : (
                  <p className="text-sm text-gray-500 italic">No staff notes available</p>
                )}
              </div>
            </div>

            {/* Customer Message */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Customer Message</h3>
              </div>
              <div className="px-6 py-4">
                {order.customer_message ? (
                  <p className="text-sm text-gray-900">{order.customer_message}</p>
                ) : (
                  <p className="text-sm text-gray-500 italic">No customer message</p>
                )}
              </div>
            </div>

            {/* Additional Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Additional Information</h3>
              </div>
              <div className="px-6 py-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Customer Locale</label>
                    <p className="text-sm text-gray-900">{order.customer_locale}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Email Opt-in</label>
                    <p className="text-sm text-gray-900">{order.is_email_opt_in ? 'Yes' : 'No'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Digital Order</label>
                    <p className="text-sm text-gray-900">{order.order_is_digital ? 'Yes' : 'No'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">eBay Order ID</label>
                    <p className="text-sm text-gray-900">{order.ebay_order_id || 'N/A'}</p>
                  </div>
                </div>
                {(order.ip_address || order.geoip_country) && (
                  <div className="border-t border-gray-200 pt-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Location Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {order.ip_address && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">IP Address</label>
                          <p className="text-sm text-gray-900">{order.ip_address}</p>
                        </div>
                      )}
                      {order.geoip_country && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">GeoIP Country</label>
                          <p className="text-sm text-gray-900">{order.geoip_country} ({order.geoip_country_iso2})</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default BigCommerceOrderDetailsPage

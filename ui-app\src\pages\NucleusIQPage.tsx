import React, { useState, useRef, useEffect } from 'react';
import { useChat } from '../contexts/ChatContext';
import {
  Send,
  Bot,
  Settings,
  Download,
  Share2,
  RotateCcw,
  ChevronRight
} from 'lucide-react';
import StructuredChatResponse from '../components/StructuredChatResponse';
import NucleusIQSidebar from '../components/NucleusIQSidebar';

const NucleusIQPage: React.FC = () => {
  const { 
    currentConversation,
    sendMessage,
    isLoading
  } = useChat();

  const [inputValue, setInputValue] = useState('');
  const [showSidebar, setShowSidebar] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentConversation?.messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;
    
    await sendMessage(inputValue);
    setInputValue('');
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* New Advanced Sidebar */}
      <NucleusIQSidebar 
        isOpen={showSidebar}
        onToggle={() => setShowSidebar(!showSidebar)}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-gray-200 bg-white shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {!showSidebar && (
                <button
                  onClick={() => setShowSidebar(true)}
                  className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              )}
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <Bot className="h-5 w-5 text-white" />
                </div>
                <div>
                  <span className="font-medium text-gray-900">
                    {currentConversation?.name || 'New Conversation'}
                  </span>
                  {currentConversation && (
                    <span className="text-sm text-gray-500 ml-2">
                      {currentConversation.messages.length} messages
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-500 hover:text-gray-700 transition-colors">
                <Share2 className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-500 hover:text-gray-700 transition-colors">
                <Download className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-500 hover:text-gray-700 transition-colors">
                <Settings className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {currentConversation?.messages.length === 0 ? (
            <div className="text-center text-gray-500 mt-8">
              <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Bot className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-medium mb-2">Welcome to NucleusIQ</h3>
              <p className="text-sm max-w-md mx-auto">
                Your AI assistant for the Nuclues platform. Ask me anything about your business, 
                products, customers, or platform features!
              </p>
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                <div className="p-4 bg-white rounded-lg border border-gray-200">
                  <h4 className="font-medium mb-2">💼 Business Insights</h4>
                  <p className="text-sm text-gray-600">"Who is my top customer?"</p>
                  <p className="text-sm text-gray-600">"What's my best product?"</p>
                </div>
                <div className="p-4 bg-white rounded-lg border border-gray-200">
                  <h4 className="font-medium mb-2">📊 Analytics</h4>
                  <p className="text-sm text-gray-600">"Show me recent orders"</p>
                  <p className="text-sm text-gray-600">"Compare my products"</p>
                </div>
              </div>
            </div>
          ) : (
            currentConversation?.messages.map((message) => (
              <MessageBubble key={message.id} message={message} />
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="p-4 border-t border-gray-200 bg-white">
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
            <div className="flex space-x-3">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="Ask me anything about your Nuclues platform..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12"
                  disabled={isLoading}
                />
                <button
                  type="button"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600"
                >
                  <RotateCcw className="h-4 w-4" />
                </button>
              </div>
              <button
                type="submit"
                disabled={!inputValue.trim() || isLoading}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2 shadow-lg"
              >
                <Send className="h-4 w-4" />
                <span>Send</span>
              </button>
            </div>
            <div className="mt-2 text-xs text-gray-500 text-center">
              NucleusIQ can help with business insights, product analysis, customer data, and platform navigation
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Message Bubble Component
interface MessageBubbleProps {
  message: {
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    structuredData?: {
      type: 'customer' | 'product' | 'order' | 'revenue' | 'list';
      title: string;
      subtitle?: string;
      data: any;
      actions?: Array<{
        label: string;
        action: string;
        icon?: string;
      }>;
    };
  };
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  return (
    <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-3xl px-6 py-4 rounded-2xl ${
        message.role === 'user'
          ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
          : 'bg-white border border-gray-200 text-gray-900 shadow-sm'
      }`}>
        <div className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</div>
        
        {/* Render structured data if available */}
        {message.structuredData && (
          <div className="mt-4">
            <StructuredChatResponse data={message.structuredData} />
          </div>
        )}
        
        <div className={`text-xs mt-3 ${
          message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
        }`}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      </div>
    </div>
  );
};

export default NucleusIQPage; 
import {
  Order,
  OrderListItem,
  OrderListResult,
  OrderQueryParams
} from '../types/order'

// Simulate network delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// Mock data
const mockOrderData: OrderListItem[] = [
  {
    id: 'ORD-1001',
    customer_id: 'CUST-001',
    customer_name: '<PERSON>',
    customer_email: '<EMAIL>',
    date_created: '2023-06-17T10:00:00Z',
    date_modified: '2023-06-17T10:00:00Z',
    status: 'Completed',
    status_id: 10,
    payment_status: 'paid',
    payment_method: 'Credit Card',
    total_inc_tax: 570.50,
    items_total: 3,
    currency_code: 'USD',
    billing_address: {
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>'
    },
    shipping_addresses: [{
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      city: 'New York',
      state: 'NY',
      country: 'United States'
    }],
    channel: 'Shopify'
  },
  {
    id: 'ORD-1002',
    customer_id: 'CUST-002',
    customer_name: '<PERSON>',
    customer_email: '<EMAIL>',
    date_created: '2023-06-21T14:30:00Z',
    date_modified: '2023-06-21T14:30:00Z',
    status: 'Processing',
    status_id: 7,
    payment_status: 'pending',
    payment_method: 'PayPal',
    total_inc_tax: 125.00,
    items_total: 1,
    currency_code: 'USD',
    billing_address: {
      first_name: 'Jane',
      last_name: 'Smith',
      email: '<EMAIL>'
    },
    shipping_addresses: [{
      first_name: 'Jane',
      last_name: 'Smith',
      city: 'Los Angeles',
      state: 'CA',
      country: 'United States'
    }],
    channel: 'BigCommerce'
  },
  {
    id: 'ORD-1003',
    customer_id: 'CUST-003',
    customer_name: 'Bob Johnson',
    customer_email: '<EMAIL>',
    date_created: '2023-06-22T09:15:00Z',
    date_modified: '2023-06-22T09:15:00Z',
    status: 'Shipped',
    status_id: 8,
    payment_status: 'paid',
    payment_method: 'Credit Card',
    total_inc_tax: 89.99,
    items_total: 2,
    currency_code: 'USD',
    billing_address: {
      first_name: 'Bob',
      last_name: 'Johnson',
      email: '<EMAIL>'
    },
    shipping_addresses: [{
      first_name: 'Bob',
      last_name: 'Johnson',
      city: 'Chicago',
      state: 'IL',
      country: 'United States'
    }],
    channel: 'Shopify'
  }
]

export const orderApi = {
  // Get all orders with filtering and pagination
  getOrders: async (params?: OrderQueryParams): Promise<OrderListResult> => {
    await delay()
    
    let filteredOrders = [...mockOrderData]

    // Apply filters
    if (params?.search) {
      const search = params.search.toLowerCase()
      filteredOrders = filteredOrders.filter((order: OrderListItem) =>
        order.id.toLowerCase().includes(search) ||
        order.customer_name?.toLowerCase().includes(search) ||
        order.customer_email?.toLowerCase().includes(search)
      )
    }

    if (params?.status) {
      filteredOrders = filteredOrders.filter((order: OrderListItem) => order.status === params.status)
    }

    if (params?.payment_status) {
      filteredOrders = filteredOrders.filter((order: OrderListItem) => order.payment_status === params.payment_status)
    }

    if (params?.channel) {
      filteredOrders = filteredOrders.filter((order: OrderListItem) => order.channel === params.channel)
    }

    // Apply sorting
    if (params?.sort_by) {
      const sortBy = params.sort_by as keyof OrderListItem
      const sortOrder = params.sort_order === 'ASC' ? 1 : -1

      filteredOrders.sort((a, b) => {
        const aVal = a[sortBy]
        const bVal = b[sortBy]

        if (typeof aVal === 'string' && typeof bVal === 'string') {
          return aVal.localeCompare(bVal) * sortOrder
        }

        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return (aVal - bVal) * sortOrder
        }

        return 0
      })
    }

    // Apply pagination
    const page = params?.page || 1
    const limit = params?.limit || 20
    const offset = (page - 1) * limit
    const paginatedOrders = filteredOrders.slice(offset, offset + limit)

    return {
      orders: paginatedOrders,
      total: filteredOrders.length,
      page,
      limit,
      total_pages: Math.ceil(filteredOrders.length / limit)
    }
  },

  // Get a single order by ID
  getOrder: async (id: string): Promise<Order> => {
    await delay()
    const order = mockOrderData.find((o: OrderListItem) => o.id === id)
    if (!order) {
      throw new Error('Order not found')
    }
    
    // Convert OrderListItem to full Order
    const fullOrder: Order = {
      ...order,
      billing_address: {
        first_name: 'John',
        last_name: 'Doe',
        company: 'Acme Corp',
        address1: '123 Main St',
        address2: 'Suite 100',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'US',
        phone: '******-123-4567'
      },
      shipping_address: {
        first_name: 'John',
        last_name: 'Doe',
        company: 'Acme Corp',
        address1: '123 Main St',
        address2: 'Suite 100',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'US',
        phone: '******-123-4567'
      },
      items: [
        {
          id: 'item_1',
          product_id: 'prod_1',
          product_name: 'Sample Product',
          sku: 'SAMPLE-001',
          quantity: 1,
          price: order.total_inc_tax,
          total: order.total_inc_tax
        }
      ],
      notes: [],
      shipments: []
    }
    
    return fullOrder
  },

  // Update order status
  updateOrderStatus: async (id: string, status: string): Promise<Order> => {
    await delay()
    const orderIndex = mockOrderData.findIndex((o: OrderListItem) => o.id === id)
    if (orderIndex === -1) {
      throw new Error('Order not found')
    }
    
    mockOrderData[orderIndex] = {
      ...mockOrderData[orderIndex],
      status: status as any,
      updated_at: new Date().toISOString()
    }
    
    return await orderApi.getOrder(id)
  },

  // Add order notes
  addOrderNote: async (id: string, note: string): Promise<{ message: string }> => {
    await delay()
    const order = mockOrderData.find((o: OrderListItem) => o.id === id)
    if (!order) {
      throw new Error('Order not found')
    }
    
    return { message: 'Note added successfully' }
  },

  // Process refund
  processRefund: async (id: string, amount: number, reason: string): Promise<{ message: string }> => {
    await delay(2000) // Simulate longer processing time
    const order = mockOrderData.find((o: OrderListItem) => o.id === id)
    if (!order) {
      throw new Error('Order not found')
    }
    
    return { message: `Refund of $${amount} processed successfully` }
  },

  // Create shipment
  createShipment: async (id: string, shipmentData: any): Promise<{ message: string }> => {
    await delay(1500)
    const order = mockOrderData.find((o: OrderListItem) => o.id === id)
    if (!order) {
      throw new Error('Order not found')
    }
    
    return { message: 'Shipment created successfully' }
  }
}

// Mock API service for development (keeping for backward compatibility)
export const mockOrderApi = {
  getOrders: async (params?: OrderQueryParams): Promise<OrderListResult> => {
    return orderApi.getOrders(params)
  },

  getOrder: async (id: string): Promise<Order> => {
    return orderApi.getOrder(id)
  },

  updateOrderStatus: async (id: string, status: string): Promise<Order> => {
    return orderApi.updateOrderStatus(id, status)
  },

  addOrderNote: async (id: string, note: string): Promise<{ message: string }> => {
    return orderApi.addOrderNote(id, note)
  },

  processRefund: async (id: string, amount: number, reason: string): Promise<{ message: string }> => {
    return orderApi.processRefund(id, amount, reason)
  },

  createShipment: async (id: string, shipmentData: any): Promise<{ message: string }> => {
    return orderApi.createShipment(id, shipmentData)
  }
}

import { useState, useRef, useEffect } from 'react'
import { X, Download, Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react'

interface CSVModalProps {
  isOpen: boolean
  onClose: () => void
  mode: 'import' | 'export'
  onExport?: (options: ExportOptions) => void
  onImport?: (file: File) => void
}

interface ExportOptions {
  type: 'all' | 'visible' | 'filtered'
  includeHeaders: boolean
  selectedColumns?: string[]
}

const CSVModal = ({ isOpen, onClose, mode, onExport, onImport }: CSVModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    type: 'filtered',
    includeHeaders: true
  })
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [importStatus, setImportStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle')

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedFile(null)
      setImportStatus('idle')
      setExportOptions({
        type: 'filtered',
        includeHeaders: true
      })
    }
  }, [isOpen])

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type === 'text/csv') {
      setSelectedFile(file)
      setImportStatus('idle')
    } else {
      alert('Please select a valid CSV file')
    }
  }

  const handleImport = () => {
    if (selectedFile && onImport) {
      setImportStatus('processing')
      // Simulate import process
      setTimeout(() => {
        onImport(selectedFile)
        setImportStatus('success')
        setTimeout(() => {
          onClose()
        }, 2000)
      }, 1500)
    }
  }

  const handleExport = () => {
    if (onExport) {
      onExport(exportOptions)
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />

        {/* Modal */}
        <div
          ref={modalRef}
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
        >
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {mode === 'import' ? (
                  <Upload className="h-5 w-5 text-gray-400 mr-2" />
                ) : (
                  <Download className="h-5 w-5 text-gray-400 mr-2" />
                )}
                <h3 className="text-lg font-medium text-gray-900">
                  {mode === 'import' ? 'Import CSV' : 'Export CSV'}
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="bg-white px-6 py-4">
            {mode === 'import' ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select CSV File
                  </label>
                  <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                    <div className="space-y-1 text-center">
                      <FileText className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="file-upload"
                          className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                        >
                          <span>Upload a file</span>
                          <input
                            ref={fileInputRef}
                            id="file-upload"
                            name="file-upload"
                            type="file"
                            accept=".csv"
                            className="sr-only"
                            onChange={handleFileSelect}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">CSV files only</p>
                    </div>
                  </div>
                </div>

                {selectedFile && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 text-blue-600 mr-2" />
                      <span className="text-sm font-medium text-blue-900">
                        {selectedFile.name}
                      </span>
                      <span className="text-sm text-blue-700 ml-2">
                        ({(selectedFile.size / 1024).toFixed(1)} KB)
                      </span>
                    </div>
                  </div>
                )}

                {importStatus === 'processing' && (
                  <div className="p-3 bg-yellow-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
                      <span className="text-sm text-yellow-800">Processing import...</span>
                    </div>
                  </div>
                )}

                {importStatus === 'success' && (
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm text-green-800">Import completed successfully!</span>
                    </div>
                  </div>
                )}

                {importStatus === 'error' && (
                  <div className="p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                      <span className="text-sm text-red-800">Import failed. Please check your file format.</span>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Export Options
                  </label>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Data to Export</label>
                      <div className="mt-2 space-y-2">
                        {[
                          { value: 'all', label: 'All data' },
                          { value: 'visible', label: 'Visible columns only' },
                          { value: 'filtered', label: 'Current filtered data' }
                        ].map((option) => (
                          <label key={option.value} className="flex items-center">
                            <input
                              type="radio"
                              name="exportType"
                              value={option.value}
                              checked={exportOptions.type === option.value}
                              onChange={(e) => setExportOptions(prev => ({ ...prev, type: e.target.value as any }))}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={exportOptions.includeHeaders}
                          onChange={(e) => setExportOptions(prev => ({ ...prev, includeHeaders: e.target.checked }))}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Include column headers</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            {mode === 'import' ? (
              <button
                onClick={handleImport}
                disabled={!selectedFile || importStatus === 'processing'}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {importStatus === 'processing' ? 'Importing...' : 'Import'}
              </button>
            ) : (
              <button
                onClick={handleExport}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Export
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CSVModal

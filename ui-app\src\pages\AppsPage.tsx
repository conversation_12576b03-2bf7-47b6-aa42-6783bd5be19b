import { useState, useEffect } from 'react'
import { Plus, ShoppingBag, Store, CheckCircle, XCircle, Settings } from 'lucide-react'
import AppConnectionModal from '../components/AppConnectionModal'
import { mockApiService } from '../services/mockApi'
import { App } from '../data/mockData'
import toast from 'react-hot-toast'
import SectionHeader from '../components/SectionHeader'

const AppsPage = () => {
  const [selectedApp, setSelectedApp] = useState<App | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [apps, setApps] = useState<App[]>([])
  const [loading, setLoading] = useState(true)

  // Load apps from mock API
  useEffect(() => {
    const loadApps = async () => {
      try {
        const appsData = await mockApiService.apps.getAll()
        // Map icons to components
        const appsWithIcons = appsData.map(app => ({
          ...app,
          icon: app.id === 'shopify' ? ShoppingBag : Store
        }))
        setApps(appsWithIcons)
      } catch (error) {
        toast.error('Failed to load apps')
      } finally {
        setLoading(false)
      }
    }

    loadApps()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'syncing':
        return <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      default:
        return <XCircle className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected':
        return 'Connected'
      case 'error':
        return 'Error'
      case 'syncing':
        return 'Syncing'
      default:
        return 'Not Connected'
    }
  }

  const handleConnectApp = (app: App) => {
    setSelectedApp(app)
    setIsModalOpen(true)
  }

  const handleAppConnection = async (_credentials: any) => {
    if (!selectedApp) return

    try {
      // The connection is already handled in the modal, just refresh the apps list
      const appsData = await mockApiService.apps.getAll()
      const appsWithIcons = appsData.map(app => ({
        ...app,
        icon: app.id === 'shopify' ? ShoppingBag : Store
      }))
      setApps(appsWithIcons)
      toast.success(`${selectedApp.name} connected successfully!`)
    } catch (error) {
      toast.error('Failed to update app status')
    }
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-600 bg-green-50'
      case 'error':
        return 'text-red-600 bg-red-50'
      case 'syncing':
        return 'text-blue-600 bg-blue-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Integrations</h1>
            <p className="mt-1 text-sm text-gray-600">
              Connect and manage your e-commerce platform integrations
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <SectionHeader
        icon={<Store className="h-7 w-7" />}
        title="Integrations"
        subtitle="Connect and manage your e-commerce platform integrations"
        actions={
          <button className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
            <Plus className="h-4 w-4" />
            Browse Apps
          </button>
        }
      />

      {/* Connected Apps Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Connected Apps</p>
              <p className="text-2xl font-bold text-gray-900">
                {apps.filter(app => app.isConnected).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <Store className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Apps</p>
              <p className="text-2xl font-bold text-gray-900">{apps.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <div className="h-6 w-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Syncs</p>
              <p className="text-2xl font-bold text-gray-900">
                {apps.filter(app => app.status === 'syncing').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Apps Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {apps.map((app) => {
          const Icon = typeof app.icon === 'string' ? Store : app.icon
          return (
            <div key={app.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-12 w-12 bg-gray-50 rounded-lg flex items-center justify-center">
                      <Icon className="h-6 w-6 text-gray-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">{app.name}</h3>
                    <p className="text-sm text-gray-500 mt-1">{app.description}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {getStatusIcon(app.status)}
                    <span className={`ml-2 text-xs font-medium px-2 py-1 rounded-full ${getStatusColor(app.status)}`}>
                      {getStatusText(app.status)}
                    </span>
                  </div>
                  <button
                    onClick={() => handleConnectApp(app)}
                    className="bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md hover:bg-gray-200 transition-colors duration-200 flex items-center text-sm"
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    {app.isConnected ? 'Configure' : 'Connect'}
                  </button>
                </div>

                {app.lastSync && (
                  <p className="text-xs text-gray-500 mt-3">
                    Last sync: {app.lastSync}
                  </p>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* Empty State for when no apps are available */}
      {apps.length === 0 && (
        <div className="text-center py-12">
          <Store className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No apps available</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by browsing available integrations.
          </p>
          <div className="mt-6">
            <button className="btn btn-primary">
              Browse App Store
            </button>
          </div>
        </div>
      )}

      {/* Connection Modal */}
      {selectedApp && (
        <AppConnectionModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false)
            setSelectedApp(null)
          }}
          app={selectedApp}
          onConnect={handleAppConnection}
        />
      )}
    </div>
  )
}

export default AppsPage

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Settings, 
  TestTube, 
  RefreshCw, 
  AlertCircle, 
  CheckCircle, 
  XCircle,
  Edit,
  Trash2,
  Activity,
  Database,
  Globe,
  Building,
  Zap,
  Mail,
  CreditCard,
  Eye,
  EyeOff,
  Copy,
  ExternalLink
} from 'lucide-react';

interface Connection {
  id: string;
  name: string;
  platform: string;
  status: 'active' | 'inactive' | 'error';
  authType: string;
  credentials: Record<string, string>;
  connectionConfig: Record<string, any>;
  lastSync?: string;
  syncStatus?: 'success' | 'error' | 'running';
  createdAt: string;
  updatedAt: string;
}

const ConnectionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [connection, setConnection] = useState<Connection | null>(null);
  const [loading, setLoading] = useState(true);
  const [testing, setTesting] = useState(false);
  const [showCredentials, setShowCredentials] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // Mock API call to fetch connection details
    const fetchConnection = async () => {
      setLoading(true);
      try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock connection data
        const mockConnection: Connection = {
          id: id || '1',
          name: 'Salesforce Production',
          platform: 'salesforce',
          status: 'active',
          authType: 'OAuth2',
          credentials: {
            client_id: '3MVG9...',
            client_secret: '••••••••••••••••••••••••••••••••',
            instance_url: 'https://na1.salesforce.com'
          },
          connectionConfig: {
            api_version: '58.0',
            timeout: 30
          },
          lastSync: '2024-01-15T10:30:00Z',
          syncStatus: 'success',
          createdAt: '2024-01-10T09:00:00Z',
          updatedAt: '2024-01-15T10:30:00Z'
        };
        
        setConnection(mockConnection);
      } catch (error) {
        console.error('Error fetching connection:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConnection();
  }, [id]);

  const handleTestConnection = async () => {
    setTesting(true);
    try {
      // Mock API call to test connection
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update connection status
      if (connection) {
        setConnection({
          ...connection,
          status: 'active',
          updatedAt: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Connection test failed:', error);
    } finally {
      setTesting(false);
    }
  };

  const handleDeleteConnection = async () => {
    if (confirm('Are you sure you want to delete this connection? This action cannot be undone.')) {
      try {
        // Mock API call to delete connection
        await new Promise(resolve => setTimeout(resolve, 1000));
        navigate('/integrations/connections');
      } catch (error) {
        console.error('Error deleting connection:', error);
      }
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'salesforce':
        return <Building className="w-6 h-6 text-blue-600" />;
      case 'zoho':
        return <Globe className="w-6 h-6 text-orange-600" />;
      case 'hubspot':
        return <Zap className="w-6 h-6 text-orange-400" />;
      case 'stripe':
        return <CreditCard className="w-6 h-6 text-purple-600" />;
      case 'mailchimp':
        return <Mail className="w-6 h-6 text-yellow-600" />;
      default:
        return <Database className="w-6 h-6 text-gray-600" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'inactive':
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'inactive':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!connection) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center">
          <XCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Connection Not Found</h2>
          <p className="text-gray-600 mb-4">The connection you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/integrations/connections')}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Connections
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/integrations/connections')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div className="flex items-center space-x-3">
            {getPlatformIcon(connection.platform)}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{connection.name}</h1>
              <p className="text-gray-600 mt-2">Connection details and configuration</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleTestConnection}
            disabled={testing}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {testing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Testing...
              </>
            ) : (
              <>
                <TestTube className="w-4 h-4 mr-2" />
                Test Connection
              </>
            )}
          </button>
          
          <button
            onClick={() => navigate(`/integrations/connections/${id}/edit`)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </button>
          
          <button
            onClick={handleDeleteConnection}
            className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </button>
        </div>
      </div>

      {/* Status Banner */}
      <div className={`mb-6 p-4 rounded-lg border ${getStatusColor(connection.status)}`}>
        <div className="flex items-center">
          {getStatusIcon(connection.status)}
          <div className="ml-3">
            <p className="text-sm font-medium">
              Status: {connection.status.charAt(0).toUpperCase() + connection.status.slice(1)}
            </p>
            <p className="text-sm opacity-75">
              Last updated: {new Date(connection.updatedAt).toLocaleString()}
            </p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: Activity },
            { id: 'configuration', label: 'Configuration', icon: Settings },
            { id: 'sync', label: 'Sync History', icon: RefreshCw }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow">
        {activeTab === 'overview' && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Connection Information</h3>
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Platform</dt>
                    <dd className="text-sm text-gray-900 flex items-center mt-1">
                      {getPlatformIcon(connection.platform)}
                      <span className="ml-2 capitalize">{connection.platform}</span>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Authentication Type</dt>
                    <dd className="text-sm text-gray-900 mt-1">{connection.authType}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                    <dd className="text-sm text-gray-900 mt-1">
                      {new Date(connection.createdAt).toLocaleDateString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                    <dd className="text-sm text-gray-900 mt-1">
                      {new Date(connection.updatedAt).toLocaleDateString()}
                    </dd>
                  </div>
                </dl>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Sync Status</h3>
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Sync</dt>
                    <dd className="text-sm text-gray-900 mt-1">
                      {connection.lastSync 
                        ? new Date(connection.lastSync).toLocaleString()
                        : 'Never'
                      }
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Sync Status</dt>
                    <dd className="text-sm text-gray-900 mt-1 flex items-center">
                      {connection.syncStatus === 'success' && <CheckCircle className="w-4 h-4 text-green-600 mr-2" />}
                      {connection.syncStatus === 'error' && <XCircle className="w-4 h-4 text-red-600 mr-2" />}
                      {connection.syncStatus === 'running' && <RefreshCw className="w-4 h-4 text-blue-600 mr-2 animate-spin" />}
                      {connection.syncStatus ? connection.syncStatus.charAt(0).toUpperCase() + connection.syncStatus.slice(1) : 'Unknown'}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'configuration' && (
          <div className="p-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Credentials</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-3">
                    {Object.entries(connection.credentials).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700 capitalize">
                          {key.replace(/_/g, ' ')}:
                        </span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-900 font-mono">
                            {key.includes('secret') || key.includes('key') ? 
                              (showCredentials ? value : '••••••••••••••••••••••••••••••••') : 
                              value
                            }
                          </span>
                          <button
                            onClick={() => copyToClipboard(value)}
                            className="p-1 hover:bg-gray-200 rounded"
                          >
                            <Copy className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                  <button
                    onClick={() => setShowCredentials(!showCredentials)}
                    className="mt-3 inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                  >
                    {showCredentials ? (
                      <>
                        <EyeOff className="w-4 h-4 mr-1" />
                        Hide Credentials
                      </>
                    ) : (
                      <>
                        <Eye className="w-4 h-4 mr-1" />
                        Show Credentials
                      </>
                    )}
                  </button>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <dl className="space-y-3">
                    {Object.entries(connection.connectionConfig).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700 capitalize">
                          {key.replace(/_/g, ' ')}:
                        </span>
                        <span className="text-sm text-gray-900">{value}</span>
                      </div>
                    ))}
                  </dl>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'sync' && (
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Sync History</h3>
              <button className="inline-flex items-center px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                <RefreshCw className="w-4 h-4 mr-1" />
                Sync Now
              </button>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600 text-center">
                Sync history will be displayed here. No syncs have been performed yet.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConnectionDetail; 
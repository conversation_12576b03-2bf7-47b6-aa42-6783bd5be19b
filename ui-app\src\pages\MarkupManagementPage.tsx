import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Filter,
  Settings,
  DollarSign,
  Percent,
  Target,
  Users,
  Package,
  Tag,
  Building2,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Calculator,
  TrendingUp,
  AlertCircle,
  Check,
  X
} from 'lucide-react';
import StatCard from '../components/StatCard';
import SectionHeader from '../components/SectionHeader';
import { markupApi, MarkupRuleListResponse, SupplierMarkupOverride, CreateMarkupRuleRequest, CreateMarkupTargetRequest } from '../services/markupApi';

const MarkupManagementPage = () => {
  const [activeTab, setActiveTab] = useState<'rules' | 'overrides' | 'preview'>('rules');
  const [markupRules, setMarkupRules] = useState<MarkupRuleListResponse[]>([]);
  const [supplierOverrides, setSupplierOverrides] = useState<SupplierMarkupOverride[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  
  // Modal states
  const [showAddRuleModal, setShowAddRuleModal] = useState(false);
  const [showAddOverrideModal, setShowAddOverrideModal] = useState(false);
  
  // Form states for Add Rule
  const [ruleForm, setRuleForm] = useState({
    name: '',
    description: '',
    markup_type: 'percentage' as 'percentage' | 'fixed',
    markup_value: 0,
    priority: 0,
    targets: [] as CreateMarkupTargetRequest[]
  });

  // Form states for Add Override
  const [overrideForm, setOverrideForm] = useState({
    supplier_id: '',
    product_id: '',
    markup_type: 'percentage' as 'percentage' | 'fixed',
    markup_value: 0
  });

  // Target selection states
  const [selectedTargetType, setSelectedTargetType] = useState<'product' | 'category' | 'brand' | 'supplier'>('product');
  const [selectedTargetId, setSelectedTargetId] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      if (activeTab === 'rules') {
        const response = await markupApi.getMarkupRules();
        if (response.success && response.data) {
          setMarkupRules(response.data);
        }
      } else if (activeTab === 'overrides') {
        const response = await markupApi.getSupplierMarkupOverrides();
        if (response.success && response.data) {
          setSupplierOverrides(response.data);
        }
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddRule = async () => {
    if (!ruleForm.name || ruleForm.markup_value <= 0 || ruleForm.targets.length === 0) {
      alert('Please fill in all required fields and add at least one target.');
      return;
    }

    try {
      const request: CreateMarkupRuleRequest = {
        name: ruleForm.name,
        description: ruleForm.description || undefined,
        markup_type: ruleForm.markup_type,
        markup_value: ruleForm.markup_value,
        priority: ruleForm.priority,
        targets: ruleForm.targets
      };

      const response = await markupApi.createMarkupRule(request);
      if (response.success) {
        setShowAddRuleModal(false);
        resetRuleForm();
        loadData();
      } else {
        alert('Failed to create rule: ' + response.error);
      }
    } catch (error) {
      console.error('Error creating rule:', error);
      alert('Failed to create rule');
    }
  };

  const handleAddOverride = async () => {
    if (!overrideForm.supplier_id || !overrideForm.product_id || overrideForm.markup_value <= 0) {
      alert('Please fill in all required fields.');
      return;
    }

    try {
      const response = await markupApi.createSupplierMarkupOverride(overrideForm);
      if (response.success) {
        setShowAddOverrideModal(false);
        resetOverrideForm();
        loadData();
      } else {
        alert('Failed to create override: ' + response.error);
      }
    } catch (error) {
      console.error('Error creating override:', error);
      alert('Failed to create override');
    }
  };

  const addTarget = () => {
    if (!selectedTargetId) {
      alert('Please select a target.');
      return;
    }

    const newTarget: CreateMarkupTargetRequest = {
      target_type: selectedTargetType,
      target_id: selectedTargetId
    };

    setRuleForm(prev => ({
      ...prev,
      targets: [...prev.targets, newTarget]
    }));

    setSelectedTargetId('');
  };

  const removeTarget = (index: number) => {
    setRuleForm(prev => ({
      ...prev,
      targets: prev.targets.filter((_, i) => i !== index)
    }));
  };

  const resetRuleForm = () => {
    setRuleForm({
      name: '',
      description: '',
      markup_type: 'percentage',
      markup_value: 0,
      priority: 0,
      targets: []
    });
  };

  const resetOverrideForm = () => {
    setOverrideForm({
      supplier_id: '',
      product_id: '',
      markup_type: 'percentage',
      markup_value: 0
    });
  };

  // Mock data for dropdowns
  const mockProducts = [
    { id: 'product_1', name: 'Wireless Headphones' },
    { id: 'product_2', name: 'Smart Watch' },
    { id: 'product_3', name: 'Laptop' }
  ];

  const mockCategories = [
    { id: 'cat_1', name: 'Electronics' },
    { id: 'cat_2', name: 'Clothing' },
    { id: 'cat_3', name: 'Home & Garden' }
  ];

  const mockBrands = [
    { id: 'brand_1', name: 'TechCorp' },
    { id: 'brand_2', name: 'AudioTech' },
    { id: 'brand_3', name: 'FitTech' }
  ];

  const mockSuppliers = [
    { id: 'supplier_1', name: 'TechCorp Solutions' },
    { id: 'supplier_2', name: 'AudioTech Pro' },
    { id: 'supplier_3', name: 'HealthTech Inc' }
  ];

  const getTargetOptions = () => {
    switch (selectedTargetType) {
      case 'product': return mockProducts;
      case 'category': return mockCategories;
      case 'brand': return mockBrands;
      case 'supplier': return mockSuppliers;
      default: return [];
    }
  };

  const getTargetName = (target: CreateMarkupTargetRequest) => {
    const options = getTargetOptions();
    const option = options.find(opt => opt.id === target.target_id);
    return option?.name || target.target_id;
  };

  const getMarkupTypeIcon = (type: string) => {
    return type === 'percentage' ? <Percent className="h-4 w-4" /> : <DollarSign className="h-4 w-4" />;
  };

  const getMarkupTypeLabel = (type: string) => {
    return type === 'percentage' ? 'Percentage' : 'Fixed Amount';
  };

  const getTargetTypeIcon = (type: string) => {
    switch (type) {
      case 'product': return <Package className="h-4 w-4" />;
      case 'category': return <Tag className="h-4 w-4" />;
      case 'brand': return <Building2 className="h-4 w-4" />;
      case 'supplier': return <Users className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <Check className="h-3 w-3 mr-1" />
        Active
      </span>
    ) : (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        <X className="h-3 w-3 mr-1" />
        Inactive
      </span>
    );
  };

  const filteredRules = markupRules.filter(rule => {
    const matchesSearch = rule.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || 
      (selectedFilter === 'active' && rule.is_active) ||
      (selectedFilter === 'inactive' && !rule.is_active);
    return matchesSearch && matchesFilter;
  });

  const filteredOverrides = supplierOverrides.filter(override => {
    const matchesSearch = (override.product_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (override.supplier_name || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || 
      (selectedFilter === 'active' && override.is_active) ||
      (selectedFilter === 'inactive' && !override.is_active);
    return matchesSearch && matchesFilter;
  });

  const stats = [
    {
      title: 'Total Rules',
      value: markupRules.length.toString(),
      icon: Settings,
      iconColor: 'text-blue-600'
    },
    {
      title: 'Active Rules',
      value: markupRules.filter(r => r.is_active).length.toString(),
      icon: Check,
      iconColor: 'text-green-600'
    },
    {
      title: 'Supplier Overrides',
      value: supplierOverrides.length.toString(),
      icon: Users,
      iconColor: 'text-purple-600'
    },
    {
      title: 'Avg Markup',
      value: '12.5%',
      icon: TrendingUp,
      iconColor: 'text-orange-600'
    }
  ];

  return (
    <div className="p-6 space-y-6">
      <SectionHeader
        title="PIM Markup Management"
        subtitle="Configure pricing markups for products, categories, brands, and suppliers"
        icon={<Calculator className="h-8 w-8" />}
      />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            iconColor={stat.iconColor}
          />
        ))}
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { id: 'rules', label: 'Markup Rules', icon: Settings },
              { id: 'overrides', label: 'Supplier Overrides', icon: Users },
              { id: 'preview', label: 'Price Preview', icon: Eye }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
              <button
                onClick={loadData}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
              <button 
                onClick={() => activeTab === 'rules' ? setShowAddRuleModal(true) : setShowAddOverrideModal(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add {activeTab === 'rules' ? 'Rule' : 'Override'}</span>
              </button>
            </div>
          </div>

          {/* Content based on active tab */}
          {activeTab === 'rules' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rule
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Value
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Targets
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRules.map((rule) => (
                    <tr key={rule.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{rule.name}</div>
                          <div className="text-sm text-gray-500">Created {new Date(rule.created_at).toLocaleDateString()}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          {getMarkupTypeIcon(rule.markup_type)}
                          <span className="text-sm text-gray-900">{getMarkupTypeLabel(rule.markup_type)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {rule.markup_type === 'percentage' ? `${rule.markup_value}%` : `$${rule.markup_value}`}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{rule.priority}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{rule.target_count} targets</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(rule.is_active)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <Eye className="h-4 w-4" />
                          </button>
                          <button className="text-blue-600 hover:text-blue-900">
                            <Edit className="h-4 w-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {activeTab === 'overrides' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Supplier
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Value
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredOverrides.map((override) => (
                    <tr key={override.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{override.supplier_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{override.product_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          {getMarkupTypeIcon(override.markup_type)}
                          <span className="text-sm text-gray-900">{getMarkupTypeLabel(override.markup_type)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {override.markup_type === 'percentage' ? `${override.markup_value}%` : `$${override.markup_value}`}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(override.is_active)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button className="text-gray-400 hover:text-gray-600">
                            <Eye className="h-4 w-4" />
                          </button>
                          <button className="text-blue-600 hover:text-blue-900">
                            <Edit className="h-4 w-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {activeTab === 'preview' && (
            <div className="text-center py-12">
              <Calculator className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Price Preview</h3>
              <p className="text-gray-500 mb-6">
                Preview how markup rules will affect product prices for specific suppliers
              </p>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Launch Price Preview
              </button>
            </div>
          )}

          {loading && (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 text-gray-400 mx-auto animate-spin" />
              <p className="text-gray-500 mt-2">Loading...</p>
            </div>
          )}
        </div>
      </div>

      {/* Add Rule Modal */}
      {showAddRuleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Add Markup Rule</h2>
              <button
                onClick={() => {
                  setShowAddRuleModal(false);
                  resetRuleForm();
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Rule Name *</label>
                <input
                  type="text"
                  value={ruleForm.name}
                  onChange={(e) => setRuleForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter rule name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  value={ruleForm.description}
                  onChange={(e) => setRuleForm(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter description"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Markup Type *</label>
                  <select
                    value={ruleForm.markup_type}
                    onChange={(e) => setRuleForm(prev => ({ ...prev, markup_type: e.target.value as 'percentage' | 'fixed' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="percentage">Percentage</option>
                    <option value="fixed">Fixed Amount</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Markup Value * ({ruleForm.markup_type === 'percentage' ? '%' : '$'})
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={ruleForm.markup_value}
                    onChange={(e) => setRuleForm(prev => ({ ...prev, markup_value: parseFloat(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder={ruleForm.markup_type === 'percentage' ? '15.0' : '5.00'}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                <input
                  type="number"
                  value={ruleForm.priority}
                  onChange={(e) => setRuleForm(prev => ({ ...prev, priority: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Targets *</label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <select
                      value={selectedTargetType}
                      onChange={(e) => setSelectedTargetType(e.target.value as any)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="product">Product</option>
                      <option value="category">Category</option>
                      <option value="brand">Brand</option>
                      <option value="supplier">Supplier</option>
                    </select>
                    <select
                      value={selectedTargetId}
                      onChange={(e) => setSelectedTargetId(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select {selectedTargetType}</option>
                      {getTargetOptions().map(option => (
                        <option key={option.id} value={option.id}>{option.name}</option>
                      ))}
                    </select>
                    <button
                      onClick={addTarget}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Add
                    </button>
                  </div>

                  {ruleForm.targets.length > 0 && (
                    <div className="space-y-1">
                      {ruleForm.targets.map((target, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                          <span className="text-sm">
                            {target.target_type}: {getTargetName(target)}
                          </span>
                          <button
                            onClick={() => removeTarget(index)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddRuleModal(false);
                  resetRuleForm();
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleAddRule}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Create Rule
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Override Modal */}
      {showAddOverrideModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Add Supplier Override</h2>
              <button
                onClick={() => {
                  setShowAddOverrideModal(false);
                  resetOverrideForm();
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Supplier *</label>
                <select
                  value={overrideForm.supplier_id}
                  onChange={(e) => setOverrideForm(prev => ({ ...prev, supplier_id: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Supplier</option>
                  {mockSuppliers.map(supplier => (
                    <option key={supplier.id} value={supplier.id}>{supplier.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Product *</label>
                <select
                  value={overrideForm.product_id}
                  onChange={(e) => setOverrideForm(prev => ({ ...prev, product_id: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Product</option>
                  {mockProducts.map(product => (
                    <option key={product.id} value={product.id}>{product.name}</option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Markup Type *</label>
                  <select
                    value={overrideForm.markup_type}
                    onChange={(e) => setOverrideForm(prev => ({ ...prev, markup_type: e.target.value as 'percentage' | 'fixed' }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="percentage">Percentage</option>
                    <option value="fixed">Fixed Amount</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Markup Value * ({overrideForm.markup_type === 'percentage' ? '%' : '$'})
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={overrideForm.markup_value}
                    onChange={(e) => setOverrideForm(prev => ({ ...prev, markup_value: parseFloat(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder={overrideForm.markup_type === 'percentage' ? '15.0' : '5.00'}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddOverrideModal(false);
                  resetOverrideForm();
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleAddOverride}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Create Override
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarkupManagementPage; 
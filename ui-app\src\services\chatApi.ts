import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

const chatApi = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  pageContext?: string;
}

export interface ChatConversation {
  id: string;
  name: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  pageContext?: string;
}

export interface SendMessageRequest {
  message: string;
  conversationId: string;
  pageContext?: string;
  conversationHistory?: ChatMessage[];
}

export interface SendMessageResponse {
  response: string;
  conversationId: string;
  messageId: string;
}

export interface CreateConversationRequest {
  name?: string;
  pageContext?: string;
}

export interface UpdateConversationRequest {
  name: string;
}

export const chatApiService = {
  // Send a message and get AI response
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      const response = await chatApi.post('/chat', request);
      return response.data.data;
    } catch (error) {
      console.error('Failed to send message:', error);
      throw new Error('Failed to send message');
    }
  },

  // Get all conversations for the current user
  async getConversations(): Promise<ChatConversation[]> {
    try {
      const response = await chatApi.get('/conversations');
      return response.data.data;
    } catch (error) {
      console.error('Failed to get conversations:', error);
      throw new Error('Failed to get conversations');
    }
  },

  // Get a specific conversation
  async getConversation(conversationId: string): Promise<ChatConversation | null> {
    try {
      const response = await chatApi.get(`/conversations/${conversationId}`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to get conversation:', error);
      return null;
    }
  },

  // Create a new conversation
  async createConversation(request: CreateConversationRequest): Promise<ChatConversation> {
    try {
      const response = await chatApi.post('/conversations', request);
      return response.data.data;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      throw new Error('Failed to create conversation');
    }
  },

  // Update a conversation (rename)
  async updateConversation(conversationId: string, request: UpdateConversationRequest): Promise<ChatConversation> {
    try {
      const response = await chatApi.put(`/conversations/${conversationId}`, request);
      return response.data.data;
    } catch (error) {
      console.error('Failed to update conversation:', error);
      throw new Error('Failed to update conversation');
    }
  },

  // Delete a conversation
  async deleteConversation(conversationId: string): Promise<boolean> {
    try {
      await chatApi.delete(`/conversations/${conversationId}`);
      return true;
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      throw new Error('Failed to delete conversation');
    }
  },

  // Get messages for a conversation
  async getMessages(conversationId: string): Promise<ChatMessage[]> {
    try {
      const response = await chatApi.get(`/conversations/${conversationId}/messages`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to get messages:', error);
      throw new Error('Failed to get messages');
    }
  },
};

export default chatApiService; 
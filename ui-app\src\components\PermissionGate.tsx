import { ReactNode } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { createPermission<PERSON>he<PERSON> } from '../utils/permissions'

interface PermissionGateProps {
  children: ReactNode
  // Permission-based access
  resource?: string
  action?: string
  scope?: string
  // Role-based access
  roles?: string[]
  requireAllRoles?: boolean
  // Multiple permission checks
  permissions?: Array<{ resource: string; action: string; scope?: string }>
  requireAllPermissions?: boolean
  // Fallback content
  fallback?: ReactNode
  // Custom permission check function
  customCheck?: () => boolean
}

const PermissionGate = ({
  children,
  resource,
  action,
  scope,
  roles,
  requireAllRoles = false,
  permissions,
  requireAllPermissions = true,
  fallback = null,
  customCheck
}: PermissionGateProps) => {
  const { 
    user, 
    currentOrganization, 
    currentWorkspace, 
    currentRole, 
    permissions: userPermissions, 
    memberships,
    isAuthenticated 
  } = useAuth()

  // If not authenticated, deny access
  if (!isAuthenticated || !user) {
    return <>{fallback}</>
  }

  // Create permission checker
  const permissionChecker = createPermissionChecker({
    user,
    currentOrganization: currentOrganization!,
    currentWorkspace: currentWorkspace!,
    currentRole: currentRole!,
    availableOrganizations: [],
    availableWorkspaces: [],
    permissions: userPermissions,
    memberships,
    sessionId: '',
    loginAt: '',
    lastActivityAt: '',
    expiresAt: ''
  })

  if (!permissionChecker) {
    return <>{fallback}</>
  }

  // Custom check takes precedence
  if (customCheck) {
    return customCheck() ? <>{children}</> : <>{fallback}</>
  }

  // Role-based check
  if (roles && roles.length > 0) {
    const hasRequiredRoles = requireAllRoles
      ? roles.every(role => permissionChecker.hasRole(role))
      : roles.some(role => permissionChecker.hasRole(role))
    
    if (!hasRequiredRoles) {
      return <>{fallback}</>
    }
  }

  // Single permission check
  if (resource && action) {
    if (!permissionChecker.hasPermission(resource, action, scope)) {
      return <>{fallback}</>
    }
  }

  // Multiple permissions check
  if (permissions && permissions.length > 0) {
    const hasRequiredPermissions = requireAllPermissions
      ? permissions.every(perm => permissionChecker.hasPermission(perm.resource, perm.action, perm.scope))
      : permissions.some(perm => permissionChecker.hasPermission(perm.resource, perm.action, perm.scope))
    
    if (!hasRequiredPermissions) {
      return <>{fallback}</>
    }
  }

  // If all checks pass, render children
  return <>{children}</>
}

export default PermissionGate

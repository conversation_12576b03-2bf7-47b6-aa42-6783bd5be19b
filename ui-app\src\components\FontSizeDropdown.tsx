import React, { useState, useRef, useEffect } from 'react'
import { Search, Type, ZoomIn } from 'lucide-react'
import { useSettings, FontSize } from '../contexts/SettingsContext'

const FontSizeDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const { fontSize, setFontSize } = useSettings()
  const dropdownRef = useRef<HTMLDivElement>(null)

  const fontSizeOptions = [
    { value: 'small' as FontSize, label: 'Small', icon: Search },
    { value: 'medium' as FontSize, label: 'Medium', icon: Type },
    { value: 'large' as FontSize, label: 'Large', icon: ZoomIn }
  ]

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleFontSizeChange = (size: FontSize) => {
    setFontSize(size)
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="text-gray-600 hover:text-gray-800 transition-colors p-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label="Font size settings"
        aria-expanded={isOpen}
      >
        <span className="text-xl font-medium">T</span>
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-72 bg-white rounded-lg shadow-xl border border-gray-200 z-50 animate-in fade-in-0 zoom-in-95 duration-200">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Display Size</h3>

            <div className="space-y-3">
              {fontSizeOptions.map((option) => {
                const Icon = option.icon
                const isActive = fontSize === option.value

                return (
                  <button
                    key={option.value}
                    onClick={() => handleFontSizeChange(option.value)}
                    className={`w-full flex items-center justify-between p-4 rounded-lg border transition-colors duration-200 ${
                      isActive
                        ? 'border-gray-300 bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <Icon className="h-5 w-5 text-gray-600" />
                      <span className="text-gray-900 font-medium text-base">{option.label}</span>
                    </div>

                    {isActive && (
                      <span className="px-4 py-1 bg-gray-200 text-gray-700 text-sm rounded-full font-medium">
                        Active
                      </span>
                    )}
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FontSizeDropdown

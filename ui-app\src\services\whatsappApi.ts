// Mock WhatsApp API service - no real API calls
import { mockWhatsAppData, WhatsAppAccount, WhatsAppContact, WhatsAppConversation, WhatsAppMessage } from '../data/mockData'

// Simulate network delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

interface ApiResponse<T> {
  data?: T
  error?: string
  success: boolean
}

// Mock WhatsApp API service
export const whatsappApi = {
  // Account CRUD Operations
  async getAccounts(): Promise<ApiResponse<WhatsAppAccount[]>> {
    await delay()
    return { 
      success: true, 
      data: mockWhatsAppData.accounts 
    }
  },

  async createAccount(accountData: { name: string; phone: string }): Promise<ApiResponse<WhatsAppAccount>> {
    await delay()
    const newAccount: WhatsAppAccount = {
      id: `acc_${Date.now()}`,
      ...accountData,
      status: 'disconnected',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    mockWhatsAppData.accounts.push(newAccount)
    return { success: true, data: newAccount }
  },

  async updateAccount(accountId: string, accountData: Partial<{ name: string; phone: string; status: string }>): Promise<ApiResponse<WhatsAppAccount>> {
    await delay()
    const accountIndex = mockWhatsAppData.accounts.findIndex((acc: WhatsAppAccount) => acc.id === accountId)
    if (accountIndex === -1) {
      return { success: false, error: 'Account not found' }
    }
    
    mockWhatsAppData.accounts[accountIndex] = {
      ...mockWhatsAppData.accounts[accountIndex],
      ...accountData,
      status: accountData.status as 'connected' | 'disconnected' | 'connecting' || mockWhatsAppData.accounts[accountIndex].status,
      updated_at: new Date().toISOString()
    }
    
    return { success: true, data: mockWhatsAppData.accounts[accountIndex] }
  },

  async deleteAccount(accountId: string): Promise<ApiResponse<void>> {
    await delay()
    const accountIndex = mockWhatsAppData.accounts.findIndex((acc: WhatsAppAccount) => acc.id === accountId)
    if (accountIndex === -1) {
      return { success: false, error: 'Account not found' }
    }
    
    mockWhatsAppData.accounts.splice(accountIndex, 1)
    return { success: true }
  },

  // Conversation CRUD Operations
  async getConversations(accountId: string, options?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<WhatsAppConversation[]>> {
    await delay()
    let conversations = mockWhatsAppData.conversations.filter((conv: WhatsAppConversation) => conv.account_id === accountId)
    
    if (options?.search) {
      const search = options.search.toLowerCase()
      conversations = conversations.filter((conv: WhatsAppConversation) => 
        conv.contact_name.toLowerCase().includes(search) ||
        conv.contact_phone.includes(search)
      )
    }
    
    return { success: true, data: conversations }
  },

  async updateConversation(conversationId: string, updates: any): Promise<ApiResponse<WhatsAppConversation>> {
    await delay()
    const convIndex = mockWhatsAppData.conversations.findIndex((conv: WhatsAppConversation) => conv.id === conversationId)
    if (convIndex === -1) {
      return { success: false, error: 'Conversation not found' }
    }
    
    mockWhatsAppData.conversations[convIndex] = {
      ...mockWhatsAppData.conversations[convIndex],
      ...updates,
      updated_at: new Date().toISOString()
    }
    
    return { success: true, data: mockWhatsAppData.conversations[convIndex] }
  },

  async deleteConversation(conversationId: string): Promise<ApiResponse<void>> {
    await delay()
    const convIndex = mockWhatsAppData.conversations.findIndex((conv: WhatsAppConversation) => conv.id === conversationId)
    if (convIndex === -1) {
      return { success: false, error: 'Conversation not found' }
    }
    
    mockWhatsAppData.conversations.splice(convIndex, 1)
    return { success: true }
  },

  // Message CRUD Operations
  async getMessages(conversationId: string, options?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<WhatsAppMessage[]>> {
    await delay()
    let messages = mockWhatsAppData.messages.filter((msg: WhatsAppMessage) => msg.conversation_id === conversationId)
    
    if (options?.search) {
      const search = options.search.toLowerCase()
      messages = messages.filter((msg: WhatsAppMessage) => 
        msg.content.toLowerCase().includes(search)
      )
    }
    
    return { success: true, data: messages }
  },

  async sendMessage(accountId: string, messageData: { contactPhone: string; message: string; messageType?: string }): Promise<ApiResponse<WhatsAppMessage>> {
    await delay()
    const newMessage: WhatsAppMessage = {
      id: `msg_${Date.now()}`,
      conversation_id: `conv_${accountId}_${messageData.contactPhone}`,
      account_id: accountId,
      contact_phone: messageData.contactPhone,
      content: messageData.message,
      type: (messageData.messageType as 'text' | 'image' | 'video' | 'document' | 'audio' | 'contact') || 'text',
      direction: 'outbound',
      status: 'sent',
      timestamp: new Date().toISOString(),
      created_at: new Date().toISOString()
    }
    
    mockWhatsAppData.messages.push(newMessage)
    return { success: true, data: newMessage }
  },

  // Contact CRUD Operations
  async getContacts(accountId: string, options?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse<WhatsAppContact[]>> {
    await delay()
    let contacts = mockWhatsAppData.contacts.filter((contact: WhatsAppContact) => contact.account_id === accountId)
    
    if (options?.search) {
      const search = options.search.toLowerCase()
      contacts = contacts.filter((contact: WhatsAppContact) => 
        contact.name.toLowerCase().includes(search) ||
        contact.phone.includes(search)
      )
    }
    
    return { success: true, data: contacts }
  },

  // Session Management
  async startSession(accountId: string): Promise<ApiResponse<any>> {
    await delay(2000) // Simulate longer connection time
    const accountIndex = mockWhatsAppData.accounts.findIndex((acc: WhatsAppAccount) => acc.id === accountId)
    if (accountIndex === -1) {
      return { success: false, error: 'Account not found' }
    }
    
    mockWhatsAppData.accounts[accountIndex].status = 'connected'
    return { success: true, data: { qr_code: 'mock_qr_code_data', status: 'connected' } }
  },

  async stopSession(accountId: string): Promise<ApiResponse<any>> {
    await delay()
    const accountIndex = mockWhatsAppData.accounts.findIndex((acc: WhatsAppAccount) => acc.id === accountId)
    if (accountIndex === -1) {
      return { success: false, error: 'Account not found' }
    }
    
    mockWhatsAppData.accounts[accountIndex].status = 'disconnected'
    return { success: true, data: { status: 'disconnected' } }
  },

  // Broadcast Operations
  async sendBroadcast(broadcastData: { accountIds: string[]; contactIds: string[]; message: string; messageType?: string }): Promise<ApiResponse<any>> {
    await delay(3000) // Simulate longer broadcast time
    const broadcastId = `broadcast_${Date.now()}`
    
    // Simulate sending to multiple contacts
    const results = broadcastData.contactIds.map(contactId => ({
      contact_id: contactId,
      status: 'sent',
      message_id: `msg_${Date.now()}_${Math.random()}`
    }))
    
    return { 
      success: true, 
      data: { 
        broadcast_id: broadcastId, 
        results, 
        total_sent: results.length 
      } 
    }
  },

  // Archive Operations
  async getArchivedConversations(accountId: string): Promise<ApiResponse<WhatsAppConversation[]>> {
    await delay()
    const archivedConversations = mockWhatsAppData.conversations.filter(
      (conv: WhatsAppConversation) => conv.account_id === accountId && conv.is_archived
    )
    return { success: true, data: archivedConversations }
  },

  // Analytics
  async getAnalytics(accountId: string, dateRange?: { startDate: string; endDate: string }): Promise<ApiResponse<any>> {
    await delay()
    const analytics = {
      total_messages: Math.floor(Math.random() * 1000) + 100,
      total_conversations: Math.floor(Math.random() * 100) + 10,
      total_contacts: Math.floor(Math.random() * 500) + 50,
      response_rate: Math.random() * 100,
      average_response_time: Math.floor(Math.random() * 60) + 5,
      date_range: dateRange || { startDate: new Date().toISOString(), endDate: new Date().toISOString() }
    }
    
    return { success: true, data: analytics }
  }
}

import { useState, useRef, useEffect } from 'react'
import {
  User,
  Settings,
  LogOut,
  HelpCircle
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'

interface UserProfileDropdownProps {
  className?: string
}

const UserProfileDropdown = ({ className = '' }: UserProfileDropdownProps) => {
  const { user, logout, isLoading } = useAuth()
  const navigate = useNavigate()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = () => {
    logout()
    setIsOpen(false)
  }

  const handleNavigation = (path: string) => {
    navigate(path)
    setIsOpen(false)
  }

  if (isLoading || !user) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* User Avatar Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 hover:bg-gray-50 transition-colors duration-200 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label="User profile menu"
        aria-expanded={isOpen}
      >
        {/* Avatar */}
        <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden ring-2 ring-white shadow-sm">
          <img
            src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
            alt="User avatar"
            className="h-full w-full object-cover"
          />
        </div>

        {/* User Info */}
        <div className="text-left">
          <div className="text-sm font-medium text-gray-900">John Doe</div>
          <div className="text-xs text-gray-500">Administrator</div>
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-xl z-50 animate-in fade-in-0 zoom-in-95 duration-200">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="text-sm font-semibold text-gray-900">John Doe</div>
            <div className="text-xs text-gray-500"><EMAIL></div>
          </div>

          {/* Menu Items */}
          <div className="py-1">
            {/* Profile */}
            <button
              onClick={() => handleNavigation('/settings/users/my-organization/')}
              className="w-full flex items-center px-4 py-2 text-left hover:bg-gray-50 transition-colors duration-200"
            >
              <User className="h-4 w-4 text-gray-400 mr-3" />
              <div className="text-sm text-gray-900">Profile</div>
            </button>

            {/* Settings */}
            <button
              onClick={() => handleNavigation('/settings')}
              className="w-full flex items-center px-4 py-2 text-left hover:bg-gray-50 transition-colors duration-200"
            >
              <Settings className="h-4 w-4 text-gray-400 mr-3" />
              <div className="text-sm text-gray-900">Settings</div>
            </button>

            {/* Help & Support */}
            <button className="w-full flex items-center px-4 py-2 text-left hover:bg-gray-50 transition-colors duration-200">
              <HelpCircle className="h-4 w-4 text-gray-400 mr-3" />
              <div className="text-sm text-gray-900">Help & Support</div>
            </button>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-100"></div>

          {/* Logout */}
          <div className="py-1">
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-2 text-left hover:bg-gray-50 transition-colors duration-200"
            >
              <LogOut className="h-4 w-4 text-red-400 mr-3" />
              <div className="text-sm text-red-600">Logout</div>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserProfileDropdown

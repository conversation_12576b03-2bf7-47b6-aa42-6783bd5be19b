import { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate, useSearchParams } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  ArrowLeft, Package, User, CreditCard, Truck, MessageSquare, FileText,
  Printer, Download, Edit, Clock, RefreshCw, CheckCircle, XCircle, Check,
  Mail, Phone, MapPin, DollarSign, Plus, Eye, MoreHorizontal,
  Copy, ExternalLink, Upload, Calendar, Building, UserCheck,
  Clipboard, AlertCircle, TrendingUp, Archive, Settings
} from 'lucide-react'
import { mockOrderApi } from '../services/orderApi'
import { Order } from '../types/order'

const OrderDetailsPage = () => {
  const { id } = useParams<{ id: string }>()
  const [searchParams, setSearchParams] = useSearchParams()
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'summary')
  const navigate = useNavigate()

  // Detect if this is opened in a new window (popup)
  const isPopupWindow = window.opener !== null

  // Update URL when tab changes
  useEffect(() => {
    setSearchParams({ tab: activeTab })
  }, [activeTab, setSearchParams])

  // Fetch order details
  const { data: order, isLoading, error } = useQuery(
    ['order', id],
    () => mockOrderApi.getOrder(id!),
    {
      enabled: !!id,
      staleTime: 30000
    }
  )

  const tabs = [
    { id: 'summary', label: 'Summary' },
    { id: 'products', label: 'Products' },
    { id: 'fulfillment', label: 'Fulfillment' },
    { id: 'documents', label: 'Documents' },
    { id: 'history', label: 'History' }
  ]

  // Helper functions
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Pending': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
      'Processing': { bg: 'bg-blue-100', text: 'text-blue-800' },
      'Shipped': { bg: 'bg-purple-100', text: 'text-purple-800' },
      'Completed': { bg: 'bg-green-100', text: 'text-green-800' },
      'Cancelled': { bg: 'bg-red-100', text: 'text-red-800' },
      'Refunded': { bg: 'bg-gray-100', text: 'text-gray-800' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['Pending']

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${config.bg} ${config.text}`}>
        {status}
      </span>
    )
  }

  // Order progress steps
  const getOrderProgress = () => {
    const steps = [
      { id: 'placed', label: 'Order placed', time: '08:30', completed: true },
      { id: 'payment', label: 'Payment processed', time: '08:32', completed: true },
      { id: 'approved', label: 'Order approved', time: '09:15', completed: true },
      { id: 'fulfillment', label: 'Fulfillment', time: '10:30', completed: false, active: true },
      { id: 'shipped', label: 'Shipped', time: '', completed: false },
      { id: 'delivered', label: 'Delivered', time: '', completed: false },
      { id: 'confirmed', label: 'Confirmed', time: '', completed: false }
    ]
    return steps
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Order Not Found</h2>
          <p className="text-gray-600">The order you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/orders')}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
              </button>
              <div>
                <div className="flex items-center space-x-3">
                  <h1 className="text-xl font-semibold text-gray-900">Order {order.id}</h1>
                  {getStatusBadge(order.status)}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Placed on {formatDate(order.date_created)} by Acme Corporation
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center text-sm text-gray-600">
                <UserCheck className="h-4 w-4 mr-1" />
                Assigned to: Emily Chen
              </div>
              <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                <Printer className="h-4 w-4 mr-2" />
                Print
              </button>
              <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
              <button className="flex items-center px-3 py-2 text-sm text-white bg-gray-900 rounded-md hover:bg-gray-800">
                <Edit className="h-4 w-4 mr-2" />
                Edit Order
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Order Progress */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-gray-900" />
              <h3 className="text-lg font-medium text-gray-900">Order Progress</h3>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Assigned to: Emily Chen</span>
            </div>
          </div>

          <div className="relative">
            {/* Progress Line Background */}
            <div className="absolute top-4 left-0 right-0 h-1 bg-gray-200 rounded-full mx-4"></div>
            {/* Progress Line Active */}
            <div className="absolute top-4 left-0 h-1 bg-gray-900 rounded-full mx-4" style={{ width: '50%' }}></div>

            <div className="relative flex items-start justify-between">
              {getOrderProgress().map((step, index) => (
                <div key={step.id} className="flex flex-col items-center relative z-10">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border-2 ${
                    step.completed
                      ? 'bg-gray-900 text-white border-gray-900'
                      : step.active
                        ? 'bg-yellow-400 text-gray-900 border-yellow-400 ring-4 ring-yellow-100'
                        : 'bg-white text-gray-400 border-gray-300'
                  }`}>
                    {step.completed ? <Check className="h-4 w-4" /> : step.active ? <Clock className="h-4 w-4" /> : <div className="w-2 h-2 bg-gray-400 rounded-full"></div>}
                  </div>
                  <div className="mt-3 text-center">
                    <div className={`text-sm font-medium ${step.completed || step.active ? 'text-gray-900' : 'text-gray-500'}`}>
                      {step.label}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">{step.time}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-gray-900 text-gray-900'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="px-6 py-6">
        {activeTab === 'summary' && renderSummaryTab()}
        {activeTab === 'products' && renderProductsTab()}
        {activeTab === 'fulfillment' && renderFulfillmentTab()}
        {activeTab === 'documents' && renderDocumentsTab()}
        {activeTab === 'history' && renderHistoryTab()}
      </div>
    </div>
  )

  // Tab content renderers
  function renderSummaryTab() {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Information */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-sm font-medium text-gray-900 mb-4">Order Information</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Order Number</span>
              <span className="text-sm font-medium text-gray-900">{order.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">PO Reference</span>
              <span className="text-sm font-medium text-gray-900">PO-76594</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Order Date</span>
              <span className="text-sm font-medium text-gray-900">{formatDate(order.date_created)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Expected Delivery</span>
              <span className="text-sm font-medium text-gray-900">May 28, 2025</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Quote Reference</span>
              <span className="text-sm font-medium text-gray-900">QT-4567</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Order Source</span>
              <span className="text-sm font-medium text-gray-900">B2B Portal</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Order Status</span>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">Processing</span>
                <button className="text-xs text-blue-600 hover:text-blue-800">Change Status</button>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Information */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-sm font-medium text-gray-900 mb-4">Customer Information</h3>
          <div className="space-y-4">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-sm font-medium text-gray-900">Acme Corporation</span>
                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  Enterprise
                </span>
              </div>
              <p className="text-sm text-gray-500">ID: CUST-1234</p>
              <p className="text-sm text-gray-500">Customer since Mar 15, 2020</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Contact Information</h4>
              <div className="space-y-1">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-900">John Smith</span>
                  <span className="text-sm text-gray-500 ml-2">Procurement Manager</span>
                </div>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-900"><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-900">+****************</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Contract Information</h4>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Contract Number:</span>
                  <span className="text-sm font-medium text-gray-900">CONTRACT-5678</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Contract Type:</span>
                  <span className="text-sm font-medium text-gray-900">Annual Volume Discount</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Valid Until:</span>
                  <span className="text-sm font-medium text-gray-900">Dec 31, 2025</span>
                </div>
              </div>
              <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">View</button>
            </div>
          </div>
        </div>

        {/* Payment Information */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-sm font-medium text-gray-900 mb-4">Payment Information</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-900">Total Amount</span>
                <span className="text-lg font-semibold text-gray-900">{formatPrice(order.total_inc_tax)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                  Paid
                </span>
                <span className="text-sm text-gray-500">{formatDate(order.date_created)}</span>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Details</h4>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Method:</span>
                  <span className="text-sm font-medium text-gray-900">Credit Card</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Transaction ID:</span>
                  <span className="text-sm font-medium text-gray-900">TXN-987654321</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Billing Address</h4>
              <div className="text-sm text-gray-900">
                <p>Acme Corporation</p>
                <p>123 Commerce Drive</p>
                <p>Chicago, IL 60601</p>
                <p>United States</p>
              </div>
            </div>
          </div>
        </div>
        </div>

        {/* Additional sections for summary tab */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Shipping Address */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-sm font-medium text-gray-900 mb-4">Shipping Address</h3>
            <div className="text-sm text-gray-900">
              <p className="font-medium">Acme Corporation - Warehouse</p>
              <p>456 Logistics Avenue</p>
              <p>Chicago, IL 60607</p>
              <p>United States</p>
              <button className="mt-3 flex items-center text-sm text-blue-600 hover:text-blue-800">
                <MapPin className="h-4 w-4 mr-1" />
                View on Map
              </button>
            </div>
          </div>

          {/* Sales Representative */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-sm font-medium text-gray-900 mb-4">Sales Representative</h3>
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-white">EC</span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Emily Chen</p>
                <p className="text-sm text-gray-500"><EMAIL></p>
                <p className="text-sm text-gray-500">+****************</p>
              </div>
            </div>
            <div className="mt-4 flex space-x-2">
              <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                <Mail className="h-4 w-4 mr-1" />
                Email
              </button>
              <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                <Phone className="h-4 w-4 mr-1" />
                Call
              </button>
            </div>
          </div>
        </div>

        {/* Order Notes */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-sm font-medium text-gray-900 mb-4">Order Notes</h3>
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900">Emily Chen</span>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">Internal</span>
                  <span className="text-xs text-gray-500">{formatDate(order.date_created)}</span>
                </div>
              </div>
              <p className="text-sm text-gray-700">Customer requested delivery to loading dock entrance</p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900">System</span>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">System</span>
                  <span className="text-xs text-gray-500">{formatDate(order.date_created)}</span>
                </div>
              </div>
              <p className="text-sm text-gray-700">Special volume discount applied per contract #CONTRACT-5678</p>
            </div>
            <div className="mt-4">
              <textarea
                placeholder="Add a note about this order..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
              />
              <div className="mt-2 flex justify-end">
                <button className="flex items-center px-4 py-2 text-sm text-white bg-gray-900 rounded-md hover:bg-gray-800">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Note
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  function renderProductsTab() {
    const mockProducts = [
      {
        id: 1,
        name: 'Business Laptop Pro X1',
        description: '15" Professional Laptop, Intel i7, 16GB RAM, 1TB SSD',
        sku: 'TECH-LAPTOP-PRO',
        quantity: 5,
        unitPrice: 1299.99,
        discount: 10,
        total: 5849.95,
        status: 'In Stock',
        statusColor: 'green'
      },
      {
        id: 2,
        name: 'USB-C Universal Docking Station',
        description: '12-in-1 USB-C Dock with Dual 4K HDMI',
        sku: 'ACC-DOCK-USBC',
        quantity: 5,
        unitPrice: 129.99,
        discount: 5,
        total: 649.95,
        status: 'Partial',
        statusColor: 'yellow'
      },
      {
        id: 3,
        name: 'Professional Office Suite',
        description: 'Enterprise Office Software (5-year license)',
        sku: 'SW-OFFICE-PRO',
        quantity: 5,
        unitPrice: 249.99,
        discount: 15,
        total: 1062.45,
        status: 'Digital',
        statusColor: 'blue'
      },
      {
        id: 4,
        name: 'Ergonomic Wireless Mouse',
        description: 'Professional Ergonomic Mouse with Programmable Buttons',
        sku: 'ACC-MOUSE-ERG',
        quantity: 5,
        unitPrice: 49.99,
        discount: 0,
        total: 249.95,
        status: 'In Stock',
        statusColor: 'green'
      }
    ]

    return (
      <div className="space-y-6">
        {/* Order Items */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Order Items</h3>
            <div className="flex space-x-3">
              <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                <Eye className="h-4 w-4 mr-2" />
                Check Availability
              </button>
              <button className="flex items-center px-3 py-2 text-sm text-white bg-gray-900 rounded-md hover:bg-gray-800">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </button>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {mockProducts.map((product) => (
                  <tr key={product.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                          <Package className="h-5 w-5 text-gray-400" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                          <div className="text-sm text-gray-500">{product.description}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-sm text-gray-900">{product.sku}</span>
                        <Copy className="h-4 w-4 text-gray-400 ml-2 cursor-pointer hover:text-gray-600" />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.quantity}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatPrice(product.unitPrice)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.discount}%</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{formatPrice(product.total)}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                        product.statusColor === 'green' ? 'bg-green-100 text-green-800' :
                        product.statusColor === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {product.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex justify-end space-x-6">
              <div className="text-sm">
                <span className="text-gray-500">Subtotal:</span>
                <span className="font-medium text-gray-900 ml-2">$8,212.30</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-500">Discount:</span>
                <span className="font-medium text-gray-900 ml-2">-$562.50</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-500">Tax:</span>
                <span className="font-medium text-gray-900 ml-2">$800.95</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-500">Total:</span>
                <span className="font-semibold text-gray-900 ml-2">{formatPrice(order.total_inc_tax)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Inventory Availability */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Inventory Availability</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ordered</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">East Coast</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">West Coast</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Central</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Available</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Business Laptop Pro X1</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TECH-LAPTOP-PRO</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                      3
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">23</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                      In Stock
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">USB-C Universal Docking Station</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">ACC-DOCK-USBC</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                      3
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">6</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                      0
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">9</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 text-orange-800">
                        Backordered
                      </span>
                      <span className="text-xs text-gray-500">Expected: May 26, 2025</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Supplier Information */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Supplier Information</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Part #</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lead Time</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Business Laptop Pro X1</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TECH-LAPTOP-PRO</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TechSupplier Inc.</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TS-LP-PRO-X1</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3-5 days</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                      In Stock
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">USB-C Universal Docking Station</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">ACC-DOCK-USBC</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TechSupplier Inc.</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TS-DOCK-U12</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1-2 days</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-100 text-orange-800">
                        On Order
                      </span>
                      <span className="text-xs text-gray-500">Arriving: May 26, 2025</span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Professional Office Suite</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">SW-OFFICE-PRO</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">SoftwareDistribution Co.</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">SD-OFF-PRO-5Y</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Immediate</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      Digital
                    </span>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Ergonomic Wireless Mouse</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">ACC-MOUSE-ERG</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">OfficeGear Distributors</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">OG-MS-ERG-PRO</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2-4 days</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                      In Stock
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    )
  }

  function renderFulfillmentTab() {
    return (
      <div className="space-y-6">
        {/* Fulfillment Summary */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Fulfillment Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <span className="text-sm text-gray-500">Shipment Status</span>
              <div className="mt-1 flex items-center space-x-2">
                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  Partial
                </span>
                <button className="text-sm text-blue-600 hover:text-blue-800">Update</button>
              </div>
            </div>
            <div>
              <span className="text-sm text-gray-500">Primary Warehouse</span>
              <div className="mt-1 text-sm font-medium text-gray-900">East Coast Fulfillment Center</div>
            </div>
            <div>
              <span className="text-sm text-gray-500">Expected Delivery</span>
              <div className="mt-1 text-sm font-medium text-gray-900">May 28, 2025</div>
            </div>
            <div>
              <span className="text-sm text-gray-500">Packing Slip</span>
              <div className="mt-1 flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">PS-98765</span>
                <button className="text-blue-600 hover:text-blue-800">
                  <Printer className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">Shipping Method</span>
              <span className="text-sm font-medium text-gray-900">Premium Courier</span>
            </div>
            <div className="flex items-center space-x-4 mt-2">
              <span className="text-sm text-gray-500">Carrier</span>
              <span className="text-sm font-medium text-gray-900">UPS</span>
            </div>
            <div className="flex items-center space-x-4 mt-2">
              <span className="text-sm text-gray-500">Tracking Information</span>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">1Z999AA1012345784</span>
                <button className="text-blue-600 hover:text-blue-800">
                  <Copy className="h-4 w-4" />
                </button>
                <button className="text-blue-600 hover:text-blue-800">
                  <ExternalLink className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Shipments */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Shipments</h3>
              <p className="text-sm text-gray-500">View all shipments associated with this order</p>
            </div>
            <div className="flex space-x-3">
              <button className="flex items-center px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                <XCircle className="h-4 w-4 mr-2" />
                Cancel Pending Shipments
              </button>
              <button className="flex items-center px-3 py-2 text-sm text-white bg-gray-900 rounded-md hover:bg-gray-800">
                <Plus className="h-4 w-4 mr-2" />
                Create New Shipment
              </button>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shipment ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warehouse</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tracking</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHIP-001</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">May 22, 2025</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-900">9</span>
                      <Package className="h-4 w-4 text-gray-400 ml-1" />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">East Coast Fulfillment Center</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-900">1Z999AA1012345...</span>
                      <ExternalLink className="h-4 w-4 text-blue-600 cursor-pointer hover:text-blue-800" />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">
                      Shipped
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button className="text-gray-400 hover:text-gray-600">
                      <MoreHorizontal className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">PENDING-001</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">May 25, 2025</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-900">6</span>
                      <Package className="h-4 w-4 text-gray-400 ml-1" />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Central Warehouse</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Not yet assigned</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      Processing
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button className="flex items-center px-3 py-1 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50">
                      <Settings className="h-4 w-4 mr-1" />
                      Process
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Related Purchase Orders */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Related Purchase Orders</h3>
            <p className="text-sm text-gray-500">Purchase orders created for this customer order</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Number</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expected Delivery</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">PO-87654</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TechSupplier Inc.</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4 items</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      Confirmed
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">May 26, 2025</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button className="flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800">
                      <ExternalLink className="h-4 w-4 mr-1" />
                      View PO
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    )
  }

  function renderDocumentsTab() {
    return (
      <div className="space-y-6">
        {/* Related Documents */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Related Documents</h3>
              <p className="text-sm text-gray-500">Contracts, purchase authorizations, and other documents</p>
            </div>
            <button className="flex items-center px-3 py-2 text-sm text-white bg-gray-900 rounded-md hover:bg-gray-800">
              <Upload className="h-4 w-4 mr-2" />
              Upload Document
            </button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uploaded By</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Sales Contract.pdf</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Contract</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">John Smith</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">May 20, 2025</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <button className="text-blue-600 hover:text-blue-800">
                        <Download className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Purchase Authorization.pdf</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Authorization</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Jane Doe</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">May 21, 2025</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <button className="text-blue-600 hover:text-blue-800">
                        <Download className="h-4 w-4" />
                      </button>
                      <button className="text-gray-400 hover:text-gray-600">
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Quotation Details and Approval Workflow */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quotation Details</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Quotation Reference</span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900">QT-4567</span>
                  <button className="text-blue-600 hover:text-blue-800">
                    <ExternalLink className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Special Pricing</span>
                <span className="text-sm font-medium text-gray-900">Volume Discount: 10%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Contract Pricing:</span>
                <span className="text-sm font-medium text-gray-900">Yes (CONTRACT-5678)</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Negotiated Terms:</span>
                <span className="text-sm font-medium text-gray-900">Net 30</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Approval Workflow</h3>
              <button className="text-blue-600 hover:text-blue-800">
                <Eye className="h-4 w-4" />
              </button>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Approval Status</span>
                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                  Approved
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-900">Customer Approval</span>
                      <span className="text-xs text-gray-500">May 21, 2025</span>
                    </div>
                    <p className="text-xs text-gray-500">John Smith (Procurement)</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-900">Financial Approval</span>
                      <span className="text-xs text-gray-500">May 21, 2025</span>
                    </div>
                    <p className="text-xs text-gray-500">Finance Department</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-900">Inventory Verification</span>
                      <span className="text-xs text-gray-500">May 22, 2025</span>
                    </div>
                    <p className="text-xs text-gray-500">Automated System</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  function renderHistoryTab() {
    const activityItems = [
      {
        id: 1,
        type: 'order_created',
        title: 'Order Created',
        description: 'Order placed through B2B portal',
        user: 'John Smith',
        timestamp: 'May 22, 2025, 08:30 AM',
        icon: Package,
        iconColor: 'text-blue-600'
      },
      {
        id: 2,
        type: 'payment_received',
        title: 'Payment Received',
        description: 'Credit card payment processed successfully',
        user: 'System',
        timestamp: 'May 22, 2025, 08:32 AM',
        icon: CreditCard,
        iconColor: 'text-green-600'
      },
      {
        id: 3,
        type: 'order_processed',
        title: 'Order Processed',
        description: 'Order verified and released to warehouse',
        user: 'Emily Chen',
        timestamp: 'May 22, 2025, 09:15 AM',
        icon: CheckCircle,
        iconColor: 'text-blue-600'
      },
      {
        id: 4,
        type: 'shipment_created',
        title: 'Partial Shipment Created',
        description: 'Partial shipment of 3 items created',
        user: 'Warehouse System',
        timestamp: 'May 22, 2025, 10:30 AM',
        icon: Truck,
        iconColor: 'text-orange-600'
      },
      {
        id: 5,
        type: 'tracking_generated',
        title: 'Tracking Number Generated',
        description: 'Tracking number 1Z999AA1012345784 assigned',
        user: 'Warehouse System',
        timestamp: 'May 22, 2025, 10:55 AM',
        icon: MapPin,
        iconColor: 'text-purple-600'
      },
      {
        id: 6,
        type: 'purchase_order_created',
        title: 'Purchase Order Created',
        description: 'PO-87654 created for backordered items',
        user: 'System',
        timestamp: 'May 22, 2025, 10:20 AM',
        icon: FileText,
        iconColor: 'text-gray-600'
      }
    ]

    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Activity Timeline</h3>
            <p className="text-sm text-gray-500">Complete history of actions and updates for this order</p>
          </div>
          <div className="p-6">
            <div className="flow-root">
              <ul className="-mb-8">
                {activityItems.map((item, itemIdx) => (
                  <li key={item.id}>
                    <div className="relative pb-8">
                      {itemIdx !== activityItems.length - 1 ? (
                        <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white bg-gray-100`}>
                            <item.icon className={`h-4 w-4 ${item.iconColor}`} aria-hidden="true" />
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{item.title}</p>
                            <p className="text-sm text-gray-500">{item.description}</p>
                            <p className="text-xs text-gray-400">By: {item.user}</p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500">
                            <time>{item.timestamp}</time>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default OrderDetailsPage

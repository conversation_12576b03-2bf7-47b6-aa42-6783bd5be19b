from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from datetime import datetime

# Create FastAPI instance
app = FastAPI(
    title="Hello World API",
    description="A simple FastAPI Hello World application",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint returning a simple hello world message"""
    return {
        "message": "Hello World!",
        "timestamp": datetime.now().isoformat(),
        "status": "success"
    }

# API prefix endpoints
@app.get("/api")
async def api_root():
    """API root endpoint"""
    return {
        "message": "Hello from API!",
        "version": "1.0.0",
        "endpoints": {
            "root": "/",
            "api": "/api",
            "hello": "/api/hello",
            "health": "/api/health"
        }
    }

@app.get("/api/hello")
async def hello():
    """Hello endpoint with more details"""
    return {
        "message": "Hello World from FastAPI!",
        "framework": "FastAPI",
        "python_version": "3.x",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/hello/{name}")
async def hello_name(name: str):
    """Personalized hello endpoint"""
    return {
        "message": f"Hello, {name}!",
        "name": name,
        "timestamp": datetime.now().isoformat()
    }

# Health check endpoint
@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "Hello World API"
    }

# Run the application
if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
from datetime import datetime

# Import API routes
from api.routes import products

# Import database
from core.db import create_tables, check_db_health

# Create FastAPI instance
app = FastAPI(
    title="PIM API",
    description="Product Information Management API with FastAPI, PostgreSQL, and SQLAlchemy",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Create database tables on startup
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    create_tables()
    yield
    # Shutdown (if needed)

app.router.lifespan_context = lifespan

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(products.router, prefix="/api", tags=["products"])

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint returning API information"""
    return {
        "message": "PIM API - Product Information Management",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "status": "success",
        "docs": "/docs"
    }

# Health check endpoint
@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    db_status = "connected" if check_db_health() else "disconnected"
    overall_status = "healthy" if db_status == "connected" else "unhealthy"

    return {
        "status": overall_status,
        "timestamp": datetime.now().isoformat(),
        "service": "PIM API",
        "database": db_status
    }

# Run the application
if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
